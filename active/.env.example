# LiveKit Configuration
LIVEKIT_URL=wss://first-project-2rpwr03w.livekit.cloud
LIVEKIT_API_KEY=your-livekit-api-key
LIVEKIT_API_SECRET=your-livekit-api-secret

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key

# Cartesia Configuration (for TTS)
CARTESIA_API_KEY=your-cartesia-api-key

# DeepGram Configuration (for STT)
DEEPGRAM_API_KEY=your-deepgram-api-key

# Dify Configuration
DIFY_API_URL=http://localhost:5001/v1/chat-messages
DIFY_API_KEY=your-dify-api-key

# N8n Configuration (if needed for direct integration)
N8N_API_URL=http://localhost:5678/api/v1
N8N_API_KEY=your-n8n-api-key

# Project Management Settings
PROJECT_WORKSPACE=/path/to/your/projects
DEFAULT_PROJECT_TEMPLATE=basic-template
