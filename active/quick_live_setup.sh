#!/bin/bash

# Quick Live Setup for Speechmatics Testing
# Fast setup to get live testing running immediately

echo "⚡ Quick Live Setup for Speechmatics Testing..."

# Check if we're in the right directory
if [ ! -f "quick_live_setup.sh" ]; then
    echo "❌ Please run this script from the active/ directory"
    exit 1
fi

# Create virtual environment if needed
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install only essential packages for live testing
echo "📥 Installing essential packages for live testing..."

# Core LiveKit and agents
pip install livekit-agents[codecs,openai,silero,elevenlabs] --quiet

# Speechmatics
pip install speechmatics-python --quiet

# WebSockets for Speechmatics
pip install websockets --quiet

# Audio processing
pip install numpy --quiet

# AI services
pip install openai elevenlabs --quiet

# Quick verification
echo "✅ Quick verification..."
python -c "import speechmatics; print('✅ Speechmatics ready')" 2>/dev/null || echo "❌ Speechmatics failed"
python -c "import livekit; print('✅ LiveKit ready')" 2>/dev/null || echo "❌ LiveKit failed"

echo ""
echo "🚀 READY FOR LIVE TESTING!"
echo ""
echo "🎯 To start live testing:"
echo "   python src/juno_live_speechmatics.py"
echo ""
echo "📢 Test phrases to try:"
echo "   English: 'Juno, hello how are you?'"
echo "   Arabic: 'جونو، مرحبا كيف حالك؟'"
echo "   Mixed: 'Juno, مرحبا how are you today?'"
echo ""
echo "🔍 Watch for:"
echo "   - [EN]/[AR] language markers"
echo "   - Improved Arabic recognition"
echo "   - Faster response times"
echo ""
echo "💡 Your working Whisper backup is safe in:"
echo "   archive/2025-06-04_2025-06-04-working-arabic-english-wake-word/"
