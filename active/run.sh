#!/bin/bash

# LiveKit Voice Agent Runner Script
# This script activates the virtual environment and runs the agent

echo "🎙️  Starting LiveKit Voice Agent..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Please run ./setup.sh first."
    exit 1
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create it with your API keys."
    exit 1
fi

# Test configuration first
echo "🔍 Testing configuration..."
python3 test_config.py

if [ $? -ne 0 ]; then
    echo "❌ Configuration test failed. Please fix the issues above."
    exit 1
fi

echo ""
echo "🚀 Starting the voice agent..."
echo "📱 A browser URL will appear - open it to test your agent"
echo "🎤 Make sure to allow microphone access in your browser"
echo ""
echo "Press Ctrl+C to stop the agent"
echo ""

# Run the agent
python3 agent.py dev
