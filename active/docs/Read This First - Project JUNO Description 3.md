NOTE WE ARE NOW USING WHISPER STT AND ELEVEN LABS TTS


***Project Description Prompt***

You are an expert AI systems architect tasked with creating a comprehensive, step-by-step implementation plan for "Juno" - a meeting assistant AI that uses LiveKit (voice layer) → Dify (intelligence layer) → N8N (automation layer) architecture.
CRITICAL RULES:
	1	Create a detailed task list with numbered steps
	2	Each step must be 100% functional and tested before proceeding to the next
	3	Build incrementally from ground up - no step depends on future functionality
	4	FUTURE-COMPATIBLE DESIGN: When implementing each step, design it to accommodate all planned future functionality without requiring rework of previous steps
	5	Each step should be testable independently
	6	Focus on creating a stable, extensible foundation before adding complexity
	7	The final goal is a meeting assistant that records, transcribes, answers questions, takes notes, and automates follow-ups
CORE BEHAVIOR REQUIREMENT: Juno operates in "passive listening mode" by default - continuously listening and recording but NEVER speaking unless explicitly called by name "<PERSON>". Once name is detected, <PERSON> can respond normally. After response, <PERSON> returns to passive listening mode until name is called again.
PROJECT SCOPE:
	•	Target: 3-day implementation timeline
	•	Final demo: AI assistant participates in real business meeting in passive mode, then demonstrates active capabilities when called by name during presentation
	•	Core functions: Passive continuous recording, wake-word detection ("<PERSON>"), intelligent Q&A with RAG, meeting summary generation, action item extraction, follow-up automation
ARCHITECTURE REQUIREMENTS:
	•	LiveKit: Voice input/output, wake-word detection, passive recording mode
	•	Dify: Conversation management when activated, RAG integration, meeting context understanding
	•	N8N: Continuous meeting data processing, transcription storage, automation workflows
Create a numbered task list where each step can be completed and verified before moving forward, but is designed to seamlessly support all future planned features without architectural changes. Include testing criteria for each step. Design each component to be extensible for the full feature set from the beginning, even if those features aren't implemented until later steps.

***Arabic Addition Prompt***

**Prompt for Claude Sonnet:**

You are implementing Step 2 of the Juno meeting assistant: Arabic-English code-switching detection. This builds on the existing LiveKit-Dify-N8N architecture from Step 1.

**OBJECTIVE:**
Modify the existing LiveKit voice agent to detect and handle Arabic-English language switching within the same conversation, sentence, or even phrase. Users frequently switch between English and Arabic mid-sentence in business meetings.

**TECHNICAL REQUIREMENTS:**
1. Replace Deepgram STT with OpenAI Whisper STT in the LiveKit agent configuration
2. Configure Whisper for automatic language detection between English ("en") and Arabic ("ar") only
3. Enable Whisper to return language labels and timestamps for each detected language segment
4. Ensure the transcription output includes language markers for each segment
5. Update Dify agent to process mixed-language input with language markers
6. Maintain all existing functionality from Step 1 while adding language detection

**EXPECTED OUTPUT FORMAT:**
Transcripts should include language markers like: "We need to finish the [EN]budget report[/EN] [AR]قبل نهاية الأسبوع[/AR] [EN]by Friday[/EN]"

**TESTING CRITERIA:**
- Test with pure English speech
- Test with pure Arabic speech  
- Test with English-Arabic code-switching within same sentence
- Verify all Step 1 functionality still works
- Confirm Dify processes mixed-language input correctly

Create implementation steps that modify only the STT component without breaking existing LiveKit-Dify-N8N integration. Focus on seamless language detection while preserving wake-word detection and passive listening mode.

***What we want to achieve***

**During Meeting Features:**
1. **Smart attendance tracking** - Automatically identifies who joins/leaves and tracks speaking time per person
2. **Key decision detection** - Flags when someone says "we've decided" or "let's move forward with" and highlights these moments
3. **Follow-up capture** - Detects phrases like "I'll send that" or "let me get back to you" and creates automatic reminders

**After Meeting Features:**
4. **Instant meeting digest** - Generates 3-sentence summary within 30 seconds of meeting end
5. **Action item extraction with owners** - Not just tasks, but automatically assigns them to whoever said "I'll handle that"
6. **Next meeting suggestions** - Based on unfinished agenda items, suggests follow-up meeting topics and timing

**Show-off Features:**
7. **Meeting health score** - Rates meeting effectiveness (participation balance, decision velocity, agenda adherence)
8. **Quote extraction** - Captures and formats important statements for easy sharing ("As Sarah mentioned: 'This could increase revenue by 40%'")

These are simple but impressive because they show Juno understanding context and relationships, not just transcribing words. Each creates immediate business value and demonstrates AI intelligence beyond basic recording.

NOTE WE ARE NOW USING WHISPER STT AND ELEVEN LABS TTS