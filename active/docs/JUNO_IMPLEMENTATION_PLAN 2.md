# Juno Meeting Assistant - Comprehensive Implementation Plan

## 🎯 **Project Overview**
Building "Juno" - a passive meeting assistant that continuously records, processes, and automates meeting workflows using LiveKit → Dify → N8N architecture.

**CORE BEHAVIOR:** <PERSON> operates in "passive listening mode" by default - continuously listening and recording but NEVER speaking unless explicitly called by name "<PERSON>". Once name is detected, <PERSON> can respond normally. After response, <PERSON> returns to passive listening mode until name is called again.

## 📋 **Implementation Roadmap (3-Day Timeline)**

### **DAY 1: Foundation & Core Infrastructure**

#### **Step 1: Core LiveKit Audio Foundation** ⏱️ 2-3 hours
**Goal:** Establish continuous audio capture with future wake-word capability

**Implementation Tasks:**
- Create `juno_audio_core.py` with continuous audio streaming
- Implement audio buffer management for future wake-word detection
- Design audio pipeline to support both passive recording and active conversation modes
- Set up audio quality monitoring and connection stability

**Future-Compatible Design Elements:**
```python
class JunoAudioCore:
    def __init__(self):
        self.passive_mode = True
        self.wake_word_detector = None  # Future implementation
        self.conversation_mode = False
        self.audio_buffer = CircularBuffer(size=30)  # 30-second rolling buffer

    async def continuous_listen(self):
        # Designed to handle both passive and active modes
        pass

    async def activate_conversation_mode(self):
        # Future: Switch from passive to active
        pass
```

**Testing Criteria:**
- [ ] Audio streams continuously for 10+ minutes without drops
- [ ] Audio buffer maintains rolling 30-second history
- [ ] Connection recovery works after network interruption
- [ ] Audio quality metrics show consistent levels

**Files to Create:**
- `juno_audio_core.py` - Main audio processing class
- `audio_buffer.py` - Circular buffer implementation
- `test_audio_core.py` - Testing suite
- `requirements_step1.txt` - Dependencies for this step

---

#### **Step 2A: Arabic-English Language Detection** ⏱️ 2-3 hours
**Goal:** Implement seamless Arabic-English code-switching detection in voice conversations

**Implementation Tasks:**
- Replace Deepgram STT with OpenAI Whisper STT in LiveKit agent
- Configure Whisper for automatic language detection (English + Arabic only)
- Enable language labels and timestamps for each detected segment
- Add language markers to transcription output format
- Update Dify agent to process mixed-language input with language markers
- Preserve all existing Step 1 functionality (wake-word, passive listening)

**Expected Output Format:**
```
"We need to finish the [EN]budget report[/EN] [AR]قبل نهاية الأسبوع[/AR] [EN]by Friday[/EN]"
```

**Future-Compatible Design:**
```python
class WhisperSTTWithLanguageDetection:
    def __init__(self):
        self.supported_languages = ["en", "ar"]
        self.language_markers = {"en": "[EN]", "ar": "[AR]"}

    async def transcribe_with_language_detection(self, audio_chunk):
        # Process audio with Whisper for language detection
        segments = await self.whisper_transcribe(audio_chunk)

        # Add language markers to each segment
        marked_transcript = ""
        for segment in segments:
            lang = segment.language
            text = segment.text
            marked_transcript += f"{self.language_markers[lang]}{text}[/{lang.upper()}] "

        return marked_transcript.strip()
```

**Testing Criteria:**
- [ ] Pure English speech recognition works correctly
- [ ] Pure Arabic speech recognition works correctly
- [ ] English-Arabic code-switching within same sentence detected
- [ ] Language markers properly formatted in transcription output
- [ ] All Step 1 functionality (LiveKit-Dify-N8N) remains intact
- [ ] Dify processes mixed-language input correctly

**Files to Create:**
- `whisper_language_stt.py` - OpenAI Whisper STT with language detection
- `language_detection_config.py` - Language detection configuration
- `test_language_detection.py` - Language detection tests
- `mixed_language_examples.py` - Test cases for code-switching scenarios

---

#### **Step 2B: N8N Data Pipeline Foundation** ⏱️ 2-3 hours
**Goal:** Create extensible data processing pipeline for all future meeting data

**Implementation Tasks:**
- Set up N8N workflow: `Juno_Data_Pipeline`
- Create webhook endpoints for different data types (audio, transcription, analysis)
- Design database schema to support all future features
- Implement data storage with proper indexing for retrieval

**Future-Compatible Database Schema:**
```sql
-- Database schema designed for all future features
CREATE TABLE meetings (
    id UUID PRIMARY KEY,
    title VARCHAR(255),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    participants JSONB,
    status VARCHAR(50)
);

CREATE TABLE audio_segments (
    id UUID PRIMARY KEY,
    meeting_id UUID REFERENCES meetings(id),
    start_time TIMESTAMP,
    duration INTEGER,
    audio_url TEXT,
    transcription TEXT,
    speaker_id VARCHAR(100)
);

CREATE TABLE meeting_events (
    id UUID PRIMARY KEY,
    meeting_id UUID REFERENCES meetings(id),
    event_type VARCHAR(50), -- 'wake_word', 'question', 'action_item', etc.
    timestamp TIMESTAMP,
    content JSONB,
    processed BOOLEAN DEFAULT FALSE
);
```

**Testing Criteria:**
- [ ] Webhook receives and stores audio metadata
- [ ] Database handles concurrent writes
- [ ] Data retrieval queries execute under 100ms
- [ ] Webhook responds within 200ms

**Files to Create:**
- `n8n_workflows/Juno_Data_Pipeline.json` - N8N workflow export
- `database_schema.sql` - Complete database setup
- `webhook_handlers.py` - Python webhook handlers for testing
- `test_n8n_integration.py` - Integration tests

---

#### **Step 3: Basic Dify Integration Foundation** ⏱️ 2-3 hours
**Goal:** Establish Dify connection with context management for future features

**Implementation Tasks:**
- Create Dify agent: "Juno Meeting Assistant"
- Design conversation flow to handle both passive and active modes
- Set up context management for meeting continuity
- Implement basic response formatting for voice output

**Future-Compatible Design:**
```python
class DifyIntegration:
    def __init__(self):
        self.meeting_context = {}
        self.passive_mode = True
        self.conversation_history = []

    async def process_wake_word_activation(self, audio_context):
        # Future: Handle wake word detection
        pass

    async def process_meeting_query(self, query, meeting_context):
        # Future: RAG-enabled meeting queries
        pass

    async def generate_meeting_summary(self, meeting_id):
        # Future: Automated summary generation
        pass
```

**Testing Criteria:**
- [ ] Dify agent responds to test queries within 2 seconds
- [ ] Context maintains across multiple interactions
- [ ] Response format is voice-optimized (no markdown)
- [ ] Error handling works for API failures

**Files to Create:**
- `dify_integration.py` - Main Dify interface class
- `dify_config.json` - Dify agent configuration
- `conversation_manager.py` - Context management
- `test_dify_integration.py` - Dify testing suite

---

### **DAY 2: Core Functionality & Integration**

#### **Step 4: Continuous Recording & Transcription** ⏱️ 3-4 hours
**Goal:** Implement passive recording with real-time transcription

**Implementation Tasks:**
- Integrate Deepgram for continuous transcription
- Implement speaker diarization for future participant tracking
- Create transcription chunking and storage system
- Set up real-time transcription streaming to N8N

**Future-Compatible Design:**
```python
class TranscriptionEngine:
    def __init__(self):
        self.speakers = {}  # Future: Speaker identification
        self.wake_word_buffer = []
        self.meeting_transcript = []

    async def process_audio_chunk(self, audio_data):
        # Process for both transcription and wake-word detection
        transcription = await self.transcribe(audio_data)
        wake_word_detected = await self.check_wake_word(audio_data)

        if wake_word_detected:
            await self.trigger_activation()

        return transcription
```

**Testing Criteria:**
- [ ] Transcription accuracy >90% for clear speech
- [ ] Real-time processing with <3 second delay
- [ ] Speaker changes are detected and logged
- [ ] Transcription chunks stored with proper timestamps

**Files to Create:**
- `transcription_engine.py` - Main transcription class
- `speaker_diarization.py` - Speaker identification
- `transcription_storage.py` - Storage and chunking
- `test_transcription.py` - Transcription tests

---

#### **Step 5: Wake-Word Detection System** ⏱️ 3-4 hours
**Goal:** Implement "Juno" wake-word detection for activation

**Implementation Tasks:**
- Integrate wake-word detection library (Picovoice Porcupine or similar)
- Implement activation state management
- Create smooth transition between passive and active modes
- Set up activation logging and analytics

**Future-Compatible Design:**
```python
class WakeWordDetector:
    def __init__(self):
        self.sensitivity = 0.7
        self.activation_cooldown = 30  # seconds
        self.last_activation = None

    async def process_audio_for_wake_word(self, audio_chunk):
        if self.detect_wake_word(audio_chunk, "juno"):
            await self.activate_juno()
            return True
        return False

    async def activate_juno(self):
        # Switch to conversation mode
        # Notify all components of activation
        pass
```

**Testing Criteria:**
- [ ] "Juno" detection works with 95% accuracy
- [ ] False positive rate <1 per hour
- [ ] Activation triggers within 500ms
- [ ] System returns to passive mode after interaction

**Files to Create:**
- `wake_word_detector.py` - Wake word detection class
- `activation_manager.py` - State management
- `wake_word_models/` - Model files directory
- `test_wake_word.py` - Wake word tests

---

#### **Step 6: Active Conversation Mode** ⏱️ 2-3 hours
**Goal:** Implement full conversation capability when activated

**Implementation Tasks:**
- Create conversation state management
- Implement voice response system via Cartesia
- Set up conversation timeout and return to passive mode
- Create conversation logging and context preservation

**Testing Criteria:**
- [ ] Natural conversation flow after wake-word
- [ ] Voice responses are clear and natural
- [ ] Conversation ends gracefully after 30 seconds of silence
- [ ] Context is preserved for follow-up questions

**Files to Create:**
- `conversation_handler.py` - Active conversation management
- `voice_response.py` - TTS integration
- `conversation_timeout.py` - Timeout management
- `test_conversation.py` - Conversation tests

---

### **DAY 3: Advanced Features & Demo Preparation**

#### **Step 7: Meeting Context & RAG Integration** ⏱️ 3-4 hours
**Goal:** Enable intelligent meeting-aware responses

**Implementation Tasks:**
- Set up vector database (Pinecone/Qdrant) for meeting content
- Implement RAG pipeline for meeting-specific queries
- Create meeting summary generation
- Set up participant and topic tracking

**Testing Criteria:**
- [ ] Can answer questions about current meeting content
- [ ] Retrieves relevant information from meeting history
- [ ] Generates accurate meeting summaries
- [ ] Identifies key participants and topics

**Files to Create:**
- `rag_engine.py` - RAG implementation
- `vector_store.py` - Vector database interface
- `meeting_summarizer.py` - Summary generation
- `test_rag.py` - RAG testing

---

#### **Step 8: Action Item Detection & Automation** ⏱️ 2-3 hours
**Goal:** Automatically detect and process action items

**Implementation Tasks:**
- Create action item detection in transcription
- Set up automated follow-up workflows in N8N
- Implement task assignment and deadline tracking
- Create notification system for action items

**Testing Criteria:**
- [ ] Detects action items with 85% accuracy
- [ ] Creates follow-up tasks automatically
- [ ] Sends appropriate notifications
- [ ] Tracks completion status

**Files to Create:**
- `action_item_detector.py` - Action item detection
- `task_automation.py` - Automated task creation
- `notification_system.py` - Notifications
- `test_action_items.py` - Action item tests

---

#### **Step 9: Demo Integration & Polish** ⏱️ 2-3 hours
**Goal:** Prepare complete demo scenario

**Implementation Tasks:**
- Create demo meeting scenario
- Set up presentation flow showing passive → active modes
- Implement demo-specific responses and capabilities
- Create monitoring dashboard for demo

**Testing Criteria:**
- [ ] Complete 30-minute demo runs without issues
- [ ] All features demonstrate smoothly
- [ ] Passive mode works throughout meeting
- [ ] Active mode showcases all capabilities

**Files to Create:**
- `demo_scenario.py` - Demo orchestration
- `demo_dashboard.py` - Monitoring interface
- `demo_responses.py` - Demo-specific responses
- `demo_test.py` - Demo validation

---

#### **Step 10: Production Readiness & Documentation** ⏱️ 1-2 hours
**Goal:** Finalize system for production use

**Implementation Tasks:**
- Create deployment documentation
- Set up monitoring and alerting
- Implement backup and recovery procedures
- Create user guide and troubleshooting docs

**Testing Criteria:**
- [ ] System runs stable for 4+ hours
- [ ] All components have proper error handling
- [ ] Documentation is complete and tested
- [ ] Backup/recovery procedures verified

**Files to Create:**
- `DEPLOYMENT_GUIDE.md` - Deployment instructions
- `MONITORING_SETUP.md` - Monitoring configuration
- `USER_GUIDE.md` - User documentation
- `TROUBLESHOOTING.md` - Common issues and solutions

---

## 🔧 **Technical Architecture Overview**

### **Component Communication Flow:**
```
Audio Input → LiveKit → Wake-Word Detection → Transcription → N8N Storage
                ↓                                              ↓
         Conversation Mode ← Dify Intelligence ← Meeting Context/RAG
                ↓
         Voice Response → LiveKit Output
```

### **Data Flow Design:**
1. **Passive Mode**: Audio → Transcription → Storage → Analysis
2. **Active Mode**: Audio → Wake-Word → Dify → Response → Audio Output
3. **Background**: Continuous analysis → Action Items → Automation

### **Extensibility Points:**
- Audio processing pipeline supports multiple detection algorithms
- Database schema accommodates all planned features
- Dify integration handles multiple conversation types
- N8N workflows are modular and expandable

## 🎯 **Success Metrics**
- **Day 1**: Foundation components working independently
- **Day 2**: Integrated passive recording and wake-word activation
- **Day 3**: Full meeting assistant demo ready

Each step builds upon the previous while maintaining forward compatibility for all planned features. The architecture ensures no rework is needed as functionality expands.

## 📁 **Project Structure**
```
Juno/
├── JUNO_IMPLEMENTATION_PLAN.md
├── core/
│   ├── juno_audio_core.py
│   ├── audio_buffer.py
│   ├── transcription_engine.py
│   ├── wake_word_detector.py
│   └── conversation_handler.py
├── integrations/
│   ├── dify_integration.py
│   ├── n8n_integration.py
│   └── vector_store.py
├── workflows/
│   └── n8n_workflows/
├── tests/
│   ├── test_audio_core.py
│   ├── test_transcription.py
│   └── test_wake_word.py
├── demo/
│   ├── demo_scenario.py
│   └── demo_dashboard.py
└── docs/
    ├── DEPLOYMENT_GUIDE.md
    ├── USER_GUIDE.md
    └── TROUBLESHOOTING.md
```
