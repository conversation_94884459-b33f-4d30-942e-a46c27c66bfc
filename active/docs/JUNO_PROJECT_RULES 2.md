# JUNO PROJECT RULES
*Comprehensive Development Guidelines Based on Real Project Experience*

## 🎯 PROJECT OVERVIEW

**Juno** is a meeting assistant AI with a three-layer architecture:
- **LiveKit** = Voice interface (passive listening, wake-word detection, speech I/O)
- **Dify** = Intelligence layer (conversation management, RAG, context understanding)
- **N8N** = Automation layer (data processing, workflows, follow-up actions)

**Core Behavior**: Passive listening mode by default, activates only when "<PERSON>" is called, returns to passive mode after response.

---

## 🏗️ ARCHITECTURE PRINCIPLES

### 1. **Correct Layer Responsibilities**
- **LiveKit**: ONLY voice input/output, wake-word detection, language detection
- **Dify**: ALL conversational intelligence, context management, decision making
- **N8N**: ALL automation, data processing, external integrations

**❌ ANTI-PATTERN**: LiveKit acting as both brain AND mouth (original architecture mistake)
**✅ CORRECT**: LiveKit as pure voice interface routing everything to Dify

### 2. **Technology Stack Decisions**
- **STT**: <PERSON>A<PERSON> Whisper (NOT Deepgram) - proven Arabic-English code-switching
- **TTS**: ElevenLabs ONLY (voice ID: `gOkFV1JMCt0G0n9xmBwV`)
- **LLM**: GPT-4o-mini for LiveKit agent
- **VAD**: Silero VAD for voice activity detection

### 3. **API Integration Patterns**
- **Dify API**: Use `"response_mode": "streaming"` (NOT "blocking")
- **Webhook Communication**: LiveKit ↔ N8N via webhooks for data flow
- **Language Markers**: Output format `[EN]English text[/EN]` `[AR]Arabic text[/AR]`

---

## 🚨 CRITICAL ERROR PREVENTION

### 1. **"Too Quiet" Error Prevention**
**Root Causes & Solutions**:
- ✅ Use modern `Agent` + `AgentSession` API (NOT deprecated `VoiceAgent`)
- ✅ Correct STT parameter names: `endpointing_ms=300` (NOT `endpointing=300`)
- ✅ Add `conn_options=None` parameter to `_recognize_impl` method
- ✅ Verify all environment variables are properly configured
- ✅ Use single definitive agent file (avoid multiple versions)

### 2. **API Compatibility Issues**
**ChatContext Problems**:
- ❌ NEVER use: `ChatContext().append()` or `ChatContext().messages`
- ✅ USE: `Agent(instructions="...")` with modern API

**Dify API Issues**:
- ❌ NEVER use: `"response_mode": "blocking"`
- ✅ USE: `"response_mode": "streaming"`

### 3. **Language Detection Issues**
- ❌ NEVER use Deepgram for Arabic (confirmed poor performance)
- ✅ USE: Custom `WhisperLanguageSTT` with `detect_language=True`
- ✅ Configure: `supported_languages=["en", "ar"]`

---

## 🔧 IMPLEMENTATION PROTOCOLS

### 1. **Safe Code Chunk Testing (SCCT)**
**MANDATORY for all changes**:
1. **Isolate**: Test each component independently
2. **Verify**: Confirm functionality before integration
3. **Document**: Record working configurations
4. **Backup**: Keep working versions before modifications

### 2. **File Management Rules**
- **Single Source of Truth**: One definitive agent file per configuration
- **Clear Naming**: `juno_final_elevenlabs_only.py` (descriptive, final)
- **Version Control**: Delete old/test files to avoid confusion
- **Documentation**: Include working configuration in comments

### 3. **Environment Configuration**
**Required Variables**:
```bash
LIVEKIT_URL=wss://your-project.livekit.cloud
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret
OPENAI_API_KEY=your-openai-key
ELEVEN_API_KEY=your-elevenlabs-key
```

---

## 🎯 DEVELOPMENT METHODOLOGY

### 1. **Incremental Development**
- **Step-by-Step**: Each step must be 100% functional before proceeding
- **Independent Testing**: Each component testable in isolation
- **Future-Compatible**: Design for extensibility without rework
- **No Dependencies**: No step depends on future functionality

### 2. **Architecture-First Approach**
- **Verify Architecture**: Ensure correct layer responsibilities
- **Simple Solutions**: Choose simplest working approach
- **Proven Technologies**: Use technologies with confirmed compatibility
- **Minimal Complexity**: Avoid over-engineering

### 3. **Performance Optimization**
**Hybrid Response Strategy**:
- **Local Handling**: Simple queries (greetings, confirmations) - instant response
- **Dify Routing**: Complex queries - route to intelligence layer
- **Latency Reduction**: 60-80% improvement in response time

---

## 🛠️ TROUBLESHOOTING PROTOCOLS

### 1. **Systematic Debugging**
1. **Check Environment**: Verify all API keys and URLs
2. **Test Components**: Isolate STT, TTS, LLM individually
3. **Verify API Calls**: Check request/response formats
4. **Review Logs**: Look for specific error patterns
5. **Compare Working**: Reference known working configurations

### 2. **Common Error Patterns**
**Agent Not Responding**:
- Check modern API usage (Agent + AgentSession)
- Verify STT parameter names
- Confirm environment variables
- Test with single agent file

**Language Detection Issues**:
- Switch from Deepgram to Whisper
- Configure language detection properly
- Test with known Arabic/English phrases

**API Integration Failures**:
- Use streaming mode for Dify
- Check webhook URLs and formats
- Verify response cleaning for speech

### 3. **Emergency Protocols**
**When Everything Breaks**:
1. **Revert**: Return to last known working configuration
2. **Isolate**: Test each component separately
3. **Rebuild**: Start from working base, add one change at a time
4. **Document**: Record what broke and why

---

## 📋 QUALITY ASSURANCE

### 1. **Testing Checklist**
**Basic Functionality**:
- [ ] Agent starts without errors
- [ ] Responds to voice input
- [ ] Wake-word detection works
- [ ] Returns to passive mode

**Language Support**:
- [ ] Pure English transcription
- [ ] Pure Arabic transcription
- [ ] English-Arabic code-switching
- [ ] Language markers in output

**Integration**:
- [ ] Dify API communication
- [ ] N8N webhook functionality
- [ ] Response cleaning for speech
- [ ] Real-time conversation flow

### 2. **Performance Metrics**
- **Response Latency**: < 500ms for simple queries
- **Language Detection**: > 95% accuracy
- **Wake-word Detection**: < 2 second activation
- **Conversation Flow**: Natural back-and-forth

### 3. **Success Criteria**
- **Technical**: All components working independently and together
- **User Experience**: Natural conversation with proper language handling
- **Business Value**: Meeting assistance features functional
- **Scalability**: Architecture supports planned features

---

## 🎪 DEMO PREPARATION

### 1. **Demo Environment**
- **Stable Configuration**: Use proven working setup
- **Backup Plan**: Have fallback configuration ready
- **Test Scenarios**: Practice with real meeting scenarios
- **Error Handling**: Prepare for common issues

### 2. **Feature Showcase**
- **Passive Listening**: Demonstrate continuous recording
- **Wake-word Activation**: Show "Juno" detection
- **Language Switching**: Arabic-English code-switching
- **Intelligence**: Context-aware responses via Dify
- **Automation**: N8N workflow integration

### 3. **Risk Mitigation**
- **Multiple Backups**: Several working configurations
- **Offline Fallback**: Local testing capability
- **Quick Recovery**: Rapid restart procedures
- **Documentation**: Step-by-step troubleshooting guide

---

## 📚 KNOWLEDGE BASE

### 1. **Working Configurations**
- **Agent File**: `juno_final_elevenlabs_only.py`
- **STT**: Custom WhisperLanguageSTT
- **TTS**: ElevenLabs (voice: gOkFV1JMCt0G0n9xmBwV)
- **API Mode**: Streaming for Dify

### 2. **Proven Solutions**
- **Arabic Support**: Whisper STT with language detection
- **Low Latency**: Hybrid local/remote response strategy
- **Stability**: Modern LiveKit Agents API
- **Integration**: Webhook-based architecture

### 3. **Lessons Learned**
- **Architecture Matters**: Correct layer separation is critical
- **Technology Choice**: Use proven, compatible technologies
- **Incremental Development**: Build and test step-by-step
- **Documentation**: Record working configurations immediately
- **Simplicity**: Choose simplest working solution

---

## 🚀 FUTURE DEVELOPMENT

### 1. **Planned Features**
- Smart attendance tracking
- Key decision detection
- Follow-up capture
- Meeting health scoring
- Action item extraction
- Quote extraction

### 2. **Architecture Extensions**
- **Database Integration**: PostgreSQL via N8N
- **RAG Implementation**: Meeting context understanding
- **Advanced Analytics**: Meeting effectiveness metrics
- **Multi-modal Input**: Screen sharing, document analysis

### 3. **Scalability Considerations**
- **Performance**: Optimize for longer meetings
- **Storage**: Efficient audio/transcript storage
- **Security**: Enterprise-grade data protection
- **Integration**: Additional business tool connections

---

*This document represents the collective learnings from the Juno project development process. Follow these rules to avoid repeating solved problems and maintain development velocity.*