# LiveKit Voice Agent with OpenAI, Cartesia & DeepGram

A sophisticated voice AI agent built with LiveKit, featuring:
- **STT (Speech-to-Text)**: DeepGram Nova-2
- **LLM (Language Model)**: OpenAI GPT-4o-mini
- **TTS (Text-to-Speech)**: <PERSON><PERSON><PERSON> Sonic
- **Turn Detection**: LiveKit's EOU model for natural conversations

## 🚀 Quick Start

### 1. Prerequisites
- Python 3.8 or higher
- Valid API keys for OpenAI, Cartesia, and DeepGram
- LiveKit account (free tier includes 5,000 minutes/month)

### 2. Installation

```bash
# Make setup script executable
chmod +x setup.sh

# Run setup
./setup.sh
```

### 3. Configuration

Edit the `.env` file and add your API keys:

```env
# Your API Keys (replace with actual keys)
OPENAI_API_KEY=sk-your-openai-key-here
CARTESIA_API_KEY=your-cartesia-key-here
DEEPGRAM_API_KEY=your-deepgram-key-here

# LiveKit Configuration (already set from your project)
LIVEKIT_URL=wss://cloudy-neuralnet-29u9d5.livekit.cloud
LIVEKIT_API_KEY=APIZjdNU2pHEamh
LIVEKIT_API_SECRET=SCOcEtH2fGuUAtlzVGlj5vTRBZd6hqAfijgnm65SfiBA
```

### 4. Run the Agent

```bash
# Activate virtual environment
source venv/bin/activate

# Start the agent in development mode
python3 agent.py dev
```

### 5. Test Your Agent

1. The agent will start and show a URL to the LiveKit playground
2. Open the URL in your browser
3. Allow microphone permissions
4. Start talking to your AI agent!

## 🎛️ Customization

### Voice Configuration

To change the Cartesia voice, edit `agent.py`:

```python
# In the tts_instance configuration
tts_instance = cartesia.TTS(
    model="sonic-english",
    voice="your-preferred-voice-id",  # Change this
    speed=1.0,
    emotion=["positivity:high", "curiosity:high"]
)
```

### LLM Model

To use GPT-4 instead of GPT-4o-mini:

```python
# In the llm_instance configuration
llm_instance = openai.LLM(
    model="gpt-4",  # Change from gpt-4o-mini
    temperature=0.7,
    max_tokens=150,
)
```

### System Prompt

Modify the agent's personality in the `initial_ctx` section:

```python
initial_ctx = llm.ChatContext().append(
    role="system",
    text="Your custom system prompt here..."
)
```

## 🔧 Troubleshooting

### Common Issues

1. **"API key not found" errors**
   - Ensure all API keys are correctly set in `.env`
   - Check that there are no extra spaces or quotes

2. **Turn detector model download fails**
   - Run the agent anyway, it will download on first use
   - Ensure stable internet connection

3. **Agent doesn't respond**
   - Check microphone permissions in browser
   - Verify all API keys are valid and have sufficient credits
   - Check the console for error messages

### Debug Mode

Run with verbose logging:

```bash
LOG_LEVEL=DEBUG python3 agent.py dev
```

## 📚 Learning Resources

This implementation follows the tutorial from: https://www.youtube.com/watch?v=_diVy9VdT5s

Key concepts:
- **Pipeline Architecture**: STT → LLM → TTS flow
- **Turn Detection**: Prevents interruptions during speech
- **Async Processing**: Non-blocking voice processing
- **LiveKit Integration**: Real-time communication platform

## 🔄 Next Steps

1. **Add Knowledge Base**: Integrate with vector databases for RAG
2. **Function Calling**: Add tools and external API integrations  
3. **Multimodal**: Extend to handle images and video
4. **Production Deployment**: Scale with LiveKit's cloud infrastructure

## 🤝 Integration with Langchain/LangGraph

This agent can be extended with Langchain components:

```python
from langchain.agents import AgentExecutor
from langchain.tools import Tool

# Add tools to your agent
tools = [
    Tool(name="calculator", func=calculator_function),
    Tool(name="search", func=search_function),
]

# Integrate with LangGraph for complex workflows
```

Perfect foundation for your Langchain/LangGraph learning journey!
