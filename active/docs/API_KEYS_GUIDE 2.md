# API Keys Setup Guide

This guide will help you obtain and configure the API keys needed for your LiveKit voice agent.

## 🔑 Required API Keys

### 1. OpenAI API Key

**Purpose**: Powers the language model (GPT-4o-mini) for conversation intelligence

**How to get it**:
1. Go to [OpenAI Platform](https://platform.openai.com)
2. Sign up or log in to your account
3. Navigate to "API Keys" in the left sidebar
4. Click "Create new secret key"
5. Copy the key (starts with `sk-`)

**Cost**: Pay-per-use, GPT-4o-mini is very cost-effective (~$0.15/1M input tokens)

### 2. Cartesia API Key

**Purpose**: Provides ultra-fast, high-quality text-to-speech (Sonic model)

**How to get it**:
1. Go to [Cartesia](https://cartesia.ai)
2. Sign up for an account
3. Navigate to your dashboard/API section
4. Generate an API key
5. Copy the key

**Cost**: Competitive pricing, often has free tier for testing

### 3. DeepGram API Key

**Purpose**: Handles speech-to-text conversion with high accuracy

**How to get it**:
1. Go to [DeepGram](https://deepgram.com)
2. Sign up for an account
3. You'll get $200 in free credits automatically
4. Go to your dashboard and find "API Keys"
5. Copy your API key

**Cost**: $200 free credits (lasts weeks/months for testing)

## 🛠️ Configuration Steps

### Step 1: Edit the .env file

Open the `.env` file in your `livekit-voice-agent` folder and replace the placeholder values:

```env
# Replace these with your actual API keys
OPENAI_API_KEY=sk-your-actual-openai-key-here
CARTESIA_API_KEY=your-actual-cartesia-key-here
DEEPGRAM_API_KEY=your-actual-deepgram-key-here

# LiveKit settings (already configured)
LIVEKIT_URL=wss://cloudy-neuralnet-29u9d5.livekit.cloud
LIVEKIT_API_KEY=APIZjdNU2pHEamh
LIVEKIT_API_SECRET=SCOcEtH2fGuUAtlzVGlj5vTRBZd6hqAfijgnm65SfiBA
```

### Step 2: Test Your Configuration

Run the configuration test:

```bash
python3 test_config.py
```

This will verify all your API keys are properly set.

## 🎛️ Optional Customizations

### Cartesia Voice Selection

To use a different voice, you can:

1. Visit [Cartesia Playground](https://play.cartesia.ai)
2. Try different voices and copy the voice ID
3. Update the `voice` parameter in `agent.py`:

```python
tts_instance = cartesia.TTS(
    model="sonic-english",
    voice="your-preferred-voice-id-here",  # Change this
    speed=1.0,
    emotion=["positivity:high", "curiosity:high"]
)
```

### OpenAI Model Selection

You can upgrade to GPT-4 for better responses:

```python
llm_instance = openai.LLM(
    model="gpt-4",  # More capable but more expensive
    temperature=0.7,
    max_tokens=150,
)
```

## 💰 Cost Estimation

For typical testing and development:

- **OpenAI (GPT-4o-mini)**: ~$1-5/month for moderate usage
- **Cartesia**: Often has free tier, then competitive pricing
- **DeepGram**: $200 free credits should last months
- **LiveKit**: 5,000 free minutes/month

**Total estimated cost for development**: $5-15/month

## 🔒 Security Best Practices

1. **Never commit API keys to version control**
2. **Use environment variables** (already set up in this project)
3. **Rotate keys regularly** if used in production
4. **Set usage limits** in each platform's dashboard
5. **Monitor usage** to avoid unexpected charges

## 🆘 Troubleshooting

### "Invalid API Key" Errors

1. Double-check the key is copied correctly (no extra spaces)
2. Ensure the key is active in the provider's dashboard
3. Check if you have sufficient credits/quota
4. Verify the key has the right permissions

### "Rate Limit" Errors

1. You're making too many requests too quickly
2. Check your usage limits in each platform
3. Consider upgrading your plan if needed

### "Insufficient Credits" Errors

1. Check your balance in each platform
2. Add payment method or credits as needed
3. DeepGram's $200 should last a long time for testing

## 📞 Support

If you encounter issues:

1. **OpenAI**: Check their [help center](https://help.openai.com)
2. **Cartesia**: Contact their support team
3. **DeepGram**: Excellent documentation and support
4. **LiveKit**: Active community and documentation

Remember: All these platforms have generous free tiers perfect for learning and development!
