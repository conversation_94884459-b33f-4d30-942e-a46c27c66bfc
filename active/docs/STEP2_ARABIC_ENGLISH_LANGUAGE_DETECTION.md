# Step 2: Arabic-English Language Detection Implementation

## 🎯 **Objective**
Modify the existing LiveKit voice agent to detect and handle Arabic-English language switching within the same conversation, sentence, or even phrase. Users frequently switch between English and Arabic mid-sentence in business meetings.

## 🔧 **Technical Requirements**

### 2.1: OpenAI Whisper STT Integration
- **Replace Deepgram STT** with OpenAI Whisper STT in the LiveKit agent configuration
- **Configure Whisper** for automatic language detection between English ("en") and Arabic ("ar") only
- **Enable Whisper** to return language labels and timestamps for each detected language segment
- **Ensure transcription output** includes language markers for each segment

### 2.2: Code-Switching Detection
- **Implement real-time** language switching detection within sentences
- **Add language markers** to transcription output format
- **Handle mixed-language phrases** and rapid language switching
- **Ensure seamless transition** between languages mid-conversation

### 2.3: Language-Aware Processing
- **Update Dify agent** to process mixed-language input with language markers
- **Maintain conversation context** across language switches
- **Preserve all existing Step 1 functionality** (wake-word, passive listening)
- **Test with various code-switching scenarios**

## 📋 **Expected Output Format**
```
Input Audio: "We need to finish the budget report قبل نهاية الأسبوع by Friday"
Output: "We need to finish the [EN]budget report[/EN] [AR]قبل نهاية الأسبوع[/AR] [EN]by Friday[/EN]"
```

## 🧪 **Testing Criteria**

### Core Functionality Tests:
- [ ] **Pure English speech** recognition works correctly
- [ ] **Pure Arabic speech** recognition works correctly  
- [ ] **English-Arabic code-switching** within same sentence detected
- [ ] **Language markers** properly formatted in transcription output
- [ ] **All Step 1 functionality** (LiveKit-Dify-N8N) remains intact
- [ ] **Dify processes** mixed-language input correctly

### Performance Tests:
- [ ] **Real-time processing** with <3 second delay
- [ ] **Language detection accuracy** >90% for clear speech
- [ ] **Code-switching detection** works for rapid language changes
- [ ] **Memory usage** remains stable during long conversations

## 🔄 **Implementation Steps**

### Step 2.1: Replace STT Component (30 minutes)
1. **Remove Deepgram STT** from LiveKit agent configuration
2. **Install OpenAI Whisper** dependencies
3. **Configure Whisper STT** with language detection
4. **Test basic functionality** with English and Arabic

### Step 2.2: Add Language Markers (45 minutes)
1. **Implement language marker system**
2. **Process Whisper segments** with language labels
3. **Format output** with proper language tags
4. **Test marker accuracy** with mixed content

### Step 2.3: Update Dify Integration (30 minutes)
1. **Modify Dify agent** to handle language markers
2. **Update system prompts** for mixed-language processing
3. **Test conversation flow** with marked input
4. **Verify response quality** across languages

### Step 2.4: Comprehensive Testing (15 minutes)
1. **Test pure English** conversations
2. **Test pure Arabic** conversations
3. **Test code-switching** scenarios
4. **Verify Step 1 functionality** still works

## 📁 **Files to Create**

### Core Implementation:
- `whisper_language_stt.py` - OpenAI Whisper STT with language detection
- `language_detection_config.py` - Language detection configuration
- `language_marker_processor.py` - Language marker formatting

### Testing:
- `test_language_detection.py` - Language detection tests
- `mixed_language_examples.py` - Test cases for code-switching scenarios
- `language_performance_tests.py` - Performance benchmarks

### Integration:
- `updated_juno_agent.py` - Modified LiveKit agent with Whisper
- `dify_multilingual_config.py` - Updated Dify configuration

## 🎯 **Success Metrics**
- **Language Detection Accuracy**: >90% for clear speech
- **Code-switching Detection**: Works within same sentence
- **Processing Latency**: <3 seconds for real-time conversation
- **Step 1 Compatibility**: All existing functionality preserved
- **Dify Integration**: Processes mixed-language input correctly

## 🚀 **Ready to Begin Implementation**

The plan maintains all Step 1 functionality while adding sophisticated language detection capabilities. The implementation focuses on seamless Arabic-English code-switching detection without breaking existing LiveKit-Dify-N8N integration.

**Next Action**: Begin Step 2.1 - Replace STT Component with OpenAI Whisper
