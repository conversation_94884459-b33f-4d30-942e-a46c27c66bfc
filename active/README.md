# Active Development Folder

This is your main working directory for current development projects.

## Current Project
- **Status**: Ready for Speechmatics STT Integration
- **Last Updated**: June 4, 2025
- **Previous Archive**: `archive/2025-06-04_2025-06-04-working-arabic-english-wake-word/`

## 🎯 Next Development Phase: Speechmatics Integration

### Goal
Implement and test Speechmatics STT as an alternative to OpenAI Whisper for improved:
- Arabic speech recognition accuracy
- Real-time processing speed
- Professional-grade features

### Previous Working State
✅ **Archived**: Complete Arabic-English voice agent with wake word detection
- Working Whisper STT with language markers
- ElevenLabs TTS integration
- LiveKit voice processing
- Mode switching functionality

## Workflow
1. All active development happens in this folder
2. When a development phase is complete, the entire folder gets archived
3. A fresh active folder is created for the next phase

## Archive Process
When development is complete:
1. Copy entire `active/` folder to `archive/YYYY-MM-DD_project-name/`
2. Clear or reset `active/` folder for next project
3. Update this README with new project info

## Structure
```
active/
├── README.md (this file)
├── src/          (source code)
├── docs/         (documentation)
├── tests/        (test files)
└── config/       (configuration files)
```
