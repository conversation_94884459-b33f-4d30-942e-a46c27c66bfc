# Juno Voice Agent - Working Arabic-English Configuration

✅ **RESTORED TO WORKING CONFIGURATION**

## Current Status
- **Status**: Working Arabic-English voice agent with Whisper STT
- **Last Updated**: June 4, 2025
- **Configuration**: Restored from `archive/2025-06-04_2025-06-04-working-arabic-english-wake-word/`

## Features

- **Custom Whisper STT**: Working Arabic-English language detection and code-switching
- **ElevenLabs TTS**: High-quality text-to-speech synthesis
- **Language Detection**: Automatic Arabic-English detection with language markers
- **LiveKit Integration**: Real-time voice communication
- **N8N Webhook Support**: Workflow automation integration

## Quick Start

1. **Run Agent**:
   ```bash
   ./run.sh
   ```

2. **Test Voice Agent**:
   - Open: https://first-project-2rpwr03w.livekit.cloud
   - Test with Arabic: "مرحبا جونو، كيف حالك اليوم؟"
   - Test with English: "Hello <PERSON>, how are you today?"

## Workflow
1. All active development happens in this folder
2. When a development phase is complete, the entire folder gets archived
3. A fresh active folder is created for the next phase

## Archive Process
When development is complete:
1. Copy entire `active/` folder to `archive/YYYY-MM-DD_project-name/`
2. Clear or reset `active/` folder for next project
3. Update this README with new project info

## Structure
```
active/
├── README.md (this file)
├── src/          (source code)
├── docs/         (documentation)
├── tests/        (test files)
└── config/       (configuration files)
```
