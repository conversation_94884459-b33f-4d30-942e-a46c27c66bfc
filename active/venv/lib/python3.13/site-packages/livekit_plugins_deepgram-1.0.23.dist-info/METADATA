Metadata-Version: 2.4
Name: livekit-plugins-deepgram
Version: 1.0.23
Summary: Agent Framework plugin for services using Deepgram's API.
Project-URL: Documentation, https://docs.livekit.io
Project-URL: Website, https://livekit.io/
Project-URL: Source, https://github.com/livekit/agents
Author-email: LiveKit <<EMAIL>>
License-Expression: Apache-2.0
Keywords: audio,livekit,realtime,video,webrtc
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Topic :: Multimedia :: Sound/Audio
Classifier: Topic :: Multimedia :: Video
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.9.0
Requires-Dist: livekit-agents[codecs]>=1.0.23
Requires-Dist: numpy>=1.26
Description-Content-Type: text/markdown

# Deepgram plugin for LiveKit Agents

Support for speech-to-text with [Deepgram](https://deepgram.com/).

See [https://docs.livekit.io/agents/integrations/stt/deepgram/](https://docs.livekit.io/agents/integrations/stt/deepgram/) for more information.

## Installation

```bash
pip install livekit-plugins-deepgram
```

## Pre-requisites

You'll need an API key from DeepGram. It can be set as an environment variable: `DEEPGRAM_API_KEY`
