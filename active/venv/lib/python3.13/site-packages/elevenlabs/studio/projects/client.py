# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ... import core
from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.add_project_response_model import AddProjectResponseModel
from ...types.convert_project_response_model import Convert<PERSON><PERSON><PERSON>ResponseModel
from ...types.delete_project_response_model import DeleteProjectResponseModel
from ...types.edit_project_response_model import EditProjectResponseModel
from ...types.get_projects_response import GetProjectsResponse
from ...types.project_extended_response import ProjectExtendedResponse
from .chapters.client import AsyncChaptersClient, ChaptersClient
from .content.client import AsyncContentClient, ContentClient
from .pronunciation_dictionaries.client import AsyncPronunciationDictionariesClient, PronunciationDictionariesClient
from .raw_client import AsyncRawProjectsClient, RawProjectsClient
from .snapshots.client import AsyncSnapshotsClient, SnapshotsClient
from .types.projects_create_request_apply_text_normalization import ProjectsCreateRequestApplyTextNormalization
from .types.projects_create_request_fiction import ProjectsCreateRequestFiction
from .types.projects_create_request_source_type import ProjectsCreateRequestSourceType
from .types.projects_create_request_target_audience import ProjectsCreateRequestTargetAudience

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ProjectsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawProjectsClient(client_wrapper=client_wrapper)
        self.content = ContentClient(client_wrapper=client_wrapper)

        self.snapshots = SnapshotsClient(client_wrapper=client_wrapper)

        self.chapters = ChaptersClient(client_wrapper=client_wrapper)

        self.pronunciation_dictionaries = PronunciationDictionariesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawProjectsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawProjectsClient
        """
        return self._raw_client

    def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> GetProjectsResponse:
        """
        Returns a list of your Studio projects with metadata.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetProjectsResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.list()
        """
        _response = self._raw_client.list(request_options=request_options)
        return _response.data

    def create(
        self,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        default_model_id: str,
        from_url: typing.Optional[str] = OMIT,
        from_document: typing.Optional[core.File] = OMIT,
        quality_preset: typing.Optional[str] = OMIT,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        genres: typing.Optional[typing.List[str]] = OMIT,
        target_audience: typing.Optional[ProjectsCreateRequestTargetAudience] = OMIT,
        language: typing.Optional[str] = OMIT,
        content_type: typing.Optional[str] = OMIT,
        original_publication_date: typing.Optional[str] = OMIT,
        mature_content: typing.Optional[bool] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        acx_volume_normalization: typing.Optional[bool] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[typing.List[str]] = OMIT,
        callback_url: typing.Optional[str] = OMIT,
        fiction: typing.Optional[ProjectsCreateRequestFiction] = OMIT,
        apply_text_normalization: typing.Optional[ProjectsCreateRequestApplyTextNormalization] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        auto_assign_voices: typing.Optional[bool] = OMIT,
        source_type: typing.Optional[ProjectsCreateRequestSourceType] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddProjectResponseModel:
        """
        Creates a new Studio project, it can be either initialized as blank, from a document or from a URL.

        Parameters
        ----------
        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        default_model_id : str
            The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        from_document : typing.Optional[core.File]
            See core.File for more documentation

        quality_preset : typing.Optional[str]
            Output quality of the generated audio. Must be one of:
            standard - standard output format, 128kbps with 44.1kHz sample rate.
            high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
            ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
            ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        description : typing.Optional[str]
            An optional description of the Studio project.

        genres : typing.Optional[typing.List[str]]
            An optional list of genres associated with the Studio project.

        target_audience : typing.Optional[ProjectsCreateRequestTargetAudience]
            An optional target audience of the Studio project.

        language : typing.Optional[str]
            An optional language of the Studio project. Two-letter language code (ISO 639-1).

        content_type : typing.Optional[str]
            An optional content type of the Studio project.

        original_publication_date : typing.Optional[str]
            An optional original publication date of the Studio project, in the format YYYY-MM-DD or YYYY.

        mature_content : typing.Optional[bool]
            An optional specification of whether this Studio project contains mature content.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        acx_volume_normalization : typing.Optional[bool]
            [Deprecated] When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        pronunciation_dictionary_locators : typing.Optional[typing.List[str]]
            A list of pronunciation dictionary locators (pronunciation_dictionary_id, version_id) encoded as a list of JSON strings for pronunciation dictionaries to be applied to the text. A list of json encoded strings is required as adding projects may occur through formData as opposed to jsonBody. To specify multiple dictionaries use multiple --form lines in your curl, such as --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"' --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'. Note that multiple dictionaries are not currently supported by our UI which will only show the first.

        callback_url : typing.Optional[str]
            A url that will be called by our service when the Studio project is converted. Request will contain a json blob containing the status of the conversion

        fiction : typing.Optional[ProjectsCreateRequestFiction]
            An optional specification of whether the content of this Studio project is fiction.

        apply_text_normalization : typing.Optional[ProjectsCreateRequestApplyTextNormalization]

                This parameter controls text normalization with four modes: 'auto', 'on', 'apply_english' and 'off'.
                When set to 'auto', the system will automatically decide whether to apply text normalization
                (e.g., spelling out numbers). With 'on', text normalization will always be applied, while
                with 'off', it will be skipped. 'apply_english' is the same as 'on' but will assume that text is in English.


        auto_convert : typing.Optional[bool]
            Whether to auto convert the Studio project to audio or not.

        auto_assign_voices : typing.Optional[bool]
            [Alpha Feature] Whether automatically assign voices to phrases in the create Project.

        source_type : typing.Optional[ProjectsCreateRequestSourceType]
            The type of Studio project to create.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddProjectResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.create(
            name="name",
            default_title_voice_id="default_title_voice_id",
            default_paragraph_voice_id="default_paragraph_voice_id",
            default_model_id="default_model_id",
        )
        """
        _response = self._raw_client.create(
            name=name,
            default_title_voice_id=default_title_voice_id,
            default_paragraph_voice_id=default_paragraph_voice_id,
            default_model_id=default_model_id,
            from_url=from_url,
            from_document=from_document,
            quality_preset=quality_preset,
            title=title,
            author=author,
            description=description,
            genres=genres,
            target_audience=target_audience,
            language=language,
            content_type=content_type,
            original_publication_date=original_publication_date,
            mature_content=mature_content,
            isbn_number=isbn_number,
            acx_volume_normalization=acx_volume_normalization,
            volume_normalization=volume_normalization,
            pronunciation_dictionary_locators=pronunciation_dictionary_locators,
            callback_url=callback_url,
            fiction=fiction,
            apply_text_normalization=apply_text_normalization,
            auto_convert=auto_convert,
            auto_assign_voices=auto_assign_voices,
            source_type=source_type,
            request_options=request_options,
        )
        return _response.data

    def get(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ProjectExtendedResponse:
        """
        Returns information about a specific Studio project. This endpoint returns more detailed information about a project than `GET /v1/studio`.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ProjectExtendedResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.get(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get(project_id, request_options=request_options)
        return _response.data

    def update(
        self,
        project_id: str,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EditProjectResponseModel:
        """
        Updates the specified Studio project by setting the values of the parameters passed.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditProjectResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.update(
            project_id="21m00Tcm4TlvDq8ikWAM",
            name="Project 1",
            default_title_voice_id="21m00Tcm4TlvDq8ikWAM",
            default_paragraph_voice_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.update(
            project_id,
            name=name,
            default_title_voice_id=default_title_voice_id,
            default_paragraph_voice_id=default_paragraph_voice_id,
            title=title,
            author=author,
            isbn_number=isbn_number,
            volume_normalization=volume_normalization,
            request_options=request_options,
        )
        return _response.data

    def delete(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteProjectResponseModel:
        """
        Deletes a Studio project.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteProjectResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.delete(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.delete(project_id, request_options=request_options)
        return _response.data

    def convert(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ConvertProjectResponseModel:
        """
        Starts conversion of a Studio project and all of its chapters.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ConvertProjectResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.convert(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.convert(project_id, request_options=request_options)
        return _response.data


class AsyncProjectsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawProjectsClient(client_wrapper=client_wrapper)
        self.content = AsyncContentClient(client_wrapper=client_wrapper)

        self.snapshots = AsyncSnapshotsClient(client_wrapper=client_wrapper)

        self.chapters = AsyncChaptersClient(client_wrapper=client_wrapper)

        self.pronunciation_dictionaries = AsyncPronunciationDictionariesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawProjectsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawProjectsClient
        """
        return self._raw_client

    async def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> GetProjectsResponse:
        """
        Returns a list of your Studio projects with metadata.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetProjectsResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.list()


        asyncio.run(main())
        """
        _response = await self._raw_client.list(request_options=request_options)
        return _response.data

    async def create(
        self,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        default_model_id: str,
        from_url: typing.Optional[str] = OMIT,
        from_document: typing.Optional[core.File] = OMIT,
        quality_preset: typing.Optional[str] = OMIT,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        genres: typing.Optional[typing.List[str]] = OMIT,
        target_audience: typing.Optional[ProjectsCreateRequestTargetAudience] = OMIT,
        language: typing.Optional[str] = OMIT,
        content_type: typing.Optional[str] = OMIT,
        original_publication_date: typing.Optional[str] = OMIT,
        mature_content: typing.Optional[bool] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        acx_volume_normalization: typing.Optional[bool] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[typing.List[str]] = OMIT,
        callback_url: typing.Optional[str] = OMIT,
        fiction: typing.Optional[ProjectsCreateRequestFiction] = OMIT,
        apply_text_normalization: typing.Optional[ProjectsCreateRequestApplyTextNormalization] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        auto_assign_voices: typing.Optional[bool] = OMIT,
        source_type: typing.Optional[ProjectsCreateRequestSourceType] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddProjectResponseModel:
        """
        Creates a new Studio project, it can be either initialized as blank, from a document or from a URL.

        Parameters
        ----------
        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        default_model_id : str
            The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        from_document : typing.Optional[core.File]
            See core.File for more documentation

        quality_preset : typing.Optional[str]
            Output quality of the generated audio. Must be one of:
            standard - standard output format, 128kbps with 44.1kHz sample rate.
            high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
            ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
            ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        description : typing.Optional[str]
            An optional description of the Studio project.

        genres : typing.Optional[typing.List[str]]
            An optional list of genres associated with the Studio project.

        target_audience : typing.Optional[ProjectsCreateRequestTargetAudience]
            An optional target audience of the Studio project.

        language : typing.Optional[str]
            An optional language of the Studio project. Two-letter language code (ISO 639-1).

        content_type : typing.Optional[str]
            An optional content type of the Studio project.

        original_publication_date : typing.Optional[str]
            An optional original publication date of the Studio project, in the format YYYY-MM-DD or YYYY.

        mature_content : typing.Optional[bool]
            An optional specification of whether this Studio project contains mature content.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        acx_volume_normalization : typing.Optional[bool]
            [Deprecated] When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        pronunciation_dictionary_locators : typing.Optional[typing.List[str]]
            A list of pronunciation dictionary locators (pronunciation_dictionary_id, version_id) encoded as a list of JSON strings for pronunciation dictionaries to be applied to the text. A list of json encoded strings is required as adding projects may occur through formData as opposed to jsonBody. To specify multiple dictionaries use multiple --form lines in your curl, such as --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"' --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'. Note that multiple dictionaries are not currently supported by our UI which will only show the first.

        callback_url : typing.Optional[str]
            A url that will be called by our service when the Studio project is converted. Request will contain a json blob containing the status of the conversion

        fiction : typing.Optional[ProjectsCreateRequestFiction]
            An optional specification of whether the content of this Studio project is fiction.

        apply_text_normalization : typing.Optional[ProjectsCreateRequestApplyTextNormalization]

                This parameter controls text normalization with four modes: 'auto', 'on', 'apply_english' and 'off'.
                When set to 'auto', the system will automatically decide whether to apply text normalization
                (e.g., spelling out numbers). With 'on', text normalization will always be applied, while
                with 'off', it will be skipped. 'apply_english' is the same as 'on' but will assume that text is in English.


        auto_convert : typing.Optional[bool]
            Whether to auto convert the Studio project to audio or not.

        auto_assign_voices : typing.Optional[bool]
            [Alpha Feature] Whether automatically assign voices to phrases in the create Project.

        source_type : typing.Optional[ProjectsCreateRequestSourceType]
            The type of Studio project to create.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddProjectResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.create(
                name="name",
                default_title_voice_id="default_title_voice_id",
                default_paragraph_voice_id="default_paragraph_voice_id",
                default_model_id="default_model_id",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            name=name,
            default_title_voice_id=default_title_voice_id,
            default_paragraph_voice_id=default_paragraph_voice_id,
            default_model_id=default_model_id,
            from_url=from_url,
            from_document=from_document,
            quality_preset=quality_preset,
            title=title,
            author=author,
            description=description,
            genres=genres,
            target_audience=target_audience,
            language=language,
            content_type=content_type,
            original_publication_date=original_publication_date,
            mature_content=mature_content,
            isbn_number=isbn_number,
            acx_volume_normalization=acx_volume_normalization,
            volume_normalization=volume_normalization,
            pronunciation_dictionary_locators=pronunciation_dictionary_locators,
            callback_url=callback_url,
            fiction=fiction,
            apply_text_normalization=apply_text_normalization,
            auto_convert=auto_convert,
            auto_assign_voices=auto_assign_voices,
            source_type=source_type,
            request_options=request_options,
        )
        return _response.data

    async def get(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ProjectExtendedResponse:
        """
        Returns information about a specific Studio project. This endpoint returns more detailed information about a project than `GET /v1/studio`.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ProjectExtendedResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.get(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(project_id, request_options=request_options)
        return _response.data

    async def update(
        self,
        project_id: str,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EditProjectResponseModel:
        """
        Updates the specified Studio project by setting the values of the parameters passed.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditProjectResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.update(
                project_id="21m00Tcm4TlvDq8ikWAM",
                name="Project 1",
                default_title_voice_id="21m00Tcm4TlvDq8ikWAM",
                default_paragraph_voice_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(
            project_id,
            name=name,
            default_title_voice_id=default_title_voice_id,
            default_paragraph_voice_id=default_paragraph_voice_id,
            title=title,
            author=author,
            isbn_number=isbn_number,
            volume_normalization=volume_normalization,
            request_options=request_options,
        )
        return _response.data

    async def delete(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteProjectResponseModel:
        """
        Deletes a Studio project.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteProjectResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.delete(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(project_id, request_options=request_options)
        return _response.data

    async def convert(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ConvertProjectResponseModel:
        """
        Starts conversion of a Studio project and all of its chapters.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ConvertProjectResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.convert(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.convert(project_id, request_options=request_options)
        return _response.data
