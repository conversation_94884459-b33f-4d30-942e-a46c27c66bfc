# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import JSONDecodeError

from ... import core
from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ...core.http_response import AsyncHttpR<PERSON>ponse, HttpResponse
from ...core.jsonable_encoder import jsonable_encoder
from ...core.request_options import RequestOptions
from ...core.unchecked_base_model import construct_type
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.add_project_response_model import AddProjectResponseModel
from ...types.convert_project_response_model import ConvertProjectResponseModel
from ...types.delete_project_response_model import DeleteProjectResponseModel
from ...types.edit_project_response_model import EditProjectResponseModel
from ...types.get_projects_response import GetProjectsResponse
from ...types.http_validation_error import HttpValidationError
from ...types.project_extended_response import ProjectExtendedResponse
from .types.projects_create_request_apply_text_normalization import ProjectsCreateRequestApplyTextNormalization
from .types.projects_create_request_fiction import ProjectsCreateRequestFiction
from .types.projects_create_request_source_type import ProjectsCreateRequestSourceType
from .types.projects_create_request_target_audience import ProjectsCreateRequestTargetAudience

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawProjectsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def list(self, *, request_options: typing.Optional[RequestOptions] = None) -> HttpResponse[GetProjectsResponse]:
        """
        Returns a list of your Studio projects with metadata.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[GetProjectsResponse]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/studio/projects",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetProjectsResponse,
                    construct_type(
                        type_=GetProjectsResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def create(
        self,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        default_model_id: str,
        from_url: typing.Optional[str] = OMIT,
        from_document: typing.Optional[core.File] = OMIT,
        quality_preset: typing.Optional[str] = OMIT,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        genres: typing.Optional[typing.List[str]] = OMIT,
        target_audience: typing.Optional[ProjectsCreateRequestTargetAudience] = OMIT,
        language: typing.Optional[str] = OMIT,
        content_type: typing.Optional[str] = OMIT,
        original_publication_date: typing.Optional[str] = OMIT,
        mature_content: typing.Optional[bool] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        acx_volume_normalization: typing.Optional[bool] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[typing.List[str]] = OMIT,
        callback_url: typing.Optional[str] = OMIT,
        fiction: typing.Optional[ProjectsCreateRequestFiction] = OMIT,
        apply_text_normalization: typing.Optional[ProjectsCreateRequestApplyTextNormalization] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        auto_assign_voices: typing.Optional[bool] = OMIT,
        source_type: typing.Optional[ProjectsCreateRequestSourceType] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[AddProjectResponseModel]:
        """
        Creates a new Studio project, it can be either initialized as blank, from a document or from a URL.

        Parameters
        ----------
        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        default_model_id : str
            The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        from_document : typing.Optional[core.File]
            See core.File for more documentation

        quality_preset : typing.Optional[str]
            Output quality of the generated audio. Must be one of:
            standard - standard output format, 128kbps with 44.1kHz sample rate.
            high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
            ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
            ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        description : typing.Optional[str]
            An optional description of the Studio project.

        genres : typing.Optional[typing.List[str]]
            An optional list of genres associated with the Studio project.

        target_audience : typing.Optional[ProjectsCreateRequestTargetAudience]
            An optional target audience of the Studio project.

        language : typing.Optional[str]
            An optional language of the Studio project. Two-letter language code (ISO 639-1).

        content_type : typing.Optional[str]
            An optional content type of the Studio project.

        original_publication_date : typing.Optional[str]
            An optional original publication date of the Studio project, in the format YYYY-MM-DD or YYYY.

        mature_content : typing.Optional[bool]
            An optional specification of whether this Studio project contains mature content.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        acx_volume_normalization : typing.Optional[bool]
            [Deprecated] When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        pronunciation_dictionary_locators : typing.Optional[typing.List[str]]
            A list of pronunciation dictionary locators (pronunciation_dictionary_id, version_id) encoded as a list of JSON strings for pronunciation dictionaries to be applied to the text. A list of json encoded strings is required as adding projects may occur through formData as opposed to jsonBody. To specify multiple dictionaries use multiple --form lines in your curl, such as --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"' --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'. Note that multiple dictionaries are not currently supported by our UI which will only show the first.

        callback_url : typing.Optional[str]
            A url that will be called by our service when the Studio project is converted. Request will contain a json blob containing the status of the conversion

        fiction : typing.Optional[ProjectsCreateRequestFiction]
            An optional specification of whether the content of this Studio project is fiction.

        apply_text_normalization : typing.Optional[ProjectsCreateRequestApplyTextNormalization]

                This parameter controls text normalization with four modes: 'auto', 'on', 'apply_english' and 'off'.
                When set to 'auto', the system will automatically decide whether to apply text normalization
                (e.g., spelling out numbers). With 'on', text normalization will always be applied, while
                with 'off', it will be skipped. 'apply_english' is the same as 'on' but will assume that text is in English.


        auto_convert : typing.Optional[bool]
            Whether to auto convert the Studio project to audio or not.

        auto_assign_voices : typing.Optional[bool]
            [Alpha Feature] Whether automatically assign voices to phrases in the create Project.

        source_type : typing.Optional[ProjectsCreateRequestSourceType]
            The type of Studio project to create.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[AddProjectResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/studio/projects",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
                "default_title_voice_id": default_title_voice_id,
                "default_paragraph_voice_id": default_paragraph_voice_id,
                "default_model_id": default_model_id,
                "from_url": from_url,
                "quality_preset": quality_preset,
                "title": title,
                "author": author,
                "description": description,
                "genres": genres,
                "target_audience": target_audience,
                "language": language,
                "content_type": content_type,
                "original_publication_date": original_publication_date,
                "mature_content": mature_content,
                "isbn_number": isbn_number,
                "acx_volume_normalization": acx_volume_normalization,
                "volume_normalization": volume_normalization,
                "pronunciation_dictionary_locators": pronunciation_dictionary_locators,
                "callback_url": callback_url,
                "fiction": fiction,
                "apply_text_normalization": apply_text_normalization,
                "auto_convert": auto_convert,
                "auto_assign_voices": auto_assign_voices,
                "source_type": source_type,
            },
            files={
                **({"from_document": from_document} if from_document is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddProjectResponseModel,
                    construct_type(
                        type_=AddProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def get(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[ProjectExtendedResponse]:
        """
        Returns information about a specific Studio project. This endpoint returns more detailed information about a project than `GET /v1/studio`.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[ProjectExtendedResponse]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/studio/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    ProjectExtendedResponse,
                    construct_type(
                        type_=ProjectExtendedResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def update(
        self,
        project_id: str,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[EditProjectResponseModel]:
        """
        Updates the specified Studio project by setting the values of the parameters passed.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[EditProjectResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/studio/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "name": name,
                "default_title_voice_id": default_title_voice_id,
                "default_paragraph_voice_id": default_paragraph_voice_id,
                "title": title,
                "author": author,
                "isbn_number": isbn_number,
                "volume_normalization": volume_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    EditProjectResponseModel,
                    construct_type(
                        type_=EditProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def delete(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[DeleteProjectResponseModel]:
        """
        Deletes a Studio project.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[DeleteProjectResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/studio/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DeleteProjectResponseModel,
                    construct_type(
                        type_=DeleteProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def convert(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[ConvertProjectResponseModel]:
        """
        Starts conversion of a Studio project and all of its chapters.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[ConvertProjectResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/studio/projects/{jsonable_encoder(project_id)}/convert",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    ConvertProjectResponseModel,
                    construct_type(
                        type_=ConvertProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawProjectsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def list(
        self, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[GetProjectsResponse]:
        """
        Returns a list of your Studio projects with metadata.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[GetProjectsResponse]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/studio/projects",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    GetProjectsResponse,
                    construct_type(
                        type_=GetProjectsResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def create(
        self,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        default_model_id: str,
        from_url: typing.Optional[str] = OMIT,
        from_document: typing.Optional[core.File] = OMIT,
        quality_preset: typing.Optional[str] = OMIT,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        genres: typing.Optional[typing.List[str]] = OMIT,
        target_audience: typing.Optional[ProjectsCreateRequestTargetAudience] = OMIT,
        language: typing.Optional[str] = OMIT,
        content_type: typing.Optional[str] = OMIT,
        original_publication_date: typing.Optional[str] = OMIT,
        mature_content: typing.Optional[bool] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        acx_volume_normalization: typing.Optional[bool] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[typing.List[str]] = OMIT,
        callback_url: typing.Optional[str] = OMIT,
        fiction: typing.Optional[ProjectsCreateRequestFiction] = OMIT,
        apply_text_normalization: typing.Optional[ProjectsCreateRequestApplyTextNormalization] = OMIT,
        auto_convert: typing.Optional[bool] = OMIT,
        auto_assign_voices: typing.Optional[bool] = OMIT,
        source_type: typing.Optional[ProjectsCreateRequestSourceType] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[AddProjectResponseModel]:
        """
        Creates a new Studio project, it can be either initialized as blank, from a document or from a URL.

        Parameters
        ----------
        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        default_model_id : str
            The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        from_document : typing.Optional[core.File]
            See core.File for more documentation

        quality_preset : typing.Optional[str]
            Output quality of the generated audio. Must be one of:
            standard - standard output format, 128kbps with 44.1kHz sample rate.
            high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
            ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
            ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        description : typing.Optional[str]
            An optional description of the Studio project.

        genres : typing.Optional[typing.List[str]]
            An optional list of genres associated with the Studio project.

        target_audience : typing.Optional[ProjectsCreateRequestTargetAudience]
            An optional target audience of the Studio project.

        language : typing.Optional[str]
            An optional language of the Studio project. Two-letter language code (ISO 639-1).

        content_type : typing.Optional[str]
            An optional content type of the Studio project.

        original_publication_date : typing.Optional[str]
            An optional original publication date of the Studio project, in the format YYYY-MM-DD or YYYY.

        mature_content : typing.Optional[bool]
            An optional specification of whether this Studio project contains mature content.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        acx_volume_normalization : typing.Optional[bool]
            [Deprecated] When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        pronunciation_dictionary_locators : typing.Optional[typing.List[str]]
            A list of pronunciation dictionary locators (pronunciation_dictionary_id, version_id) encoded as a list of JSON strings for pronunciation dictionaries to be applied to the text. A list of json encoded strings is required as adding projects may occur through formData as opposed to jsonBody. To specify multiple dictionaries use multiple --form lines in your curl, such as --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"' --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'. Note that multiple dictionaries are not currently supported by our UI which will only show the first.

        callback_url : typing.Optional[str]
            A url that will be called by our service when the Studio project is converted. Request will contain a json blob containing the status of the conversion

        fiction : typing.Optional[ProjectsCreateRequestFiction]
            An optional specification of whether the content of this Studio project is fiction.

        apply_text_normalization : typing.Optional[ProjectsCreateRequestApplyTextNormalization]

                This parameter controls text normalization with four modes: 'auto', 'on', 'apply_english' and 'off'.
                When set to 'auto', the system will automatically decide whether to apply text normalization
                (e.g., spelling out numbers). With 'on', text normalization will always be applied, while
                with 'off', it will be skipped. 'apply_english' is the same as 'on' but will assume that text is in English.


        auto_convert : typing.Optional[bool]
            Whether to auto convert the Studio project to audio or not.

        auto_assign_voices : typing.Optional[bool]
            [Alpha Feature] Whether automatically assign voices to phrases in the create Project.

        source_type : typing.Optional[ProjectsCreateRequestSourceType]
            The type of Studio project to create.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[AddProjectResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/studio/projects",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
                "default_title_voice_id": default_title_voice_id,
                "default_paragraph_voice_id": default_paragraph_voice_id,
                "default_model_id": default_model_id,
                "from_url": from_url,
                "quality_preset": quality_preset,
                "title": title,
                "author": author,
                "description": description,
                "genres": genres,
                "target_audience": target_audience,
                "language": language,
                "content_type": content_type,
                "original_publication_date": original_publication_date,
                "mature_content": mature_content,
                "isbn_number": isbn_number,
                "acx_volume_normalization": acx_volume_normalization,
                "volume_normalization": volume_normalization,
                "pronunciation_dictionary_locators": pronunciation_dictionary_locators,
                "callback_url": callback_url,
                "fiction": fiction,
                "apply_text_normalization": apply_text_normalization,
                "auto_convert": auto_convert,
                "auto_assign_voices": auto_assign_voices,
                "source_type": source_type,
            },
            files={
                **({"from_document": from_document} if from_document is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddProjectResponseModel,
                    construct_type(
                        type_=AddProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def get(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[ProjectExtendedResponse]:
        """
        Returns information about a specific Studio project. This endpoint returns more detailed information about a project than `GET /v1/studio`.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[ProjectExtendedResponse]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/studio/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    ProjectExtendedResponse,
                    construct_type(
                        type_=ProjectExtendedResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def update(
        self,
        project_id: str,
        *,
        name: str,
        default_title_voice_id: str,
        default_paragraph_voice_id: str,
        title: typing.Optional[str] = OMIT,
        author: typing.Optional[str] = OMIT,
        isbn_number: typing.Optional[str] = OMIT,
        volume_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[EditProjectResponseModel]:
        """
        Updates the specified Studio project by setting the values of the parameters passed.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        name : str
            The name of the Studio project, used for identification only.

        default_title_voice_id : str
            The voice_id that corresponds to the default voice used for new titles.

        default_paragraph_voice_id : str
            The voice_id that corresponds to the default voice used for new paragraphs.

        title : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        author : typing.Optional[str]
            An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.

        isbn_number : typing.Optional[str]
            An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.

        volume_normalization : typing.Optional[bool]
            When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[EditProjectResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/studio/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "name": name,
                "default_title_voice_id": default_title_voice_id,
                "default_paragraph_voice_id": default_paragraph_voice_id,
                "title": title,
                "author": author,
                "isbn_number": isbn_number,
                "volume_normalization": volume_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    EditProjectResponseModel,
                    construct_type(
                        type_=EditProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def delete(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[DeleteProjectResponseModel]:
        """
        Deletes a Studio project.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[DeleteProjectResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/studio/projects/{jsonable_encoder(project_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DeleteProjectResponseModel,
                    construct_type(
                        type_=DeleteProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def convert(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[ConvertProjectResponseModel]:
        """
        Starts conversion of a Studio project and all of its chapters.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[ConvertProjectResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/studio/projects/{jsonable_encoder(project_id)}/convert",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    ConvertProjectResponseModel,
                    construct_type(
                        type_=ConvertProjectResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
