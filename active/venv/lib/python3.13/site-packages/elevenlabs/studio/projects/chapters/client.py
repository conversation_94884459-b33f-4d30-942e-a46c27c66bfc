# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import <PERSON>ync<PERSON><PERSON><PERSON>rap<PERSON>, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.add_chapter_response_model import AddChapterResponseModel
from ....types.chapter_content_input_model import Chapter<PERSON><PERSON><PERSON>InputModel
from ....types.chapter_with_content_response_model import Chapter<PERSON><PERSON><PERSON>ontentResponseModel
from ....types.convert_chapter_response_model import ConvertChapterResponseModel
from ....types.delete_chapter_response_model import DeleteChapterResponseModel
from ....types.edit_chapter_response_model import EditChapterResponseModel
from ....types.get_chapters_response import GetChaptersResponse
from .raw_client import AsyncRawChaptersClient, RawChaptersClient
from .snapshots.client import AsyncSnapshotsClient, SnapshotsClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ChaptersClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawChaptersClient(client_wrapper=client_wrapper)
        self.snapshots = SnapshotsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawChaptersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawChaptersClient
        """
        return self._raw_client

    def list(self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> GetChaptersResponse:
        """
        Returns a list of a Studio project's chapters.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetChaptersResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.chapters.list(
            project_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.list(project_id, request_options=request_options)
        return _response.data

    def create(
        self,
        project_id: str,
        *,
        name: str,
        from_url: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddChapterResponseModel:
        """
        Creates a new chapter either as blank or from a URL.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        name : str
            The name of the chapter, used for identification only.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddChapterResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.chapters.create(
            project_id="21m00Tcm4TlvDq8ikWAM",
            name="Chapter 1",
        )
        """
        _response = self._raw_client.create(project_id, name=name, from_url=from_url, request_options=request_options)
        return _response.data

    def get(
        self, project_id: str, chapter_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ChapterWithContentResponseModel:
        """
        Returns information about a specific chapter.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ChapterWithContentResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.chapters.get(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get(project_id, chapter_id, request_options=request_options)
        return _response.data

    def update(
        self,
        project_id: str,
        chapter_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        content: typing.Optional[ChapterContentInputModel] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EditChapterResponseModel:
        """
        Updates a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        name : typing.Optional[str]
            The name of the chapter, used for identification only.

        content : typing.Optional[ChapterContentInputModel]
            The chapter content to use.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditChapterResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.chapters.update(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.update(
            project_id, chapter_id, name=name, content=content, request_options=request_options
        )
        return _response.data

    def delete(
        self, project_id: str, chapter_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteChapterResponseModel:
        """
        Deletes a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteChapterResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.chapters.delete(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.delete(project_id, chapter_id, request_options=request_options)
        return _response.data

    def convert(
        self, project_id: str, chapter_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ConvertChapterResponseModel:
        """
        Starts conversion of a specific chapter.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ConvertChapterResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.chapters.convert(
            project_id="21m00Tcm4TlvDq8ikWAM",
            chapter_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.convert(project_id, chapter_id, request_options=request_options)
        return _response.data


class AsyncChaptersClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawChaptersClient(client_wrapper=client_wrapper)
        self.snapshots = AsyncSnapshotsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawChaptersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawChaptersClient
        """
        return self._raw_client

    async def list(
        self, project_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> GetChaptersResponse:
        """
        Returns a list of a Studio project's chapters.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        GetChaptersResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.chapters.list(
                project_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.list(project_id, request_options=request_options)
        return _response.data

    async def create(
        self,
        project_id: str,
        *,
        name: str,
        from_url: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddChapterResponseModel:
        """
        Creates a new chapter either as blank or from a URL.

        Parameters
        ----------
        project_id : str
            The ID of the Studio project.

        name : str
            The name of the chapter, used for identification only.

        from_url : typing.Optional[str]
            An optional URL from which we will extract content to initialize the Studio project. If this is set, 'from_url' must be null. If neither 'from_url' or 'from_document' are provided we will initialize the Studio project as blank.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddChapterResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.chapters.create(
                project_id="21m00Tcm4TlvDq8ikWAM",
                name="Chapter 1",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            project_id, name=name, from_url=from_url, request_options=request_options
        )
        return _response.data

    async def get(
        self, project_id: str, chapter_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ChapterWithContentResponseModel:
        """
        Returns information about a specific chapter.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ChapterWithContentResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.chapters.get(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(project_id, chapter_id, request_options=request_options)
        return _response.data

    async def update(
        self,
        project_id: str,
        chapter_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        content: typing.Optional[ChapterContentInputModel] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EditChapterResponseModel:
        """
        Updates a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        name : typing.Optional[str]
            The name of the chapter, used for identification only.

        content : typing.Optional[ChapterContentInputModel]
            The chapter content to use.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditChapterResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.chapters.update(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(
            project_id, chapter_id, name=name, content=content, request_options=request_options
        )
        return _response.data

    async def delete(
        self, project_id: str, chapter_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteChapterResponseModel:
        """
        Deletes a chapter.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteChapterResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.chapters.delete(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(project_id, chapter_id, request_options=request_options)
        return _response.data

    async def convert(
        self, project_id: str, chapter_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> ConvertChapterResponseModel:
        """
        Starts conversion of a specific chapter.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        chapter_id : str
            The ID of the chapter to be used. You can use the [List project chapters](/docs/api-reference/studio/get-chapters) endpoint to list all the available chapters.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        ConvertChapterResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.chapters.convert(
                project_id="21m00Tcm4TlvDq8ikWAM",
                chapter_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.convert(project_id, chapter_id, request_options=request_options)
        return _response.data
