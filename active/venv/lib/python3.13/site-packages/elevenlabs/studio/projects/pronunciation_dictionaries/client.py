# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.create_pronunciation_dictionary_response_model import CreatePronunciationDictionaryResponseModel
from ....types.pronunciation_dictionary_version_locator import PronunciationDictionaryVersionLocator
from .raw_client import AsyncRawPronunciationDictionariesClient, RawPronunciationDictionariesClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class PronunciationDictionariesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawPronunciationDictionariesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawPronunciationDictionariesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawPronunciationDictionariesClient
        """
        return self._raw_client

    def create(
        self,
        project_id: str,
        *,
        pronunciation_dictionary_locators: typing.Sequence[PronunciationDictionaryVersionLocator],
        invalidate_affected_text: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> CreatePronunciationDictionaryResponseModel:
        """
        Create a set of pronunciation dictionaries acting on a project. This will automatically mark text within this project as requiring reconverting where the new dictionary would apply or the old one no longer does.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        pronunciation_dictionary_locators : typing.Sequence[PronunciationDictionaryVersionLocator]
            A list of pronunciation dictionary locators (pronunciation_dictionary_id, version_id) encoded as a list of JSON strings for pronunciation dictionaries to be applied to the text. A list of json encoded strings is required as adding projects may occur through formData as opposed to jsonBody. To specify multiple dictionaries use multiple --form lines in your curl, such as --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"' --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'. Note that multiple dictionaries are not currently supported by our UI which will only show the first.

        invalidate_affected_text : typing.Optional[bool]
            This will automatically mark text in this project for reconversion when the new dictionary applies or the old one no longer does.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreatePronunciationDictionaryResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs, PronunciationDictionaryVersionLocator

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.studio.projects.pronunciation_dictionaries.create(
            project_id="21m00Tcm4TlvDq8ikWAM",
            pronunciation_dictionary_locators=[
                PronunciationDictionaryVersionLocator(
                    pronunciation_dictionary_id="pronunciation_dictionary_id",
                )
            ],
        )
        """
        _response = self._raw_client.create(
            project_id,
            pronunciation_dictionary_locators=pronunciation_dictionary_locators,
            invalidate_affected_text=invalidate_affected_text,
            request_options=request_options,
        )
        return _response.data


class AsyncPronunciationDictionariesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawPronunciationDictionariesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawPronunciationDictionariesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawPronunciationDictionariesClient
        """
        return self._raw_client

    async def create(
        self,
        project_id: str,
        *,
        pronunciation_dictionary_locators: typing.Sequence[PronunciationDictionaryVersionLocator],
        invalidate_affected_text: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> CreatePronunciationDictionaryResponseModel:
        """
        Create a set of pronunciation dictionaries acting on a project. This will automatically mark text within this project as requiring reconverting where the new dictionary would apply or the old one no longer does.

        Parameters
        ----------
        project_id : str
            The ID of the project to be used. You can use the [List projects](/docs/api-reference/studio/get-projects) endpoint to list all the available projects.

        pronunciation_dictionary_locators : typing.Sequence[PronunciationDictionaryVersionLocator]
            A list of pronunciation dictionary locators (pronunciation_dictionary_id, version_id) encoded as a list of JSON strings for pronunciation dictionaries to be applied to the text. A list of json encoded strings is required as adding projects may occur through formData as opposed to jsonBody. To specify multiple dictionaries use multiple --form lines in your curl, such as --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"' --form 'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'. Note that multiple dictionaries are not currently supported by our UI which will only show the first.

        invalidate_affected_text : typing.Optional[bool]
            This will automatically mark text in this project for reconversion when the new dictionary applies or the old one no longer does.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        CreatePronunciationDictionaryResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs, PronunciationDictionaryVersionLocator

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.studio.projects.pronunciation_dictionaries.create(
                project_id="21m00Tcm4TlvDq8ikWAM",
                pronunciation_dictionary_locators=[
                    PronunciationDictionaryVersionLocator(
                        pronunciation_dictionary_id="pronunciation_dictionary_id",
                    )
                ],
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            project_id,
            pronunciation_dictionary_locators=pronunciation_dictionary_locators,
            invalidate_affected_text=invalidate_affected_text,
            request_options=request_options,
        )
        return _response.data
