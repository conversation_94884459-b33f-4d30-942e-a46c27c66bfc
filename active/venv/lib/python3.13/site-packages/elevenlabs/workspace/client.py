# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from ..core.client_wrapper import Async<PERSON><PERSON>Wrapper, SyncClientWrapper
from .groups.client import AsyncGroupsClient, GroupsClient
from .invites.client import AsyncInvites<PERSON>lient, InvitesClient
from .members.client import Async<PERSON><PERSON>bers<PERSON>lient, MembersClient
from .raw_client import AsyncRaw<PERSON>paceClient, RawWorkspaceClient
from .resources.client import AsyncResourcesClient, ResourcesClient


class WorkspaceClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawWorkspaceClient(client_wrapper=client_wrapper)
        self.groups = GroupsClient(client_wrapper=client_wrapper)

        self.invites = InvitesClient(client_wrapper=client_wrapper)

        self.members = MembersClient(client_wrapper=client_wrapper)

        self.resources = ResourcesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawWorkspaceClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawWorkspaceClient
        """
        return self._raw_client


class AsyncWorkspaceClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawWorkspaceClient(client_wrapper=client_wrapper)
        self.groups = AsyncGroupsClient(client_wrapper=client_wrapper)

        self.invites = AsyncInvitesClient(client_wrapper=client_wrapper)

        self.members = AsyncMembersClient(client_wrapper=client_wrapper)

        self.resources = AsyncResourcesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawWorkspaceClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawWorkspaceClient
        """
        return self._raw_client
