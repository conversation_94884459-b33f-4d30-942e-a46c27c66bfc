# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.pronunciation_dictionary_rules_response_model import PronunciationDictionaryRulesResponseModel
from .raw_client import AsyncRawRulesClient, RawRulesClient
from .types.pronunciation_dictionary_rule import PronunciationDictionaryRule

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RulesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawRulesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawRulesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawRulesClient
        """
        return self._raw_client

    def add(
        self,
        pronunciation_dictionary_id: str,
        *,
        rules: typing.Sequence[PronunciationDictionaryRule],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PronunciationDictionaryRulesResponseModel:
        """
        Add rules to the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rules : typing.Sequence[PronunciationDictionaryRule]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PronunciationDictionaryRulesResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs
        from elevenlabs.pronunciation_dictionaries.rules import (
            PronunciationDictionaryRule_Alias,
        )

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionaries.rules.add(
            pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
            rules=[
                PronunciationDictionaryRule_Alias(
                    string_to_replace="Thailand",
                    alias="tie-land",
                )
            ],
        )
        """
        _response = self._raw_client.add(pronunciation_dictionary_id, rules=rules, request_options=request_options)
        return _response.data

    def remove(
        self,
        pronunciation_dictionary_id: str,
        *,
        rule_strings: typing.Sequence[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PronunciationDictionaryRulesResponseModel:
        """
        Remove rules from the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rule_strings : typing.Sequence[str]
            List of strings to remove from the pronunciation dictionary.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PronunciationDictionaryRulesResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.pronunciation_dictionaries.rules.remove(
            pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
            rule_strings=["rule_strings"],
        )
        """
        _response = self._raw_client.remove(
            pronunciation_dictionary_id, rule_strings=rule_strings, request_options=request_options
        )
        return _response.data


class AsyncRulesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawRulesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawRulesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawRulesClient
        """
        return self._raw_client

    async def add(
        self,
        pronunciation_dictionary_id: str,
        *,
        rules: typing.Sequence[PronunciationDictionaryRule],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PronunciationDictionaryRulesResponseModel:
        """
        Add rules to the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rules : typing.Sequence[PronunciationDictionaryRule]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PronunciationDictionaryRulesResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs
        from elevenlabs.pronunciation_dictionaries.rules import (
            PronunciationDictionaryRule_Alias,
        )

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionaries.rules.add(
                pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
                rules=[
                    PronunciationDictionaryRule_Alias(
                        string_to_replace="Thailand",
                        alias="tie-land",
                    )
                ],
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.add(
            pronunciation_dictionary_id, rules=rules, request_options=request_options
        )
        return _response.data

    async def remove(
        self,
        pronunciation_dictionary_id: str,
        *,
        rule_strings: typing.Sequence[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> PronunciationDictionaryRulesResponseModel:
        """
        Remove rules from the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rule_strings : typing.Sequence[str]
            List of strings to remove from the pronunciation dictionary.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        PronunciationDictionaryRulesResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.pronunciation_dictionaries.rules.remove(
                pronunciation_dictionary_id="21m00Tcm4TlvDq8ikWAM",
                rule_strings=["rule_strings"],
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.remove(
            pronunciation_dictionary_id, rule_strings=rule_strings, request_options=request_options
        )
        return _response.data
