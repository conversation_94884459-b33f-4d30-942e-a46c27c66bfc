# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import JSONDecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ...core.http_response import AsyncHttpResponse, HttpResponse
from ...core.jsonable_encoder import jsonable_encoder
from ...core.request_options import RequestOptions
from ...core.serialization import convert_and_respect_annotation_metadata
from ...core.unchecked_base_model import construct_type
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.http_validation_error import HttpValidationError
from ...types.pronunciation_dictionary_rules_response_model import PronunciationDictionaryRulesResponseModel
from .types.pronunciation_dictionary_rule import PronunciationDictionaryRule

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawRulesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def add(
        self,
        pronunciation_dictionary_id: str,
        *,
        rules: typing.Sequence[PronunciationDictionaryRule],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[PronunciationDictionaryRulesResponseModel]:
        """
        Add rules to the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rules : typing.Sequence[PronunciationDictionaryRule]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[PronunciationDictionaryRulesResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}/add-rules",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "rules": convert_and_respect_annotation_metadata(
                    object_=rules, annotation=typing.Sequence[PronunciationDictionaryRule], direction="write"
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    PronunciationDictionaryRulesResponseModel,
                    construct_type(
                        type_=PronunciationDictionaryRulesResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def remove(
        self,
        pronunciation_dictionary_id: str,
        *,
        rule_strings: typing.Sequence[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[PronunciationDictionaryRulesResponseModel]:
        """
        Remove rules from the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rule_strings : typing.Sequence[str]
            List of strings to remove from the pronunciation dictionary.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[PronunciationDictionaryRulesResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}/remove-rules",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "rule_strings": rule_strings,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    PronunciationDictionaryRulesResponseModel,
                    construct_type(
                        type_=PronunciationDictionaryRulesResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawRulesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def add(
        self,
        pronunciation_dictionary_id: str,
        *,
        rules: typing.Sequence[PronunciationDictionaryRule],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[PronunciationDictionaryRulesResponseModel]:
        """
        Add rules to the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rules : typing.Sequence[PronunciationDictionaryRule]
            List of pronunciation rules. Rule can be either:
                an alias rule: {'string_to_replace': 'a', 'type': 'alias', 'alias': 'b', }
                or a phoneme rule: {'string_to_replace': 'a', 'type': 'phoneme', 'phoneme': 'b', 'alphabet': 'ipa' }

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[PronunciationDictionaryRulesResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}/add-rules",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "rules": convert_and_respect_annotation_metadata(
                    object_=rules, annotation=typing.Sequence[PronunciationDictionaryRule], direction="write"
                ),
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    PronunciationDictionaryRulesResponseModel,
                    construct_type(
                        type_=PronunciationDictionaryRulesResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def remove(
        self,
        pronunciation_dictionary_id: str,
        *,
        rule_strings: typing.Sequence[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[PronunciationDictionaryRulesResponseModel]:
        """
        Remove rules from the pronunciation dictionary

        Parameters
        ----------
        pronunciation_dictionary_id : str
            The id of the pronunciation dictionary

        rule_strings : typing.Sequence[str]
            List of strings to remove from the pronunciation dictionary.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[PronunciationDictionaryRulesResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/pronunciation-dictionaries/{jsonable_encoder(pronunciation_dictionary_id)}/remove-rules",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "rule_strings": rule_strings,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    PronunciationDictionaryRulesResponseModel,
                    construct_type(
                        type_=PronunciationDictionaryRulesResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
