# This file was auto-generated by <PERSON><PERSON> from our API Definition.

# isort: skip_file

from .types import (
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Phoneme,
    BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess,
    PronunciationDictionariesCreateFromFileRequestWorkspaceAccess,
    PronunciationDictionariesListRequestSort,
)
from . import rules
from .rules import PronunciationDictionaryRule, PronunciationDictionaryRule_Alias, PronunciationDictionaryRule_Phoneme

__all__ = [
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Alias",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostRulesItem_Phoneme",
    "BodyAddAPronunciationDictionaryV1PronunciationDictionariesAddFromRulesPostWorkspaceAccess",
    "PronunciationDictionariesCreateFromFileRequestWorkspaceAccess",
    "PronunciationDictionariesListRequestSort",
    "PronunciationDictionaryRule",
    "PronunciationDictionaryRule_Alias",
    "PronunciationDictionaryRule_Phoneme",
    "rules",
]
