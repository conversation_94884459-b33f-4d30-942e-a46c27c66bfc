# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..core.client_wrapper import As<PERSON><PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.voice import Voice
from ..types.voice_design_preview_response import VoiceDesignPreviewResponse
from .raw_client import AsyncRawTextToVoiceClient, RawTextToVoiceClient
from .types.text_to_voice_create_previews_request_output_format import TextToVoiceCreatePreviewsRequestOutputFormat

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class TextToVoiceClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawTextToVoiceClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawTextToVoiceClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawTextToVoiceClient
        """
        return self._raw_client

    def create_previews(
        self,
        *,
        voice_description: str,
        output_format: typing.Optional[TextToVoiceCreatePreviewsRequestOutputFormat] = None,
        text: typing.Optional[str] = OMIT,
        auto_generate_text: typing.Optional[bool] = OMIT,
        loudness: typing.Optional[float] = OMIT,
        quality: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        guidance_scale: typing.Optional[float] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> VoiceDesignPreviewResponse:
        """
        Create a voice from a text prompt.

        Parameters
        ----------
        voice_description : str
            Description to use for the created voice.

        output_format : typing.Optional[TextToVoiceCreatePreviewsRequestOutputFormat]
            The output format of the generated audio.

        text : typing.Optional[str]
            Text to generate, text length has to be between 100 and 1000.

        auto_generate_text : typing.Optional[bool]
            Whether to automatically generate a text suitable for the voice description.

        loudness : typing.Optional[float]
            Controls the volume level of the generated voice. -1 is quietest, 1 is loudest, 0 corresponds to roughly -24 LUFS.

        quality : typing.Optional[float]
            Higher quality results in better voice output but less variety.

        seed : typing.Optional[int]
            Random number that controls the voice generation. Same seed with same inputs produces same voice.

        guidance_scale : typing.Optional[float]
            Controls how closely the AI follows the prompt. Lower numbers give the AI more freedom to be creative, while higher numbers force it to stick more to the prompt. High numbers can cause voice to sound artificial or robotic. We recommend to use longer, more detailed prompts at lower Guidance Scale.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceDesignPreviewResponse
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.text_to_voice.create_previews(
            voice_description="A sassy squeaky mouse",
        )
        """
        _response = self._raw_client.create_previews(
            voice_description=voice_description,
            output_format=output_format,
            text=text,
            auto_generate_text=auto_generate_text,
            loudness=loudness,
            quality=quality,
            seed=seed,
            guidance_scale=guidance_scale,
            request_options=request_options,
        )
        return _response.data

    def create_voice_from_preview(
        self,
        *,
        voice_name: str,
        voice_description: str,
        generated_voice_id: str,
        labels: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        played_not_selected_voice_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Voice:
        """
        Add a generated voice to the voice library.

        Parameters
        ----------
        voice_name : str
            Name to use for the created voice.

        voice_description : str
            Description to use for the created voice.

        generated_voice_id : str
            The generated_voice_id to create, call POST /v1/text-to-voice/create-previews and fetch the generated_voice_id from the response header if don't have one yet.

        labels : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            Optional, metadata to add to the created voice. Defaults to None.

        played_not_selected_voice_ids : typing.Optional[typing.Sequence[str]]
            List of voice ids that the user has played but not selected. Used for RLHF.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Voice
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.text_to_voice.create_voice_from_preview(
            voice_name="Sassy squeaky mouse",
            voice_description="A sassy squeaky mouse",
            generated_voice_id="37HceQefKmEi3bGovXjL",
        )
        """
        _response = self._raw_client.create_voice_from_preview(
            voice_name=voice_name,
            voice_description=voice_description,
            generated_voice_id=generated_voice_id,
            labels=labels,
            played_not_selected_voice_ids=played_not_selected_voice_ids,
            request_options=request_options,
        )
        return _response.data


class AsyncTextToVoiceClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawTextToVoiceClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawTextToVoiceClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawTextToVoiceClient
        """
        return self._raw_client

    async def create_previews(
        self,
        *,
        voice_description: str,
        output_format: typing.Optional[TextToVoiceCreatePreviewsRequestOutputFormat] = None,
        text: typing.Optional[str] = OMIT,
        auto_generate_text: typing.Optional[bool] = OMIT,
        loudness: typing.Optional[float] = OMIT,
        quality: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        guidance_scale: typing.Optional[float] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> VoiceDesignPreviewResponse:
        """
        Create a voice from a text prompt.

        Parameters
        ----------
        voice_description : str
            Description to use for the created voice.

        output_format : typing.Optional[TextToVoiceCreatePreviewsRequestOutputFormat]
            The output format of the generated audio.

        text : typing.Optional[str]
            Text to generate, text length has to be between 100 and 1000.

        auto_generate_text : typing.Optional[bool]
            Whether to automatically generate a text suitable for the voice description.

        loudness : typing.Optional[float]
            Controls the volume level of the generated voice. -1 is quietest, 1 is loudest, 0 corresponds to roughly -24 LUFS.

        quality : typing.Optional[float]
            Higher quality results in better voice output but less variety.

        seed : typing.Optional[int]
            Random number that controls the voice generation. Same seed with same inputs produces same voice.

        guidance_scale : typing.Optional[float]
            Controls how closely the AI follows the prompt. Lower numbers give the AI more freedom to be creative, while higher numbers force it to stick more to the prompt. High numbers can cause voice to sound artificial or robotic. We recommend to use longer, more detailed prompts at lower Guidance Scale.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceDesignPreviewResponse
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.text_to_voice.create_previews(
                voice_description="A sassy squeaky mouse",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create_previews(
            voice_description=voice_description,
            output_format=output_format,
            text=text,
            auto_generate_text=auto_generate_text,
            loudness=loudness,
            quality=quality,
            seed=seed,
            guidance_scale=guidance_scale,
            request_options=request_options,
        )
        return _response.data

    async def create_voice_from_preview(
        self,
        *,
        voice_name: str,
        voice_description: str,
        generated_voice_id: str,
        labels: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        played_not_selected_voice_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Voice:
        """
        Add a generated voice to the voice library.

        Parameters
        ----------
        voice_name : str
            Name to use for the created voice.

        voice_description : str
            Description to use for the created voice.

        generated_voice_id : str
            The generated_voice_id to create, call POST /v1/text-to-voice/create-previews and fetch the generated_voice_id from the response header if don't have one yet.

        labels : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            Optional, metadata to add to the created voice. Defaults to None.

        played_not_selected_voice_ids : typing.Optional[typing.Sequence[str]]
            List of voice ids that the user has played but not selected. Used for RLHF.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Voice
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.text_to_voice.create_voice_from_preview(
                voice_name="Sassy squeaky mouse",
                voice_description="A sassy squeaky mouse",
                generated_voice_id="37HceQefKmEi3bGovXjL",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create_voice_from_preview(
            voice_name=voice_name,
            voice_description=voice_description,
            generated_voice_id=generated_voice_id,
            labels=labels,
            played_not_selected_voice_ids=played_not_selected_voice_ids,
            request_options=request_options,
        )
        return _response.data
