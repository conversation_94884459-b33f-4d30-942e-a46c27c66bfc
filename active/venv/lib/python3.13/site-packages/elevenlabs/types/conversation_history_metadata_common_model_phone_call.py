# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .conversation_history_sip_trunking_phone_call_model_direction import (
    ConversationHistorySipTrunkingPhoneCallModelDirection,
)
from .conversation_history_twilio_phone_call_model_direction import ConversationHistoryTwilioPhoneCallModelDirection


class ConversationHistoryMetadataCommonModelPhoneCall_SipTrunking(UncheckedBaseModel):
    type: typing.Literal["sip_trunking"] = "sip_trunking"
    direction: ConversationHistorySipTrunkingPhoneCallModelDirection
    phone_number_id: str
    agent_number: str
    external_number: str
    call_sid: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class ConversationHistoryMetadataCommonModelPhoneCall_Twilio(UncheckedBaseModel):
    type: typing.Literal["twilio"] = "twilio"
    direction: ConversationHistoryTwilioPhoneCallModelDirection
    phone_number_id: str
    agent_number: str
    external_number: str
    stream_sid: str
    call_sid: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


ConversationHistoryMetadataCommonModelPhoneCall = typing_extensions.Annotated[
    typing.Union[
        ConversationHistoryMetadataCommonModelPhoneCall_SipTrunking,
        ConversationHistoryMetadataCommonModelPhoneCall_Twilio,
    ],
    UnionMetadata(discriminant="type"),
]
