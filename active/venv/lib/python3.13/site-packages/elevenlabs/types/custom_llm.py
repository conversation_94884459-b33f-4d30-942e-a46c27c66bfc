# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conv_ai_secret_locator import ConvAiSecretLocator


class CustomLlm(UncheckedBaseModel):
    url: str = pydantic.Field()
    """
    The URL of the Chat Completions compatible endpoint
    """

    model_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    The model ID to be used if URL serves multiple models
    """

    api_key: typing.Optional[ConvAiSecretLocator] = pydantic.Field(default=None)
    """
    The API key for authentication
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
