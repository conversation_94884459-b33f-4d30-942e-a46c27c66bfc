# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .language_response import LanguageResponse
from .model_rates_response_model import ModelRatesResponseModel
from .model_response_model_concurrency_group import ModelResponseModelConcurrencyGroup


class Model(UncheckedBaseModel):
    model_id: str = pydantic.Field()
    """
    The unique identifier of the model.
    """

    name: typing.Optional[str] = pydantic.Field(default=None)
    """
    The name of the model.
    """

    can_be_finetuned: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the model can be finetuned.
    """

    can_do_text_to_speech: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the model can do text-to-speech.
    """

    can_do_voice_conversion: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the model can do voice conversion.
    """

    can_use_style: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the model can use style.
    """

    can_use_speaker_boost: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the model can use speaker boost.
    """

    serves_pro_voices: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the model serves pro voices.
    """

    token_cost_factor: typing.Optional[float] = pydantic.Field(default=None)
    """
    The cost factor for the model.
    """

    description: typing.Optional[str] = pydantic.Field(default=None)
    """
    The description of the model.
    """

    requires_alpha_access: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the model requires alpha access.
    """

    max_characters_request_free_user: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum number of characters that can be requested by a free user.
    """

    max_characters_request_subscribed_user: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum number of characters that can be requested by a subscribed user.
    """

    maximum_text_length_per_request: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum length of text that can be requested for this model.
    """

    languages: typing.Optional[typing.List[LanguageResponse]] = pydantic.Field(default=None)
    """
    The languages supported by the model.
    """

    model_rates: typing.Optional[ModelRatesResponseModel] = pydantic.Field(default=None)
    """
    The rates for the model.
    """

    concurrency_group: typing.Optional[ModelResponseModelConcurrencyGroup] = pydantic.Field(default=None)
    """
    The concurrency group for the model.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
