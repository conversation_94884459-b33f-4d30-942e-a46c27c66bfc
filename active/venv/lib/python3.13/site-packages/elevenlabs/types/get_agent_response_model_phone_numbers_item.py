# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .phone_number_agent_info import PhoneNumberAgentInfo
from .sip_trunk_config_response_model import SipTrunkConfigResponseModel


class GetAgentResponseModelPhoneNumbersItem_SipTrunk(UncheckedBaseModel):
    provider: typing.Literal["sip_trunk"] = "sip_trunk"
    phone_number: str
    label: str
    phone_number_id: str
    assigned_agent: typing.Optional[PhoneNumberAgentInfo] = None
    provider_config: typing.Optional[SipTrunkConfigResponseModel] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class GetAgentResponseModelPhoneNumbersItem_Twilio(UncheckedBaseModel):
    provider: typing.Literal["twilio"] = "twilio"
    phone_number: str
    label: str
    phone_number_id: str
    assigned_agent: typing.Optional[PhoneNumberAgentInfo] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


GetAgentResponseModelPhoneNumbersItem = typing_extensions.Annotated[
    typing.Union[GetAgentResponseModelPhoneNumbersItem_SipTrunk, GetAgentResponseModelPhoneNumbersItem_Twilio],
    UnionMetadata(discriminant="provider"),
]
