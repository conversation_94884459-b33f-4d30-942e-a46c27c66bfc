# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class OrbAvatar(UncheckedBaseModel):
    color_1: typing.Optional[str] = pydantic.Field(default=None)
    """
    The first color of the avatar
    """

    color_2: typing.Optional[str] = pydantic.Field(default=None)
    """
    The second color of the avatar
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
