# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class ConversationHistoryTranscriptToolResultCommonModel(UncheckedBaseModel):
    type: typing.Optional[str] = None
    request_id: str
    tool_name: str
    result_value: str
    is_error: bool
    tool_has_been_called: bool
    tool_latency_secs: typing.Optional[float] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
