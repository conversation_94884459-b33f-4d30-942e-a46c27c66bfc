# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class RecordingResponse(UncheckedBaseModel):
    recording_id: str = pydantic.Field()
    """
    The ID of the recording.
    """

    mime_type: str = pydantic.Field()
    """
    The MIME type of the recording.
    """

    size_bytes: int = pydantic.Field()
    """
    The size of the recording in bytes.
    """

    upload_date_unix: int = pydantic.Field()
    """
    The date of the recording in Unix time.
    """

    transcription: str = pydantic.Field()
    """
    The transcription of the recording.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
