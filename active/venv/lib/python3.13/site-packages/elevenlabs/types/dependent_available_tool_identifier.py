# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .dependent_available_tool_identifier_access_level import DependentAvailableToolIdentifierAccessLevel


class DependentAvailableToolIdentifier(UncheckedBaseModel):
    id: str
    name: str
    created_at_unix_secs: int
    access_level: DependentAvailableToolIdentifierAccessLevel

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
