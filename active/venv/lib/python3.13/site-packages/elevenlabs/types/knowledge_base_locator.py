# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .document_usage_mode_enum import DocumentUsageModeEnum
from .knowledge_base_document_type import KnowledgeBaseDocumentType


class KnowledgeBaseLocator(UncheckedBaseModel):
    type: KnowledgeBaseDocumentType = pydantic.Field()
    """
    The type of the knowledge base
    """

    name: str = pydantic.Field()
    """
    The name of the knowledge base
    """

    id: str = pydantic.Field()
    """
    The ID of the knowledge base
    """

    usage_mode: typing.Optional[DocumentUsageModeEnum] = pydantic.Field(default=None)
    """
    The usage mode of the knowledge base
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
