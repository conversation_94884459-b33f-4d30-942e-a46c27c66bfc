# This file was auto-generated by Fern from our API Definition.

import typing

Llm = typing.Union[
    typing.Literal[
        "gpt-4o-mini",
        "gpt-4o",
        "gpt-4",
        "gpt-4-turbo",
        "gpt-4.1",
        "gpt-4.1-mini",
        "gpt-4.1-nano",
        "gpt-3.5-turbo",
        "gemini-1.5-pro",
        "gemini-1.5-flash",
        "gemini-2.0-flash-001",
        "gemini-2.0-flash-lite",
        "gemini-2.5-flash",
        "gemini-1.0-pro",
        "claude-3-7-sonnet",
        "claude-3-5-sonnet",
        "claude-3-5-sonnet-v1",
        "claude-3-haiku",
        "grok-beta",
        "custom-llm",
    ],
    typing.Any,
]
