# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .speaker_separation_response_model import SpeakerSeparationResponseModel


class VoiceSample(UncheckedBaseModel):
    sample_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    The ID of the sample.
    """

    file_name: typing.Optional[str] = pydantic.Field(default=None)
    """
    The name of the sample file.
    """

    mime_type: typing.Optional[str] = pydantic.Field(default=None)
    """
    The MIME type of the sample file.
    """

    size_bytes: typing.Optional[int] = pydantic.Field(default=None)
    """
    The size of the sample file in bytes.
    """

    hash: typing.Optional[str] = pydantic.Field(default=None)
    """
    The hash of the sample file.
    """

    duration_secs: typing.Optional[float] = None
    remove_background_noise: typing.Optional[bool] = None
    has_isolated_audio: typing.Optional[bool] = None
    has_isolated_audio_preview: typing.Optional[bool] = None
    speaker_separation: typing.Optional[SpeakerSeparationResponseModel] = None
    trim_start: typing.Optional[int] = None
    trim_end: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
