# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .agent_config_override import AgentConfigOverride
from .conversation_config_override import ConversationConfigOverride
from .tts_conversational_config_override import TtsConversationalConfigOverride


class ConversationConfigClientOverrideInput(UncheckedBaseModel):
    agent: typing.Optional[AgentConfigOverride] = pydantic.Field(default=None)
    """
    The overrides for the agent configuration
    """

    tts: typing.Optional[TtsConversationalConfigOverride] = pydantic.Field(default=None)
    """
    The overrides for the TTS configuration
    """

    conversation: typing.Optional[ConversationConfigOverride] = pydantic.Field(default=None)
    """
    The overrides for the conversation configuration
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
