# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .share_option_response_model_type import ShareOptionResponseModelType


class ShareOptionResponseModel(UncheckedBaseModel):
    name: str = pydantic.Field()
    """
    The name of the principal.
    """

    id: str = pydantic.Field()
    """
    The ID of the principal.
    """

    type: ShareOptionResponseModelType = pydantic.Field()
    """
    The type of the principal: user, group, or service account (under 'key').
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
