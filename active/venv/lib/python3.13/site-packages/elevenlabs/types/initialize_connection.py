# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.serialization import FieldMetadata
from ..core.unchecked_base_model import UncheckedBaseModel
from .generation_config import GenerationConfig
from .pronunciation_dictionary_locator import PronunciationDictionaryLocator
from .realtime_voice_settings import RealtimeVoiceSettings


class InitializeConnection(UncheckedBaseModel):
    text: typing.Literal[" "] = pydantic.Field(default=" ")
    """
    The initial text that must be sent is a blank space.
    """

    voice_settings: typing.Optional[RealtimeVoiceSettings] = None
    generation_config: typing.Optional[GenerationConfig] = None
    pronunciation_dictionary_locators: typing.Optional[typing.List[PronunciationDictionaryLocator]] = pydantic.Field(
        default=None
    )
    """
    Optional list of pronunciation dictionary locators. If provided, these dictionaries will be used to
    modify pronunciation of matching text. Must only be provided in the first message.
    
    Note: Pronunciation dictionary matches will only be respected within a provided chunk.
    """

    xi_api_key: typing_extensions.Annotated[typing.Optional[str], FieldMetadata(alias="xi-api-key")] = pydantic.Field(
        default=None
    )
    """
    Your ElevenLabs API key. This can only be included in the first message and is not needed if present in the header.
    """

    authorization: typing.Optional[str] = pydantic.Field(default=None)
    """
    Your authorization bearer token. This can only be included in the first message and is not needed if present in the header.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
