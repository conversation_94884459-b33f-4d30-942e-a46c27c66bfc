# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class UsageCharactersResponseModel(UncheckedBaseModel):
    time: typing.List[int] = pydantic.Field()
    """
    The time axis with unix timestamps for each day.
    """

    usage: typing.Dict[str, typing.List[float]] = pydantic.Field()
    """
    The usage of each breakdown type along the time axis.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
