# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel
from .agent_config import AgentConfig
from .conversation_history_transcript_common_model_input import ConversationHistoryTranscriptCommonModelInput
from .tool_mock_config import ToolMockConfig


class ConversationSimulationSpecification(UncheckedBaseModel):
    """
    A specification that will be used to simulate a conversation between an agent and an AI user.
    """

    simulated_user_config: AgentConfig
    tool_mock_config: typing.Optional[typing.Dict[str, ToolMockConfig]] = None
    partial_conversation_history: typing.Optional[typing.List[ConversationHistoryTranscriptCommonModelInput]] = (
        pydantic.Field(default=None)
    )
    """
    A partial conversation history to start the simulation from. If empty, simulation starts fresh.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput  # noqa: E402, F401, I001
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput  # noqa: E402, F401, I001

update_forward_refs(ConversationSimulationSpecification)
