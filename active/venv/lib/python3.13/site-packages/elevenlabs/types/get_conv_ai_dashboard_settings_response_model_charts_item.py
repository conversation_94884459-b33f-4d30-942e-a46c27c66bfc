# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata


class GetConvAiDashboardSettingsResponseModelChartsItem_CallSuccess(UncheckedBaseModel):
    type: typing.Literal["call_success"] = "call_success"
    name: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class GetConvAiDashboardSettingsResponseModelChartsItem_Criteria(UncheckedBaseModel):
    type: typing.Literal["criteria"] = "criteria"
    name: str
    criteria_id: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class GetConvAiDashboardSettingsResponseModelChartsItem_DataCollection(UncheckedBaseModel):
    type: typing.Literal["data_collection"] = "data_collection"
    name: str
    data_collection_id: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


GetConvAiDashboardSettingsResponseModelChartsItem = typing_extensions.Annotated[
    typing.Union[
        GetConvAiDashboardSettingsResponseModelChartsItem_CallSuccess,
        GetConvAiDashboardSettingsResponseModelChartsItem_Criteria,
        GetConvAiDashboardSettingsResponseModelChartsItem_DataCollection,
    ],
    UnionMetadata(discriminant="type"),
]
