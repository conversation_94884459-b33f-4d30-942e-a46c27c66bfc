# This file was auto-generated by Fern from our API Definition.

from __future__ import annotations

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel


class NativeMcpToolConfigInput(UncheckedBaseModel):
    """
    A Native MCP tool is a tool that is used to call a Native MCP server
    """

    id: typing.Optional[str] = None
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum time in seconds to wait for the tool call to complete.
    """

    parameters: typing.Optional["ObjectJsonSchemaPropertyInput"] = pydantic.Field(default=None)
    """
    Schema for any parameters the LLM needs to provide to the MCP tool.
    """

    mcp_tool_name: str = pydantic.Field()
    """
    The name of the MCP tool to call
    """

    mcp_server_id: str = pydantic.Field()
    """
    The id of the MCP server to call
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput  # noqa: E402, F401, I001
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput  # noqa: E402, F401, I001

update_forward_refs(NativeMcpToolConfigInput)
