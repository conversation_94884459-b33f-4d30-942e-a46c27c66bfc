# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class DocxExportOptions(UncheckedBaseModel):
    include_speakers: typing.Optional[bool] = None
    include_timestamps: typing.Optional[bool] = None
    segment_on_silence_longer_than_s: typing.Optional[float] = None
    max_segment_duration_s: typing.Optional[float] = None
    max_segment_chars: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
