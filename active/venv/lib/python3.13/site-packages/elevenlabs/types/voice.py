# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .fine_tuning_response import FineTuningResponse
from .verified_voice_language_response_model import VerifiedVoiceLanguageResponseModel
from .voice_response_model_category import VoiceResponseModelCategory
from .voice_response_model_safety_control import VoiceResponseModelSafetyControl
from .voice_sample import VoiceSample
from .voice_settings import VoiceSettings
from .voice_sharing_response import VoiceSharingResponse
from .voice_verification_response import VoiceVerificationResponse


class Voice(UncheckedBaseModel):
    voice_id: str = pydantic.Field()
    """
    The ID of the voice.
    """

    name: typing.Optional[str] = pydantic.Field(default=None)
    """
    The name of the voice.
    """

    samples: typing.Optional[typing.List[VoiceSample]] = pydantic.Field(default=None)
    """
    List of samples associated with the voice.
    """

    category: typing.Optional[VoiceResponseModelCategory] = pydantic.Field(default=None)
    """
    The category of the voice.
    """

    fine_tuning: typing.Optional[FineTuningResponse] = pydantic.Field(default=None)
    """
    Fine-tuning information for the voice.
    """

    labels: typing.Optional[typing.Dict[str, str]] = pydantic.Field(default=None)
    """
    Labels associated with the voice.
    """

    description: typing.Optional[str] = pydantic.Field(default=None)
    """
    The description of the voice.
    """

    preview_url: typing.Optional[str] = pydantic.Field(default=None)
    """
    The preview URL of the voice.
    """

    available_for_tiers: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    The tiers the voice is available for.
    """

    settings: typing.Optional[VoiceSettings] = pydantic.Field(default=None)
    """
    The settings of the voice.
    """

    sharing: typing.Optional[VoiceSharingResponse] = pydantic.Field(default=None)
    """
    The sharing information of the voice.
    """

    high_quality_base_model_ids: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    The base model IDs for high-quality voices.
    """

    verified_languages: typing.Optional[typing.List[VerifiedVoiceLanguageResponseModel]] = pydantic.Field(default=None)
    """
    The verified languages of the voice.
    """

    safety_control: typing.Optional[VoiceResponseModelSafetyControl] = pydantic.Field(default=None)
    """
    The safety controls of the voice.
    """

    voice_verification: typing.Optional[VoiceVerificationResponse] = pydantic.Field(default=None)
    """
    The voice verification of the voice.
    """

    permission_on_resource: typing.Optional[str] = pydantic.Field(default=None)
    """
    The permission on the resource of the voice.
    """

    is_owner: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the voice is owned by the user.
    """

    is_legacy: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the voice is legacy.
    """

    is_mixed: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the voice is mixed.
    """

    created_at_unix: typing.Optional[int] = pydantic.Field(default=None)
    """
    The creation time of the voice in Unix time.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
