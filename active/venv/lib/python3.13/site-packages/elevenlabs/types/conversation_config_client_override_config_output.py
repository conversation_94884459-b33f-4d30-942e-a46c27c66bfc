# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .agent_config_override_config import AgentConfigOverrideConfig
from .conversation_config_override_config import ConversationConfigOverrideConfig
from .tts_conversational_config_override_config import TtsConversationalConfigOverrideConfig


class ConversationConfigClientOverrideConfigOutput(UncheckedBaseModel):
    agent: typing.Optional[AgentConfigOverrideConfig] = pydantic.Field(default=None)
    """
    Overrides for the agent configuration
    """

    tts: typing.Optional[TtsConversationalConfigOverrideConfig] = pydantic.Field(default=None)
    """
    Overrides for the TTS configuration
    """

    conversation: typing.Optional[ConversationConfigOverrideConfig] = pydantic.Field(default=None)
    """
    Overrides for the conversation configuration
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
