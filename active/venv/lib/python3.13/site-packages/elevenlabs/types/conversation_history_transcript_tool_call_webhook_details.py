# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class ConversationHistoryTranscriptToolCallWebhookDetails(UncheckedBaseModel):
    method: str
    url: str
    headers: typing.Optional[typing.Dict[str, str]] = None
    path_params: typing.Optional[typing.Dict[str, str]] = None
    query_params: typing.Optional[typing.Dict[str, str]] = None
    body: typing.Optional[str] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
