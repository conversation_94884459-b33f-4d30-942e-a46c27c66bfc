# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conversation_config_client_override_output import ConversationConfigClientOverrideOutput
from .language_preset_translation import LanguagePresetTranslation


class LanguagePresetOutput(UncheckedBaseModel):
    overrides: ConversationConfigClientOverrideOutput = pydantic.Field()
    """
    The overrides for the language preset
    """

    first_message_translation: typing.Optional[LanguagePresetTranslation] = pydantic.Field(default=None)
    """
    The translation of the first message
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
