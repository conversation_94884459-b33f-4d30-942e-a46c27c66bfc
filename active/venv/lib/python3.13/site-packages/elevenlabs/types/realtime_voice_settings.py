# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class RealtimeVoiceSettings(UncheckedBaseModel):
    stability: typing.Optional[float] = pydantic.Field(default=None)
    """
    Defines the stability for voice settings.
    """

    similarity_boost: typing.Optional[float] = pydantic.Field(default=None)
    """
    Defines the similarity boost for voice settings.
    """

    style: typing.Optional[float] = pydantic.Field(default=None)
    """
    Defines the style for voice settings. This parameter is available on V2+ models.
    """

    use_speaker_boost: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Defines the use speaker boost for voice settings. This parameter is available on V2+ models.
    """

    speed: typing.Optional[float] = pydantic.Field(default=None)
    """
    Controls the speed of the generated speech. Values range from 0.7 to 1.2, with 1.0 being the default speed.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
