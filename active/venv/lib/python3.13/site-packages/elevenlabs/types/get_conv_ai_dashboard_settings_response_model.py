# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .get_conv_ai_dashboard_settings_response_model_charts_item import GetConvAiDashboardSettingsResponseModelChartsItem


class GetConvAiDashboardSettingsResponseModel(UncheckedBaseModel):
    charts: typing.Optional[typing.List[GetConvAiDashboardSettingsResponseModelChartsItem]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
