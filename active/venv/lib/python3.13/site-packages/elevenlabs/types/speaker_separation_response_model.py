# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .speaker_response_model import SpeakerResponseModel
from .speaker_separation_response_model_status import SpeakerSeparationResponseModelStatus


class SpeakerSeparationResponseModel(UncheckedBaseModel):
    voice_id: str = pydantic.Field()
    """
    The ID of the voice.
    """

    sample_id: str = pydantic.Field()
    """
    The ID of the sample.
    """

    status: SpeakerSeparationResponseModelStatus = pydantic.Field()
    """
    The status of the speaker separation.
    """

    speakers: typing.Optional[typing.Dict[str, typing.Optional[SpeakerResponseModel]]] = pydantic.Field(default=None)
    """
    The speakers of the sample.
    """

    selected_speaker_ids: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    The IDs of the selected speakers.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
