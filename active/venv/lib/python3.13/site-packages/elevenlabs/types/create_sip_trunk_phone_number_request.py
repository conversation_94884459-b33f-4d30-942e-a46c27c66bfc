# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .sip_media_encryption_enum import SipMediaEncryptionEnum
from .sip_trunk_credentials import SipTrunkCredentials
from .sip_trunk_transport_enum import SipTrunkTransportEnum


class CreateSipTrunkPhoneNumberRequest(UncheckedBaseModel):
    """
    SIP trunk phone number request

    Includes termination URI and optional digest authentication credentials.
    If credentials are provided, both username and password must be included.
    If credentials are not provided, ACL authentication is assumed. (user needs to add our ips in their settings)
    """

    phone_number: str = pydantic.Field()
    """
    Phone number
    """

    label: str = pydantic.Field()
    """
    Label for the phone number
    """

    termination_uri: str = pydantic.Field()
    """
    SIP trunk termination URI
    """

    address: typing.Optional[str] = pydantic.Field(default=None)
    """
    Hostname or IP the SIP INVITE is sent to.
    """

    transport: typing.Optional[SipTrunkTransportEnum] = pydantic.Field(default=None)
    """
    Protocol to use for SIP transport (signalling layer).
    """

    media_encryption: typing.Optional[SipMediaEncryptionEnum] = pydantic.Field(default=None)
    """
    Whether or not to encrypt media (data layer).
    """

    headers: typing.Optional[typing.Dict[str, str]] = pydantic.Field(default=None)
    """
    SIP X-* headers for INVITE request. These headers are sent as-is and may help identify this call.
    """

    credentials: typing.Optional[SipTrunkCredentials] = pydantic.Field(default=None)
    """
    Optional digest authentication credentials (username/password). If not provided, ACL authentication is assumed.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
