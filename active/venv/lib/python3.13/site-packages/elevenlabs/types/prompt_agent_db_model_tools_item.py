# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .dynamic_variables_config import DynamicVariablesConfig
from .system_tool_config_input_params import SystemToolConfigInputParams
from .webhook_tool_api_schema_config_input import WebhookToolApiSchemaConfigInput


class PromptAgentDbModelToolsItem_Client(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["client"] = "client"
    id: typing.Optional[str] = None
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    parameters: typing.Optional["ObjectJsonSchemaPropertyInput"] = None
    expects_response: typing.Optional[bool] = None
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentDbModelToolsItem_Mcp(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["mcp"] = "mcp"
    id: typing.Optional[str] = None
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    parameters: typing.Optional["ObjectJsonSchemaPropertyInput"] = None
    mcp_tool_name: str
    mcp_server_id: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentDbModelToolsItem_NativeMcp(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["native_mcp"] = "native_mcp"
    id: typing.Optional[str] = None
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    parameters: typing.Optional["ObjectJsonSchemaPropertyInput"] = None
    mcp_tool_name: str
    mcp_server_id: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentDbModelToolsItem_System(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["system"] = "system"
    id: typing.Optional[str] = None
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    params: SystemToolConfigInputParams

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentDbModelToolsItem_Webhook(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["webhook"] = "webhook"
    id: typing.Optional[str] = None
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    api_schema: WebhookToolApiSchemaConfigInput
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput  # noqa: E402, F401, I001
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput  # noqa: E402, F401, I001

PromptAgentDbModelToolsItem = typing_extensions.Annotated[
    typing.Union[
        PromptAgentDbModelToolsItem_Client,
        PromptAgentDbModelToolsItem_Mcp,
        PromptAgentDbModelToolsItem_NativeMcp,
        PromptAgentDbModelToolsItem_System,
        PromptAgentDbModelToolsItem_Webhook,
    ],
    UnionMetadata(discriminant="type"),
]
update_forward_refs(PromptAgentDbModelToolsItem_Client)
update_forward_refs(PromptAgentDbModelToolsItem_Mcp)
update_forward_refs(PromptAgentDbModelToolsItem_NativeMcp)
update_forward_refs(PromptAgentDbModelToolsItem_Webhook)
