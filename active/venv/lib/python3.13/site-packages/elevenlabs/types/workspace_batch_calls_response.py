# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .batch_call_response import BatchCallResponse


class WorkspaceBatchCallsResponse(UncheckedBaseModel):
    batch_calls: typing.List[BatchCallResponse]
    next_doc: typing.Optional[str] = pydantic.Field(default=None)
    """
    The next document, used to paginate through the batch calls
    """

    has_more: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether there are more batch calls to paginate through
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
