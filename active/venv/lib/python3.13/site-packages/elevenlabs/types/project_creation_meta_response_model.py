# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .project_creation_meta_response_model_status import ProjectCreationMetaResponseModelStatus
from .project_creation_meta_response_model_type import ProjectCreationMetaResponseModelType


class ProjectCreationMetaResponseModel(UncheckedBaseModel):
    creation_progress: float = pydantic.Field()
    """
    The progress of the project creation.
    """

    status: ProjectCreationMetaResponseModelStatus = pydantic.Field()
    """
    The status of the project creation action.
    """

    type: ProjectCreationMetaResponseModelType = pydantic.Field()
    """
    The type of the project creation action.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
