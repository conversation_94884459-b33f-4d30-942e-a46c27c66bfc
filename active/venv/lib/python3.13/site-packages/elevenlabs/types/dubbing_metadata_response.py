# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .dubbing_media_metadata import DubbingMediaMetadata


class DubbingMetadataResponse(UncheckedBaseModel):
    dubbing_id: str = pydantic.Field()
    """
    The ID of the dubbing project.
    """

    name: str = pydantic.Field()
    """
    The name of the dubbing project.
    """

    status: str = pydantic.Field()
    """
    The status of the dubbing project. Either 'dubbed', 'dubbing' or 'failed'.
    """

    target_languages: typing.List[str] = pydantic.Field()
    """
    The target languages of the dubbing project.
    """

    media_metadata: typing.Optional[DubbingMediaMetadata] = pydantic.Field(default=None)
    """
    The media metadata of the dubbing project.
    """

    error: typing.Optional[str] = pydantic.Field(default=None)
    """
    Optional error message if the dubbing project failed.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
