# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class ChapterStatisticsResponse(UncheckedBaseModel):
    characters_unconverted: int = pydantic.Field()
    """
    The number of unconverted characters.
    """

    characters_converted: int = pydantic.Field()
    """
    The number of converted characters.
    """

    paragraphs_converted: int = pydantic.Field()
    """
    The number of converted paragraphs.
    """

    paragraphs_unconverted: int = pydantic.Field()
    """
    The number of unconverted paragraphs.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
