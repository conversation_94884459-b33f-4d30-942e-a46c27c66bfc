# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .generation_config import GenerationConfig
from .pronunciation_dictionary_locator import PronunciationDictionaryLocator
from .realtime_voice_settings import RealtimeVoiceSettings


class InitializeConnectionMulti(UncheckedBaseModel):
    """
    Payload to initialize a new context in a multi-stream WebSocket connection.
    """

    text: typing.Literal[" "] = pydantic.Field(default=" ")
    """
    Must be a single space character to initiate the context.
    """

    voice_settings: typing.Optional[RealtimeVoiceSettings] = None
    generation_config: typing.Optional[GenerationConfig] = None
    pronunciation_dictionary_locators: typing.Optional[typing.List[PronunciationDictionaryLocator]] = pydantic.Field(
        default=None
    )
    """
    Optional pronunciation dictionaries for this context.
    """

    xi_api_key: typing.Optional[str] = pydantic.Field(default=None)
    """
    Your ElevenLabs API key (if not in header). For this context's first message only.
    """

    authorization: typing.Optional[str] = pydantic.Field(default=None)
    """
    Your authorization bearer token (if not in header). For this context's first message only.
    """

    context_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    A unique identifier for the first context created in the websocket. If not provided, a default context will be used.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
