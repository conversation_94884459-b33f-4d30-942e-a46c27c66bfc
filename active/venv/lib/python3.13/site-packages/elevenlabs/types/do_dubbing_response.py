# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class DoDubbingResponse(UncheckedBaseModel):
    dubbing_id: str = pydantic.Field()
    """
    The ID of the dubbing project.
    """

    expected_duration_sec: float = pydantic.Field()
    """
    The expected duration of the dubbing project in seconds.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
