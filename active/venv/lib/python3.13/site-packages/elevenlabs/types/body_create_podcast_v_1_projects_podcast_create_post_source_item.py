# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata


class BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Text(UncheckedBaseModel):
    type: typing.Literal["text"] = "text"
    text: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Url(UncheckedBaseModel):
    type: typing.Literal["url"] = "url"
    url: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem = typing_extensions.Annotated[
    typing.Union[
        BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Text,
        BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem_Url,
    ],
    UnionMetadata(discriminant="type"),
]
