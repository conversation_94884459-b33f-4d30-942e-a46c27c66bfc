# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class SubscriptionUsageResponseModel(UncheckedBaseModel):
    rollover_credits_quota: int = pydantic.Field()
    """
    The rollover credits quota.
    """

    subscription_cycle_credits_quota: int = pydantic.Field()
    """
    The subscription cycle credits quota.
    """

    manually_gifted_credits_quota: int = pydantic.Field()
    """
    The manually gifted credits quota.
    """

    rollover_credits_used: int = pydantic.Field()
    """
    The rollover credits used.
    """

    subscription_cycle_credits_used: int = pydantic.Field()
    """
    The subscription cycle credits used.
    """

    manually_gifted_credits_used: int = pydantic.Field()
    """
    The manually gifted credits used.
    """

    paid_usage_based_credits_used: int = pydantic.Field()
    """
    The paid usage based credits used.
    """

    actual_reported_credits: int = pydantic.Field()
    """
    The actual reported credits.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
