# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import <PERSON><PERSON><PERSON>ecodeError

from .. import core
from ..core.api_error import ApiError
from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.http_response import AsyncHttpResponse, HttpResponse
from ..core.jsonable_encoder import jsonable_encoder
from ..core.request_options import RequestOptions
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.delete_dubbing_response_model import DeleteDubbingResponseModel
from ..types.do_dubbing_response import DoDubbingResponse
from ..types.dubbing_metadata_response import DubbingMetadataResponse
from ..types.http_validation_error import HttpValidationError

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawDubbingClient:
    def __init__(self, *, client_wrapper: Sync<PERSON>lientWrapper):
        self._client_wrapper = client_wrapper

    def create(
        self,
        *,
        file: typing.Optional[core.File] = OMIT,
        csv_file: typing.Optional[core.File] = OMIT,
        foreground_audio_file: typing.Optional[core.File] = OMIT,
        background_audio_file: typing.Optional[core.File] = OMIT,
        name: typing.Optional[str] = OMIT,
        source_url: typing.Optional[str] = OMIT,
        source_lang: typing.Optional[str] = OMIT,
        target_lang: typing.Optional[str] = OMIT,
        num_speakers: typing.Optional[int] = OMIT,
        watermark: typing.Optional[bool] = OMIT,
        start_time: typing.Optional[int] = OMIT,
        end_time: typing.Optional[int] = OMIT,
        highest_resolution: typing.Optional[bool] = OMIT,
        drop_background_audio: typing.Optional[bool] = OMIT,
        use_profanity_filter: typing.Optional[bool] = OMIT,
        dubbing_studio: typing.Optional[bool] = OMIT,
        disable_voice_cloning: typing.Optional[bool] = OMIT,
        mode: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[DoDubbingResponse]:
        """
        Dubs a provided audio or video file into given language.

        Parameters
        ----------
        file : typing.Optional[core.File]
            See core.File for more documentation

        csv_file : typing.Optional[core.File]
            See core.File for more documentation

        foreground_audio_file : typing.Optional[core.File]
            See core.File for more documentation

        background_audio_file : typing.Optional[core.File]
            See core.File for more documentation

        name : typing.Optional[str]
            Name of the dubbing project.

        source_url : typing.Optional[str]
            URL of the source video/audio file.

        source_lang : typing.Optional[str]
            Source language.

        target_lang : typing.Optional[str]
            The Target language to dub the content into.

        num_speakers : typing.Optional[int]
            Number of speakers to use for the dubbing. Set to 0 to automatically detect the number of speakers

        watermark : typing.Optional[bool]
            Whether to apply watermark to the output video.

        start_time : typing.Optional[int]
            Start time of the source video/audio file.

        end_time : typing.Optional[int]
            End time of the source video/audio file.

        highest_resolution : typing.Optional[bool]
            Whether to use the highest resolution available.

        drop_background_audio : typing.Optional[bool]
            An advanced setting. Whether to drop background audio from the final dub. This can improve dub quality where it's known that audio shouldn't have a background track such as for speeches or monologues.

        use_profanity_filter : typing.Optional[bool]
            [BETA] Whether transcripts should have profanities censored with the words '[censored]'

        dubbing_studio : typing.Optional[bool]
            Whether to prepare dub for edits in dubbing studio or edits as a dubbing resource.

        disable_voice_cloning : typing.Optional[bool]
            [BETA] Instead of using a voice clone in dubbing, use a similar voice from the ElevenLabs Voice Library.

        mode : typing.Optional[str]
            automatic or manual. Manual mode is only supported when creating a dubbing studio project

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[DoDubbingResponse]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/dubbing",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
                "source_url": source_url,
                "source_lang": source_lang,
                "target_lang": target_lang,
                "num_speakers": num_speakers,
                "watermark": watermark,
                "start_time": start_time,
                "end_time": end_time,
                "highest_resolution": highest_resolution,
                "drop_background_audio": drop_background_audio,
                "use_profanity_filter": use_profanity_filter,
                "dubbing_studio": dubbing_studio,
                "disable_voice_cloning": disable_voice_cloning,
                "mode": mode,
            },
            files={
                **({"file": file} if file is not None else {}),
                **({"csv_file": csv_file} if csv_file is not None else {}),
                **({"foreground_audio_file": foreground_audio_file} if foreground_audio_file is not None else {}),
                **({"background_audio_file": background_audio_file} if background_audio_file is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DoDubbingResponse,
                    construct_type(
                        type_=DoDubbingResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def get(
        self, dubbing_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[DubbingMetadataResponse]:
        """
        Returns metadata about a dubbing project, including whether it's still in progress or not

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[DubbingMetadataResponse]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DubbingMetadataResponse,
                    construct_type(
                        type_=DubbingMetadataResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def delete(
        self, dubbing_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[DeleteDubbingResponseModel]:
        """
        Deletes a dubbing project.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[DeleteDubbingResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DeleteDubbingResponseModel,
                    construct_type(
                        type_=DeleteDubbingResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawDubbingClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def create(
        self,
        *,
        file: typing.Optional[core.File] = OMIT,
        csv_file: typing.Optional[core.File] = OMIT,
        foreground_audio_file: typing.Optional[core.File] = OMIT,
        background_audio_file: typing.Optional[core.File] = OMIT,
        name: typing.Optional[str] = OMIT,
        source_url: typing.Optional[str] = OMIT,
        source_lang: typing.Optional[str] = OMIT,
        target_lang: typing.Optional[str] = OMIT,
        num_speakers: typing.Optional[int] = OMIT,
        watermark: typing.Optional[bool] = OMIT,
        start_time: typing.Optional[int] = OMIT,
        end_time: typing.Optional[int] = OMIT,
        highest_resolution: typing.Optional[bool] = OMIT,
        drop_background_audio: typing.Optional[bool] = OMIT,
        use_profanity_filter: typing.Optional[bool] = OMIT,
        dubbing_studio: typing.Optional[bool] = OMIT,
        disable_voice_cloning: typing.Optional[bool] = OMIT,
        mode: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[DoDubbingResponse]:
        """
        Dubs a provided audio or video file into given language.

        Parameters
        ----------
        file : typing.Optional[core.File]
            See core.File for more documentation

        csv_file : typing.Optional[core.File]
            See core.File for more documentation

        foreground_audio_file : typing.Optional[core.File]
            See core.File for more documentation

        background_audio_file : typing.Optional[core.File]
            See core.File for more documentation

        name : typing.Optional[str]
            Name of the dubbing project.

        source_url : typing.Optional[str]
            URL of the source video/audio file.

        source_lang : typing.Optional[str]
            Source language.

        target_lang : typing.Optional[str]
            The Target language to dub the content into.

        num_speakers : typing.Optional[int]
            Number of speakers to use for the dubbing. Set to 0 to automatically detect the number of speakers

        watermark : typing.Optional[bool]
            Whether to apply watermark to the output video.

        start_time : typing.Optional[int]
            Start time of the source video/audio file.

        end_time : typing.Optional[int]
            End time of the source video/audio file.

        highest_resolution : typing.Optional[bool]
            Whether to use the highest resolution available.

        drop_background_audio : typing.Optional[bool]
            An advanced setting. Whether to drop background audio from the final dub. This can improve dub quality where it's known that audio shouldn't have a background track such as for speeches or monologues.

        use_profanity_filter : typing.Optional[bool]
            [BETA] Whether transcripts should have profanities censored with the words '[censored]'

        dubbing_studio : typing.Optional[bool]
            Whether to prepare dub for edits in dubbing studio or edits as a dubbing resource.

        disable_voice_cloning : typing.Optional[bool]
            [BETA] Instead of using a voice clone in dubbing, use a similar voice from the ElevenLabs Voice Library.

        mode : typing.Optional[str]
            automatic or manual. Manual mode is only supported when creating a dubbing studio project

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[DoDubbingResponse]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/dubbing",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            data={
                "name": name,
                "source_url": source_url,
                "source_lang": source_lang,
                "target_lang": target_lang,
                "num_speakers": num_speakers,
                "watermark": watermark,
                "start_time": start_time,
                "end_time": end_time,
                "highest_resolution": highest_resolution,
                "drop_background_audio": drop_background_audio,
                "use_profanity_filter": use_profanity_filter,
                "dubbing_studio": dubbing_studio,
                "disable_voice_cloning": disable_voice_cloning,
                "mode": mode,
            },
            files={
                **({"file": file} if file is not None else {}),
                **({"csv_file": csv_file} if csv_file is not None else {}),
                **({"foreground_audio_file": foreground_audio_file} if foreground_audio_file is not None else {}),
                **({"background_audio_file": background_audio_file} if background_audio_file is not None else {}),
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DoDubbingResponse,
                    construct_type(
                        type_=DoDubbingResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def get(
        self, dubbing_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[DubbingMetadataResponse]:
        """
        Returns metadata about a dubbing project, including whether it's still in progress or not

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[DubbingMetadataResponse]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DubbingMetadataResponse,
                    construct_type(
                        type_=DubbingMetadataResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def delete(
        self, dubbing_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[DeleteDubbingResponseModel]:
        """
        Deletes a dubbing project.

        Parameters
        ----------
        dubbing_id : str
            ID of the dubbing project.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[DeleteDubbingResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/dubbing/{jsonable_encoder(dubbing_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DeleteDubbingResponseModel,
                    construct_type(
                        type_=DeleteDubbingResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
