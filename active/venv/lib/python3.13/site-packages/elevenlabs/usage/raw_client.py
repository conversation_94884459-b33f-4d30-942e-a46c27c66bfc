# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import J<PERSON>NDecodeError

from ..core.api_error import ApiError
from ..core.client_wrapper import Async<PERSON>lientWrapper, SyncClientWrapper
from ..core.http_response import Async<PERSON>ttpR<PERSON>ponse, HttpResponse
from ..core.request_options import RequestOptions
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.breakdown_types import BreakdownTypes
from ..types.http_validation_error import HttpValidationError
from ..types.metric_type import MetricType
from ..types.usage_aggregation_interval import UsageAggregationInterval
from ..types.usage_characters_response_model import UsageCharactersResponseModel


class RawUsageClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def get(
        self,
        *,
        start_unix: int,
        end_unix: int,
        include_workspace_metrics: typing.Optional[bool] = None,
        breakdown_type: typing.Optional[BreakdownTypes] = None,
        aggregation_interval: typing.Optional[UsageAggregationInterval] = None,
        metric: typing.Optional[MetricType] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[UsageCharactersResponseModel]:
        """
        Returns the usage metrics for the current user or the entire workspace they are part of. The response provides a time axis based on the specified aggregation interval (default: day), with usage values for each interval along that axis. Usage is broken down by the selected breakdown type. For example, breakdown type "voice" will return the usage of each voice for each interval along the time axis.

        Parameters
        ----------
        start_unix : int
            UTC Unix timestamp for the start of the usage window, in milliseconds. To include the first day of the window, the timestamp should be at 00:00:00 of that day.

        end_unix : int
            UTC Unix timestamp for the end of the usage window, in milliseconds. To include the last day of the window, the timestamp should be at 23:59:59 of that day.

        include_workspace_metrics : typing.Optional[bool]
            Whether or not to include the statistics of the entire workspace.

        breakdown_type : typing.Optional[BreakdownTypes]
            How to break down the information. Cannot be "user" if include_workspace_metrics is False.

        aggregation_interval : typing.Optional[UsageAggregationInterval]
            How to aggregate usage data over time. Can be "hour", "day", "week", "month", or "cumulative".

        metric : typing.Optional[MetricType]
            Which metric to aggregate.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[UsageCharactersResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/usage/character-stats",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "start_unix": start_unix,
                "end_unix": end_unix,
                "include_workspace_metrics": include_workspace_metrics,
                "breakdown_type": breakdown_type,
                "aggregation_interval": aggregation_interval,
                "metric": metric,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    UsageCharactersResponseModel,
                    construct_type(
                        type_=UsageCharactersResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawUsageClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def get(
        self,
        *,
        start_unix: int,
        end_unix: int,
        include_workspace_metrics: typing.Optional[bool] = None,
        breakdown_type: typing.Optional[BreakdownTypes] = None,
        aggregation_interval: typing.Optional[UsageAggregationInterval] = None,
        metric: typing.Optional[MetricType] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[UsageCharactersResponseModel]:
        """
        Returns the usage metrics for the current user or the entire workspace they are part of. The response provides a time axis based on the specified aggregation interval (default: day), with usage values for each interval along that axis. Usage is broken down by the selected breakdown type. For example, breakdown type "voice" will return the usage of each voice for each interval along the time axis.

        Parameters
        ----------
        start_unix : int
            UTC Unix timestamp for the start of the usage window, in milliseconds. To include the first day of the window, the timestamp should be at 00:00:00 of that day.

        end_unix : int
            UTC Unix timestamp for the end of the usage window, in milliseconds. To include the last day of the window, the timestamp should be at 23:59:59 of that day.

        include_workspace_metrics : typing.Optional[bool]
            Whether or not to include the statistics of the entire workspace.

        breakdown_type : typing.Optional[BreakdownTypes]
            How to break down the information. Cannot be "user" if include_workspace_metrics is False.

        aggregation_interval : typing.Optional[UsageAggregationInterval]
            How to aggregate usage data over time. Can be "hour", "day", "week", "month", or "cumulative".

        metric : typing.Optional[MetricType]
            Which metric to aggregate.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[UsageCharactersResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/usage/character-stats",
            base_url=self._client_wrapper.get_environment().base,
            method="GET",
            params={
                "start_unix": start_unix,
                "end_unix": end_unix,
                "include_workspace_metrics": include_workspace_metrics,
                "breakdown_type": breakdown_type,
                "aggregation_interval": aggregation_interval,
                "metric": metric,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    UsageCharactersResponseModel,
                    construct_type(
                        type_=UsageCharactersResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
