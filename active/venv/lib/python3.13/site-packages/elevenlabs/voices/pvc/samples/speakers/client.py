# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .....core.client_wrapper import AsyncClient<PERSON>rapper, SyncClientWrapper
from .....core.request_options import RequestOptions
from .....types.speaker_separation_response_model import SpeakerSeparationResponseModel
from .....types.start_speaker_separation_response_model import StartSpeakerSeparationResponseModel
from .audio.client import AsyncAudioClient, AudioClient
from .raw_client import AsyncRawSpeakersClient, RawSpeakersClient


class SpeakersClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSpeakersClient(client_wrapper=client_wrapper)
        self.audio = AudioClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSpeakersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSpeakersClient
        """
        return self._raw_client

    def get(
        self, voice_id: str, sample_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> SpeakerSeparationResponseModel:
        """
        Retrieve the status of the speaker separation process and the list of detected speakers if complete.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        sample_id : str
            Sample ID to be used

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SpeakerSeparationResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.samples.speakers.get(
            voice_id="21m00Tcm4TlvDq8ikWAM",
            sample_id="VW7YKqPnjY4h39yTbx2L",
        )
        """
        _response = self._raw_client.get(voice_id, sample_id, request_options=request_options)
        return _response.data

    def separate(
        self, voice_id: str, sample_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> StartSpeakerSeparationResponseModel:
        """
        Start speaker separation process for a sample

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        sample_id : str
            Sample ID to be used

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        StartSpeakerSeparationResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.samples.speakers.separate(
            voice_id="21m00Tcm4TlvDq8ikWAM",
            sample_id="VW7YKqPnjY4h39yTbx2L",
        )
        """
        _response = self._raw_client.separate(voice_id, sample_id, request_options=request_options)
        return _response.data


class AsyncSpeakersClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSpeakersClient(client_wrapper=client_wrapper)
        self.audio = AsyncAudioClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSpeakersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSpeakersClient
        """
        return self._raw_client

    async def get(
        self, voice_id: str, sample_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> SpeakerSeparationResponseModel:
        """
        Retrieve the status of the speaker separation process and the list of detected speakers if complete.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        sample_id : str
            Sample ID to be used

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SpeakerSeparationResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.samples.speakers.get(
                voice_id="21m00Tcm4TlvDq8ikWAM",
                sample_id="VW7YKqPnjY4h39yTbx2L",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(voice_id, sample_id, request_options=request_options)
        return _response.data

    async def separate(
        self, voice_id: str, sample_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> StartSpeakerSeparationResponseModel:
        """
        Start speaker separation process for a sample

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        sample_id : str
            Sample ID to be used

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        StartSpeakerSeparationResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.samples.speakers.separate(
                voice_id="21m00Tcm4TlvDq8ikWAM",
                sample_id="VW7YKqPnjY4h39yTbx2L",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.separate(voice_id, sample_id, request_options=request_options)
        return _response.data
