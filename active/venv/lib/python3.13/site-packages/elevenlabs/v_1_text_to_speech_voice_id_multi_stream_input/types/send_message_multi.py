# This file was auto-generated by Fern from our API Definition.

import typing

from ...types.close_context import CloseContext
from ...types.close_socket import CloseSocket
from ...types.flush_context import FlushContext
from ...types.initialise_context import InitialiseContext
from ...types.initialize_connection_multi import InitializeConnectionMulti
from ...types.keep_context_alive import KeepContextAlive
from ...types.send_text_multi import SendTextMulti

SendMessageMulti = typing.Union[
    InitializeConnectionMulti,
    InitialiseContext,
    SendTextMulti,
    FlushContext,
    CloseContext,
    CloseSocket,
    KeepContextAlive,
]
