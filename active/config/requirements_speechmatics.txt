# Speechmatics Integration Requirements
# Core LiveKit and AI dependencies

# LiveKit Agents Framework
livekit-agents[codecs,openai,silero,elevenlabs]>=0.8.0

# Speechmatics Python SDK
speechmatics-python>=4.0.0

# WebSocket support for real-time Speechmatics
websockets>=12.0

# Audio processing
wave>=0.0.2
pyaudio>=0.2.11

# AI and LLM
openai>=1.0.0

# TTS
elevenlabs>=1.0.0

# Voice Activity Detection
torch>=2.0.0
torchaudio>=2.0.0

# Utilities
python-dotenv>=1.0.0
asyncio-mqtt>=0.16.0

# Development and testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
