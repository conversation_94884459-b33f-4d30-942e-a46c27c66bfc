# Juno Step 2A: Arabic-English Language Detection Requirements
# This file contains all dependencies needed for Step 2A implementation

# Core LiveKit Agents framework
livekit-agents>=1.0.0

# LiveKit plugins for AI integrations
livekit-plugins-openai>=0.8.0
livekit-plugins-cartesia>=0.3.0
livekit-plugins-silero>=0.3.0

# OpenAI for Whisper STT and GPT models
openai>=1.0.0

# Environment management
python-dotenv>=1.0.0

# Audio processing (required by LiveKit)
numpy>=1.24.0
scipy>=1.10.0

# Async HTTP client (used by OpenAI)
aiohttp>=3.8.0
httpx>=0.24.0

# Data handling
pydantic>=2.0.0

# Testing dependencies
pytest>=7.0.0
pytest-asyncio>=0.21.0

# Development dependencies
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# Optional: For enhanced audio processing
# ffmpeg-python>=0.2.0  # Uncomment if needed for audio format conversion

# Note: Make sure you have the following environment variables set:
# - OPENAI_API_KEY: Your OpenAI API key for Whisper STT
# - CARTESIA_API_KEY: Your Cartesia API key for TTS
# - LIVEKIT_URL: Your LiveKit server URL
# - LIVEKIT_API_KEY: Your LiveKit API key
# - LIVEKIT_API_SECRET: Your LiveKit API secret
