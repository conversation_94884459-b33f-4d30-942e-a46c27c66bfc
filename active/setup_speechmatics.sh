#!/bin/bash

# Speechmatics Integration Setup Script
# This script sets up the environment for testing Speechmatics STT integration

echo "🚀 Setting up Speechmatics STT Integration..."

# Check if we're in the right directory
if [ ! -f "setup_speechmatics.sh" ]; then
    echo "❌ Please run this script from the active/ directory"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "📥 Installing Speechmatics requirements..."
pip install -r config/requirements_speechmatics.txt

# Install additional dependencies that might be needed
echo "📥 Installing additional dependencies..."
pip install numpy>=1.24.0
pip install scipy>=1.10.0

# Verify installations
echo "✅ Verifying installations..."

# Check Speechmatics
python -c "import speechmatics; print('✅ Speechmatics Python SDK installed')" || echo "❌ Speechmatics installation failed"

# Check LiveKit
python -c "import livekit; print('✅ LiveKit installed')" || echo "❌ LiveKit installation failed"

# Check WebSockets
python -c "import websockets; print('✅ WebSockets installed')" || echo "❌ WebSockets installation failed"

# Check OpenAI
python -c "import openai; print('✅ OpenAI installed')" || echo "❌ OpenAI installation failed"

# Check ElevenLabs
python -c "import elevenlabs; print('✅ ElevenLabs installed')" || echo "❌ ElevenLabs installation failed"

echo ""
echo "🎯 Setup complete! You can now:"
echo "   1. Test Speechmatics STT: python src/stt_comparison_test.py --test-audio your_audio.wav"
echo "   2. Run Juno with Speechmatics: python src/juno_speechmatics_agent.py"
echo ""
echo "📝 Note: Make sure you have:"
echo "   - Speechmatics API key: v47WfwRBYZd51Ei2NTCwyAHHmZrrpHF3"
echo "   - LiveKit project configured"
echo "   - Audio file in WAV format for testing"
echo ""
echo "🔍 To compare with your working Whisper setup:"
echo "   - Your archived Whisper agent is in: archive/2025-06-04_2025-06-04-working-arabic-english-wake-word/"
echo "   - You can copy files from there if needed for comparison"
