#!/usr/bin/env python3
"""
Juno Voice Agent with Speechmatics STT Integration

This agent uses Speechmatics STT for improved Arabic-English speech recognition
while maintaining the same functionality as the original Whisper-based agent.

Features:
- Speechmatics STT for professional-grade accuracy
- Arabic-English multilingual support
- Wake word detection ("Juno")
- ElevenLabs TTS for high-quality voice output
- Passive/Active mode switching
"""

import asyncio
import logging
import os
from typing import Annotated
from livekit.agents import (
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli,
    llm,
)
from livekit.agents.voice_assistant import VoiceAssistant
from livekit.plugins import openai, elevenlabs, silero

# Import our custom Speechmatics STT
from speechmatics_stt import create_speechmatics_stt

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API Keys from environment
SPEECHMATICS_API_KEY = "v47WfwRBYZd51Ei2NTCwyAHHmZrrpHF3"
ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY", "***************************************************")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "*******************************************************************************************************************************************************************")

# ElevenLabs Voice ID (your preferred voice)
ELEVENLABS_VOICE_ID = "QRq5hPRAKf5ZhSlTBH6r"

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for Juno voice agent with Speechmatics STT.
    """
    logger.info("🚀 Starting Juno Voice Agent with Speechmatics STT...")
    
    # Connect to the room first
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)
    logger.info("🔗 Connected to LiveKit room")
    
    # Create Speechmatics STT instance
    speechmatics_stt = create_speechmatics_stt(
        api_key=SPEECHMATICS_API_KEY,
        language="en",  # Primary language, can auto-detect Arabic
        enable_partials=True,
        add_language_markers=True,
        supported_languages=["en", "ar"]
    )
    logger.info("🎤 Speechmatics STT initialized")
    
    # Create LLM for conversation
    assistant_llm = openai.LLM(
        model="gpt-4o-mini",
        temperature=0.1,  # Low temperature for consistent responses
    )
    logger.info("🧠 OpenAI LLM initialized")
    
    # Create ElevenLabs TTS
    assistant_tts = elevenlabs.TTS(
        voice=ELEVENLABS_VOICE_ID,
        model="eleven_turbo_v2_5",
        api_key=ELEVENLABS_API_KEY,
    )
    logger.info("🔊 ElevenLabs TTS initialized")
    
    # Create Voice Assistant with Speechmatics STT
    assistant = VoiceAssistant(
        vad=silero.VAD.load(),
        stt=speechmatics_stt,  # Using Speechmatics instead of Whisper
        llm=assistant_llm,
        tts=assistant_tts,
        chat_ctx=llm.ChatContext().append(
            role="system",
            text="""You are Juno, a helpful AI meeting assistant.

IMPORTANT BEHAVIOR:
- You are in PASSIVE MODE by default - you only listen and transcribe
- You ONLY respond when someone says "Juno" followed by a question or request
- When activated by "Juno", provide a helpful response, then return to passive mode
- Keep responses concise and professional
- You support both English and Arabic languages seamlessly

LANGUAGE SUPPORT:
- Respond in the same language the user spoke to you
- If user speaks Arabic, respond in Arabic
- If user speaks English, respond in English
- You can handle code-switching between Arabic and English

GREETING:
When first activated, greet with: "Hi, Juno Online and in Passive Mode. مرحبا أنا جونو"

PASSIVE MODE:
- Listen and transcribe everything
- Do not respond unless "Juno" is mentioned
- Stay quiet and observant"""
        ),
    )
    
    logger.info("🤖 Voice Assistant created with Speechmatics STT")
    
    # Start the assistant
    assistant.start(ctx.room)
    logger.info("✅ Juno Voice Agent with Speechmatics STT is ready!")
    
    # Generate initial greeting
    await assistant.say(
        "Hi, Juno Online and in Passive Mode. مرحبا أنا جونو",
        allow_interruptions=True
    )
    
    logger.info("🎯 Juno is now listening with Speechmatics STT...")


if __name__ == "__main__":
    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        ),
    )
