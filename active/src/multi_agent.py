#!/usr/bin/env python3

import asyncio
import logging
import os

from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiAgentSystem:
    """Man<PERSON> switching between <PERSON> (Finance) and <PERSON> (Academic) personalities"""
    
    def __init__(self):
        self.current_agent = "marcus"  # Start with <PERSON>
        self.conversation_context = {}
        
    def get_current_instructions(self):
        """Get instructions for the currently active agent"""
        if self.current_agent == "marcus":
            return self.get_marcus_instructions()
        else:
            return self.get_sophia_instructions()
    
    def get_current_voice_id(self):
        """Get voice ID for the currently active agent"""
        if self.current_agent == "marcus":
            return os.getenv("MARCUS_VOICE_ID", "a0e99841-438c-4a64-b679-ae501e7d6091")
        else:
            return os.getenv("SOPHIA_VOICE_ID", "79a125e8-cd45-4c13-8a67-188112f4dd22")
    
    def switch_agent(self, target_agent):
        """Switch to the specified agent"""
        if target_agent in ["marcus", "sophia"]:
            self.current_agent = target_agent
            return True
        return False
    
    def get_marcus_instructions(self):
        return """You are Marcus, a professional financial advisor and expert.

PERSONALITY: Professional, confident, analytical, approachable
VOICE RULES: Use ONLY plain text, NO special characters, speak conversationally

EXPERTISE: Personal budgeting, investments, retirement planning, debt management, tax planning, real estate, business finance, insurance, market analysis

AGENT SWITCHING: If someone asks for academic help, say "Let me connect you with Sophia, our academic specialist" and call switch_to_sophia()

Remember: Provide practical financial guidance that people can implement immediately."""

    def get_sophia_instructions(self):
        return """You are Sophia, an experienced academic advisor and learning specialist.

PERSONALITY: Encouraging, knowledgeable, supportive, patient, enthusiastic
VOICE RULES: Use ONLY plain text, NO special characters, speak conversationally

EXPERTISE: Study techniques, research methodology, academic writing, time management, exam preparation, literature reviews, thesis planning, academic goal setting

AGENT SWITCHING: If someone asks for financial help, say "Let me connect you with Marcus, our finance expert" and call switch_to_marcus()

Remember: Empower learners to achieve their academic potential through proven strategies."""

# Global multi-agent system instance
multi_agent_system = MultiAgentSystem()

def prewarm(proc: JobContext):
    """Prewarm function to initialize the multi-agent system"""
    logger.info("Prewarming Multi-Agent Assistant (Marcus & Sophia)...")
    proc.wait_for_participant = False
    logger.info("Multi-Agent system ready!")

async def entrypoint(ctx: JobContext):
    """
    Multi-Agent Assistant Entry Point
    Manages Marcus (Finance) and Sophia (Academic) personalities
    """
    logger.info("Starting Multi-Agent Assistant...")

    # Connect to LiveKit room
    await ctx.connect()
    logger.info("Multi-Agent Assistant connected to LiveKit room")

    # Import LiveKit plugins
    from livekit.plugins import deepgram, cartesia, silero, openai

    # Create the multi-agent
    agent = Agent(instructions=multi_agent_system.get_current_instructions())

    # Agent switching functions
    @agent.function()
    async def switch_to_marcus():
        """Switch to Marcus (Finance Expert)"""
        multi_agent_system.switch_agent("marcus")
        logger.info("Switched to Marcus (Finance Agent)")
        return "Hi! I'm Marcus, your financial advisor. How can I help you with your money matters today?"

    @agent.function()
    async def switch_to_sophia():
        """Switch to Sophia (Academic Expert)"""
        multi_agent_system.switch_agent("sophia")
        logger.info("Switched to Sophia (Academic Agent)")
        return "Hello! I'm Sophia, your academic learning specialist. What can I help you with in your studies today?"

    # Marcus's financial functions
    @agent.function()
    async def create_budget_plan(monthly_income: float, fixed_expenses: float, variable_expenses: float, financial_goals: str):
        """Create a personalized budget plan (Marcus function)"""
        if multi_agent_system.current_agent != "marcus":
            return "Let me connect you with Marcus for financial planning."
        
        disposable_income = monthly_income - fixed_expenses - variable_expenses
        savings_rate = (disposable_income / monthly_income) * 100 if monthly_income > 0 else 0
        
        if savings_rate >= 20:
            assessment = "Excellent! You're saving at a healthy rate."
        elif savings_rate >= 10:
            assessment = "Good start, but there's room for improvement."
        else:
            assessment = "We need to work on increasing your savings rate."
            
        return f"Based on your income of ${monthly_income:,.2f}, you have ${disposable_income:,.2f} available monthly. Your savings rate is {savings_rate:.1f}%. {assessment}"

    # Sophia's academic functions
    @agent.function()
    async def create_study_schedule(subjects: str, available_hours_per_day: int, exam_dates: str, learning_style: str):
        """Create a personalized study schedule (Sophia function)"""
        if multi_agent_system.current_agent != "sophia":
            return "Let me connect you with Sophia for academic planning."
        
        subject_list = [s.strip() for s in subjects.split(",")]
        total_subjects = len(subject_list)
        hours_per_subject = available_hours_per_day // total_subjects if total_subjects > 0 else 0
        
        schedule_advice = f"With {available_hours_per_day} hours daily for {total_subjects} subjects, I recommend {hours_per_subject} hours per subject."
        
        if learning_style.lower() == "visual":
            schedule_advice += " Include mind maps and diagrams in your study sessions."
        elif learning_style.lower() == "auditory":
            schedule_advice += " Incorporate reading aloud and discussions."
        
        return schedule_advice

    # Create agent session
    session = AgentSession(
        vad=silero.VAD.load(),
        stt=deepgram.STT(model="nova-2-general", language="en"),
        llm=openai.LLM(model="gpt-4o-mini", temperature=0.2),
        tts=cartesia.TTS(
            model="sonic-english",
            voice=multi_agent_system.get_current_voice_id(),
            speed=1.0
        ),
    )

    # Start the session
    await session.start(agent=agent, room=ctx.room)
    logger.info("Multi-Agent Assistant is now active!")

    # Send initial greeting
    await session.generate_reply(
        instructions="Introduce yourself as Marcus, the financial advisor, and mention that Sophia is also available for academic help. Ask how you can assist today. Use only plain text."
    )

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
