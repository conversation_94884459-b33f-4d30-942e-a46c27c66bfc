#!/usr/bin/env python3
"""
Simple HTTP server for Juno button activation.
Provides a web interface to toggle between LISTENING and RESPONSIVE modes.
"""

import asyncio
import json
import logging
from http.server import <PERSON>TTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import os

# Global state shared with the main agent
activation_mode = "LISTENING"
mode_change_callbacks = []

logger = logging.getLogger(__name__)

class But<PERSON><PERSON>andler(BaseHTTPRequestHandler):
    """HTTP request handler for the button interface."""
    
    def do_GET(self):
        """Handle GET requests."""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/' or parsed_path.path == '/index.html':
            # Serve the main button interface
            self.serve_html_file('activation_button.html')
        elif parsed_path.path == '/status':
            # Return current mode status
            self.send_json_response({'mode': activation_mode})
        else:
            self.send_404()
    
    def do_POST(self):
        """Handle POST requests."""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/toggle-mode':
            # Toggle the activation mode
            global activation_mode
            
            if activation_mode == "LISTENING":
                activation_mode = "RESPONSIVE"
                logger.info("🟢 MODE CHANGED: RESPONSIVE - Juno can now respond")
            else:
                activation_mode = "LISTENING"
                logger.info("🔴 MODE CHANGED: LISTENING - Juno is now passive")
            
            # Notify any registered callbacks
            for callback in mode_change_callbacks:
                try:
                    callback(activation_mode)
                except Exception as e:
                    logger.error(f"Error calling mode change callback: {e}")
            
            self.send_json_response({
                'success': True, 
                'mode': activation_mode,
                'message': f'Mode changed to {activation_mode}'
            })
        else:
            self.send_404()
    
    def serve_html_file(self, filename):
        """Serve an HTML file."""
        try:
            file_path = os.path.join(os.path.dirname(__file__), filename)
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.send_header('Content-length', str(len(content)))
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_404()
        except Exception as e:
            logger.error(f"Error serving HTML file {filename}: {e}")
            self.send_500()
    
    def send_json_response(self, data):
        """Send a JSON response."""
        response = json.dumps(data)
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Content-length', str(len(response)))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(response.encode('utf-8'))
    
    def send_404(self):
        """Send a 404 response."""
        self.send_response(404)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'Not Found')
    
    def send_500(self):
        """Send a 500 response."""
        self.send_response(500)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'Internal Server Error')
    
    def log_message(self, format, *args):
        """Override to use our logger."""
        logger.info(f"HTTP: {format % args}")

def start_button_server(port=8080):
    """Start the button server in a separate thread."""
    def run_server():
        server = HTTPServer(('localhost', port), ButtonHandler)
        logger.info(f"🌐 Button server started at http://localhost:{port}")
        logger.info(f"🎯 Open http://localhost:{port} to control Juno activation")
        server.serve_forever()
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    return server_thread

def register_mode_change_callback(callback):
    """Register a callback to be called when mode changes."""
    mode_change_callbacks.append(callback)

def get_current_mode():
    """Get the current activation mode."""
    return activation_mode

def set_mode(mode):
    """Set the activation mode programmatically."""
    global activation_mode
    if mode in ["LISTENING", "RESPONSIVE"]:
        activation_mode = mode
        logger.info(f"Mode set to: {activation_mode}")
        
        # Notify callbacks
        for callback in mode_change_callbacks:
            try:
                callback(activation_mode)
            except Exception as e:
                logger.error(f"Error calling mode change callback: {e}")
        
        return True
    return False

if __name__ == "__main__":
    # Test the server standalone
    logging.basicConfig(level=logging.INFO)
    logger.info("Starting Juno Button Server...")
    
    def test_callback(mode):
        print(f"Mode changed to: {mode}")
    
    register_mode_change_callback(test_callback)
    start_button_server(8080)
    
    try:
        while True:
            import time
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Server stopped")