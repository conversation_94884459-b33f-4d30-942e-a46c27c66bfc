import pvporcupine
import numpy as np
from typing import Callable, Optional
import logging

class JunoWakeWordDetector:
    """
    Wake word detector for "Juno" using Picovoice Porcupine.
    Processes audio chunks and triggers callback when wake word is detected.
    """
    
    def __init__(self, access_key: str, keyword_path: str, callback: Optional[Callable] = None):
        """
        Initialize the wake word detector.
        
        Args:
            access_key: Picovoice access key
            keyword_path: Path to the .ppn file for "Juno" wake word
            callback: Function to call when wake word is detected
        """
        self.access_key = access_key
        self.keyword_path = keyword_path
        self.callback = callback
        self.porcupine = None
        self.is_active = False
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
        
    def start(self) -> bool:
        """
        Start the wake word detector.
        
        Returns:
            bool: True if started successfully, False otherwise
        """
        try:
            # Initialize Porcupine with custom keyword
            self.porcupine = pvporcupine.create(
                access_key=self.access_key,
                keyword_paths=[self.keyword_path]
            )
            
            self.is_active = True
            self.logger.info(f"Wake word detector started. Sample rate: {self.porcupine.sample_rate}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start wake word detector: {e}")
            return False
    
    def stop(self):
        """
        Stop the wake word detector and clean up resources.
        """
        if self.porcupine:
            self.porcupine.delete()
            self.porcupine = None
        
        self.is_active = False
        self.logger.info("Wake word detector stopped")
    
    def process_audio(self, audio_chunk: np.ndarray) -> bool:
        """
        Process audio chunk for wake word detection.
        
        Args:
            audio_chunk: Audio data as numpy array (16-bit PCM)
            
        Returns:
            bool: True if wake word detected, False otherwise
        """
        if not self.is_active or not self.porcupine:
            return False
        
        try:
            # Ensure audio is in the correct format (16-bit PCM)
            if audio_chunk.dtype != np.int16:
                # Convert float to int16 if needed
                if audio_chunk.dtype == np.float32 or audio_chunk.dtype == np.float64:
                    audio_chunk = (audio_chunk * 32767).astype(np.int16)
                else:
                    audio_chunk = audio_chunk.astype(np.int16)
            
            # Process the audio chunk
            keyword_index = self.porcupine.process(audio_chunk)
            
            # Check if wake word was detected
            if keyword_index >= 0:
                self.logger.info("Wake word 'Juno' detected!")
                
                # Trigger callback if provided
                if self.callback:
                    self.callback()
                
                return True
                
        except Exception as e:
            self.logger.error(f"Error processing audio for wake word detection: {e}")
        
        return False
    
    @property
    def sample_rate(self) -> int:
        """
        Get the required sample rate for the wake word detector.
        
        Returns:
            int: Sample rate in Hz
        """
        if self.porcupine:
            return self.porcupine.sample_rate
        return 16000  # Default Porcupine sample rate
    
    @property
    def frame_length(self) -> int:
        """
        Get the required frame length for audio processing.
        
        Returns:
            int: Frame length in samples
        """
        if self.porcupine:
            return self.porcupine.frame_length
        return 512  # Default Porcupine frame length
    
    def set_callback(self, callback: Callable):
        """
        Set or update the wake word detection callback.
        
        Args:
            callback: Function to call when wake word is detected
        """
        self.callback = callback
        self.logger.info("Wake word callback updated")