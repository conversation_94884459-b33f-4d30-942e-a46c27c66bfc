#!/usr/bin/env python3
"""
Speechmatics STT Integration for LiveKit Agents

This module implements Speechmatics STT integration for LiveKit agents
with support for Arabic-English multilingual recognition and real-time streaming.

Features:
- Real-time Arabic-English language detection
- Professional-grade accuracy
- Low latency streaming
- Language markers in transcription output
- Compatible with existing LiveKit agent architecture
"""

import asyncio
import logging
import json
import io
from typing import Optional, List, Dict, Any, AsyncIterator
import websockets
import wave
from livekit.agents import stt
from livekit.agents.utils import AudioBuffer

# Configure logging
logger = logging.getLogger(__name__)

class SpeechmaticsSTT(stt.STT):
    """
    Speechmatics STT implementation for LiveKit agents.

    This class provides real-time speech-to-text using Speechmatics API
    with support for Arabic-English multilingual recognition.
    """

    def __init__(
        self,
        *,
        api_key: str,
        language: str = "en",
        enable_partials: bool = True,
        sample_rate: int = 16000,
        add_language_markers: bool = True,
        supported_languages: List[str] = ["en", "ar"],
        url: str = "wss://eu2.rt.speechmatics.com/v2",
        **kwargs
    ):
        """
        Initialize Speechmatics STT.

        Args:
            api_key: Speechmatics API key
            language: Primary language code (en, ar, auto for detection)
            enable_partials: Enable partial transcript results
            sample_rate: Audio sample rate (16000 recommended)
            add_language_markers: Add language markers to output
            supported_languages: List of supported language codes
            url: Speechmatics WebSocket URL
            **kwargs: Additional arguments
        """
        super().__init__(
            capabilities=stt.STTCapabilities(
                streaming=True,
                interim_results=enable_partials
            )
        )

        self._api_key = api_key
        self._language = language
        self._enable_partials = enable_partials
        self._sample_rate = sample_rate
        self._add_language_markers = add_language_markers
        self._supported_languages = supported_languages
        self._url = url

        # Language markers for output formatting
        self._language_markers = {
            "en": "[EN]",
            "ar": "[AR]"
        }

        logger.info(f"Initialized SpeechmaticsSTT with language={language}, "
                   f"partials={enable_partials}, sample_rate={sample_rate}")

    async def _recognize_impl(
        self,
        buffer: AudioBuffer,
        *,
        language: Optional[str] = None,
        conn_options=None,
    ) -> stt.SpeechEvent:
        """
        Implement speech recognition using Speechmatics WebSocket API.

        Args:
            buffer: Audio buffer to transcribe
            language: Language override (optional)
            conn_options: Connection options (optional)

        Returns:
            SpeechEvent with transcription results
        """
        logger.info(f"🎤 Processing audio buffer of size: {len(buffer.data)}")

        # Use provided language or default
        target_language = language or self._language

        try:
            # Convert audio buffer to WAV format for Speechmatics
            wav_data = self._convert_to_wav(buffer)

            # Connect to Speechmatics and get transcription
            transcript_result = await self._transcribe_with_speechmatics(
                wav_data, target_language
            )

            if transcript_result:
                # Process the result and add language markers if enabled
                processed_text = self._process_transcript(
                    transcript_result["text"],
                    transcript_result.get("language", target_language)
                )

                logger.info(f"✨ Speechmatics result: '{processed_text}'")

                # Create speech data
                speech_data = stt.SpeechData(
                    text=processed_text,
                    language=transcript_result.get("language", target_language),
                    confidence=transcript_result.get("confidence", 1.0)
                )

                return stt.SpeechEvent(
                    type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                    alternatives=[speech_data]
                )
            else:
                logger.warning("⚠️ No transcript result from Speechmatics")
                return stt.SpeechEvent(
                    type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                    alternatives=[]
                )

        except Exception as e:
            logger.error(f"❌ Speechmatics STT error: {e}")
            return stt.SpeechEvent(
                type=stt.SpeechEventType.FINAL_TRANSCRIPT,
                alternatives=[]
            )

    def _convert_to_wav(self, buffer: AudioBuffer) -> bytes:
        """
        Convert audio buffer to WAV format for Speechmatics.

        Args:
            buffer: Input audio buffer

        Returns:
            WAV-formatted audio data
        """
        try:
            # Create WAV file in memory
            wav_buffer = io.BytesIO()

            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(self._sample_rate)

                # Ensure we have bytes data
                if hasattr(buffer, 'data'):
                    audio_data = buffer.data
                else:
                    audio_data = bytes(buffer)

                wav_file.writeframes(audio_data)

            wav_data = wav_buffer.getvalue()
            logger.debug(f"🔄 Converted buffer to WAV: {len(wav_data)} bytes")
            return wav_data

        except Exception as e:
            logger.error(f"❌ WAV conversion error: {e}")
            # Return empty WAV as fallback
            wav_buffer = io.BytesIO()
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(self._sample_rate)
                wav_file.writeframes(b'')
            return wav_buffer.getvalue()

    async def _transcribe_with_speechmatics(
        self,
        audio_data: bytes,
        language: str
    ) -> Optional[Dict[str, Any]]:
        """
        Transcribe audio using Speechmatics WebSocket API.

        Args:
            audio_data: WAV audio data
            language: Target language code

        Returns:
            Transcription result dictionary or None
        """
        # Build WebSocket URL with language
        ws_url = f"{self._url}/{language}"

        headers = {
            "Authorization": f"Bearer {self._api_key}"
        }

        try:
            async with websockets.connect(ws_url, extra_headers=headers) as websocket:
                # Send start message
                start_message = {
                    "message": "StartRecognition",
                    "audio_format": {
                        "type": "file",
                        "format": "wav"
                    },
                    "transcription_config": {
                        "language": language,
                        "enable_partials": self._enable_partials,
                        "max_delay": 2
                    }
                }

                await websocket.send(json.dumps(start_message))
                logger.debug("📤 Sent StartRecognition message")

                # Send audio data
                await websocket.send(audio_data)
                logger.debug(f"📤 Sent audio data: {len(audio_data)} bytes")

                # Send end message
                end_message = {"message": "EndOfStream"}
                await websocket.send(json.dumps(end_message))
                logger.debug("📤 Sent EndOfStream message")

                # Collect transcription results
                final_transcript = None

                async for message in websocket:
                    try:
                        data = json.loads(message)
                        message_type = data.get("message")

                        if message_type == "AddTranscript":
                            # Final transcript
                            metadata = data.get("metadata", {})
                            transcript = metadata.get("transcript", "")

                            if transcript.strip():
                                final_transcript = {
                                    "text": transcript,
                                    "language": language,
                                    "confidence": 1.0  # Speechmatics doesn't provide confidence
                                }
                                logger.debug(f"📝 Final transcript: '{transcript}'")

                        elif message_type == "AddPartialTranscript" and self._enable_partials:
                            # Partial transcript (could be used for streaming)
                            metadata = data.get("metadata", {})
                            transcript = metadata.get("transcript", "")
                            logger.debug(f"📝 Partial transcript: '{transcript}'")

                        elif message_type == "EndOfTranscript":
                            # End of transcription
                            logger.debug("🏁 End of transcription")
                            break

                    except json.JSONDecodeError as e:
                        logger.warning(f"⚠️ Failed to parse message: {e}")
                        continue

                return final_transcript

        except Exception as e:
            logger.error(f"❌ Speechmatics WebSocket error: {e}")
            return None

    def _process_transcript(self, text: str, language: str) -> str:
        """
        Process transcript text and add language markers if enabled.

        Args:
            text: Raw transcript text
            language: Detected/specified language

        Returns:
            Processed text with optional language markers
        """
        if not self._add_language_markers or not text.strip():
            return text

        if language in self._language_markers:
            marker_start = self._language_markers[language]
            marker_end = f"[/{language.upper()}]"
            return f"{marker_start}{text.strip()}{marker_end}"

        return text

    @property
    def supported_languages(self) -> List[str]:
        """Return list of supported language codes."""
        return self._supported_languages.copy()


# Convenience function for easy integration
def create_speechmatics_stt(api_key: str, **kwargs) -> SpeechmaticsSTT:
    """
    Create a SpeechmaticsSTT instance with default settings for Arabic-English.

    Args:
        api_key: Speechmatics API key
        **kwargs: Additional arguments passed to SpeechmaticsSTT

    Returns:
        Configured SpeechmaticsSTT instance
    """
    default_config = {
        "language": "en",  # Start with English, can auto-detect
        "enable_partials": True,
        "sample_rate": 16000,
        "add_language_markers": True,
        "supported_languages": ["en", "ar"]
    }

    # Merge user config with defaults
    config = {**default_config, **kwargs}

    return SpeechmaticsSTT(api_key=api_key, **config)
