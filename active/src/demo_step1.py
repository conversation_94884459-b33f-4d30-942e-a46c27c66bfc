#!/usr/bin/env python3
"""
Step 1 Demo: Juno Audio Core Foundation

This demo script tests the core audio functionality including:
- Audio buffer operations
- Continuous streaming simulation
- Mode switching capabilities
- Quality monitoring
- Connection stability simulation

Run this to verify Step 1 implementation is working correctly.
"""

import asyncio
import logging
import time
import numpy as np
from typing import Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import our core components
from core.audio_buffer import CircularAudioBuffer, AudioChunk, AudioQualityMonitor
from core.juno_audio_core import JunoAudioCore, JunoMode, AudioConfig


class MockJobContext:
    """Mock LiveKit JobContext for testing."""
    
    def __init__(self):
        self.room = MockRoom()
    
    async def connect(self):
        """Mock connection."""
        logger.info("Mock: Connected to LiveKit room")
        await asyncio.sleep(0.1)


class MockRoom:
    """Mock LiveKit Room for testing."""
    
    def __init__(self):
        self.remote_participants = {}
        self.local_participant = MockParticipant()
        self.connection_state = "connected"
        self._event_handlers = {}
    
    def on(self, event: str, handler):
        """Register event handler."""
        self._event_handlers[event] = handler
    
    async def disconnect(self):
        """Mock disconnect."""
        logger.info("Mock: Disconnected from room")


class MockParticipant:
    """Mock LiveKit Participant."""
    
    def __init__(self):
        self.track_publications = {}
    
    async def publish_track(self, track):
        """Mock track publishing."""
        logger.info(f"Mock: Published track {track}")
    
    async def unpublish_track(self, track):
        """Mock track unpublishing."""
        logger.info(f"Mock: Unpublished track {track}")


def generate_test_audio(duration_seconds: float, frequency: float = 440.0, sample_rate: int = 16000) -> bytes:
    """Generate test audio data (sine wave)."""
    t = np.linspace(0, duration_seconds, int(sample_rate * duration_seconds))
    sine_wave = np.sin(2 * np.pi * frequency * t)
    
    # Convert to 16-bit integers
    audio_data = (sine_wave * 16384).astype(np.int16)
    return audio_data.tobytes()


def generate_silent_audio(duration_seconds: float, sample_rate: int = 16000) -> bytes:
    """Generate silent audio data."""
    num_samples = int(sample_rate * duration_seconds)
    return b'\x00' * (num_samples * 2)  # 16-bit audio


async def test_audio_buffer():
    """Test the audio buffer functionality."""
    logger.info("🔧 Testing Audio Buffer...")
    
    # Create buffer
    buffer = CircularAudioBuffer(max_duration_seconds=5.0, chunk_size_ms=100)
    
    # Test adding chunks
    test_audio = generate_test_audio(0.1, frequency=440.0)  # 100ms of 440Hz tone
    
    for i in range(10):
        chunk = buffer.add_chunk(test_audio)
        logger.info(f"Added chunk {i+1}, buffer size: {len(buffer)}")
        await asyncio.sleep(0.05)  # Simulate real-time processing
    
    # Test buffer stats
    stats = buffer.get_buffer_stats()
    logger.info(f"Buffer stats: {stats}")
    
    # Test recent audio retrieval
    recent_chunks = buffer.get_recent_audio(2.0)  # Last 2 seconds
    logger.info(f"Recent chunks (2s): {len(recent_chunks)}")
    
    logger.info("✅ Audio Buffer test completed")


async def test_quality_monitor():
    """Test the audio quality monitoring."""
    logger.info("🔧 Testing Audio Quality Monitor...")
    
    monitor = AudioQualityMonitor()
    
    # Test with different audio types
    test_cases = [
        ("Silent audio", generate_silent_audio(0.1)),
        ("440Hz tone", generate_test_audio(0.1, frequency=440.0)),
        ("1000Hz tone", generate_test_audio(0.1, frequency=1000.0)),
    ]
    
    for name, audio_data in test_cases:
        chunk = AudioChunk(audio_data, time.time())
        metrics = monitor.analyze_chunk(chunk)
        
        logger.info(f"{name} metrics:")
        logger.info(f"  RMS Volume: {metrics['rms_volume']:.4f}")
        logger.info(f"  Is Silent: {metrics['is_silent']}")
        logger.info(f"  Peak Amplitude: {metrics['peak_amplitude']:.4f}")
        logger.info(f"  Zero Crossing Rate: {metrics['zero_crossing_rate']:.4f}")
    
    logger.info("✅ Audio Quality Monitor test completed")


async def test_juno_audio_core():
    """Test the main Juno Audio Core functionality."""
    logger.info("🔧 Testing Juno Audio Core...")
    
    # Create configuration
    config = AudioConfig(
        sample_rate=16000,
        channels=1,
        buffer_duration_seconds=10.0,
        quality_monitoring=True,
        auto_reconnect=True
    )
    
    # Create audio core
    core = JunoAudioCore(config)
    
    # Test initialization with mock context
    mock_ctx = MockJobContext()
    
    # Mock the VAD loading to avoid actual model loading
    import unittest.mock
    with unittest.mock.patch('livekit.plugins.silero.VAD.load') as mock_vad:
        mock_vad.return_value = "mock_vad"
        await core.initialize(mock_ctx)
    
    logger.info(f"Initial mode: {core.mode.value}")
    logger.info(f"Connected: {core.is_connected}")
    
    # Test mode switching
    logger.info("Testing mode switching...")
    
    # Mock the room components for mode switching
    core.room.local_participant.track_publications = {}
    
    await core.activate_conversation_mode()
    logger.info(f"After activation: {core.mode.value}")
    
    await asyncio.sleep(1)  # Simulate some conversation time
    
    await core.deactivate_conversation_mode()
    logger.info(f"After deactivation: {core.mode.value}")
    
    # Test statistics
    stats = core.get_stats()
    logger.info("Core statistics:")
    for key, value in stats.items():
        if key != 'buffer_stats':
            logger.info(f"  {key}: {value}")
    
    # Test audio processing simulation
    logger.info("Simulating audio processing...")
    
    for i in range(5):
        # Generate different types of audio
        if i % 2 == 0:
            audio_data = generate_test_audio(0.1, frequency=440.0 + i * 100)
        else:
            audio_data = generate_silent_audio(0.1)
        
        chunk = core.audio_buffer.add_chunk(audio_data)
        logger.info(f"Processed audio chunk {i+1}")
        
        await asyncio.sleep(0.1)  # Simulate real-time processing
    
    # Final stats
    final_stats = core.get_stats()
    logger.info(f"Final buffer utilization: {final_stats['buffer_stats']['buffer_utilization']:.2%}")
    
    # Shutdown
    await core.shutdown()
    
    logger.info("✅ Juno Audio Core test completed")


async def test_callback_system():
    """Test the callback system for future extensibility."""
    logger.info("🔧 Testing Callback System...")
    
    buffer = CircularAudioBuffer()
    
    # Mock callbacks
    wake_word_calls = []
    transcription_calls = []
    analysis_calls = []
    
    async def mock_wake_word_callback(chunk):
        wake_word_calls.append(chunk)
        logger.info("Wake-word callback triggered")
    
    async def mock_transcription_callback(chunk):
        transcription_calls.append(chunk)
        logger.info("Transcription callback triggered")
    
    async def mock_analysis_callback(chunk):
        analysis_calls.append(chunk)
        logger.info("Analysis callback triggered")
    
    # Register callbacks
    buffer.register_wake_word_callback(mock_wake_word_callback)
    buffer.register_transcription_callback(mock_transcription_callback)
    buffer.register_analysis_callback(mock_analysis_callback)
    
    # Add audio chunk to trigger callbacks
    test_audio = generate_test_audio(0.1)
    buffer.add_chunk(test_audio)
    
    # Wait for async callbacks to complete
    await asyncio.sleep(0.1)
    
    # Verify callbacks were called
    assert len(wake_word_calls) == 1
    assert len(transcription_calls) == 1
    assert len(analysis_calls) == 1
    
    logger.info("✅ Callback System test completed")


async def run_continuous_simulation(duration_seconds: int = 30):
    """Run a continuous audio processing simulation."""
    logger.info(f"🔧 Running {duration_seconds}s continuous simulation...")
    
    config = AudioConfig(
        buffer_duration_seconds=30.0,
        quality_monitoring=True
    )
    
    core = JunoAudioCore(config)
    
    # Mock initialization
    import unittest.mock
    with unittest.mock.patch('livekit.plugins.silero.VAD.load') as mock_vad:
        mock_vad.return_value = "mock_vad"
        mock_ctx = MockJobContext()
        await core.initialize(mock_ctx)
    
    start_time = time.time()
    chunk_count = 0
    
    # Simulate continuous audio processing
    while time.time() - start_time < duration_seconds:
        # Generate varied audio content
        if chunk_count % 10 == 0:
            # Occasional silence
            audio_data = generate_silent_audio(0.1)
        else:
            # Various frequencies
            frequency = 440.0 + (chunk_count % 5) * 110.0
            audio_data = generate_test_audio(0.1, frequency=frequency)
        
        # Add to buffer
        core.audio_buffer.add_chunk(audio_data)
        chunk_count += 1
        
        # Simulate mode changes occasionally
        if chunk_count % 50 == 0:
            if core.mode == JunoMode.PASSIVE:
                await core.activate_conversation_mode()
            else:
                await core.deactivate_conversation_mode()
        
        # Log progress every 5 seconds
        elapsed = time.time() - start_time
        if chunk_count % 50 == 0:
            stats = core.get_stats()
            logger.info(f"Simulation progress: {elapsed:.1f}s, "
                       f"chunks: {chunk_count}, "
                       f"mode: {core.mode.value}, "
                       f"buffer: {stats['buffer_stats']['buffer_utilization']:.1%}")
        
        await asyncio.sleep(0.1)  # 100ms chunks
    
    # Final statistics
    final_stats = core.get_stats()
    logger.info("Simulation completed!")
    logger.info(f"Total chunks processed: {chunk_count}")
    logger.info(f"Mode changes: {final_stats['mode_changes']}")
    logger.info(f"Buffer utilization: {final_stats['buffer_stats']['buffer_utilization']:.1%}")
    logger.info(f"Uptime: {final_stats['uptime_seconds']:.1f}s")
    
    await core.shutdown()
    logger.info("✅ Continuous simulation completed")


async def main():
    """Run all Step 1 tests and demonstrations."""
    logger.info("🚀 Starting Juno Audio Core Step 1 Demo")
    logger.info("=" * 60)
    
    try:
        # Run individual component tests
        await test_audio_buffer()
        await asyncio.sleep(1)
        
        await test_quality_monitor()
        await asyncio.sleep(1)
        
        await test_callback_system()
        await asyncio.sleep(1)
        
        await test_juno_audio_core()
        await asyncio.sleep(1)
        
        # Run continuous simulation
        logger.info("🔄 Starting continuous processing simulation...")
        await run_continuous_simulation(duration_seconds=10)
        
        logger.info("=" * 60)
        logger.info("🎉 All Step 1 tests completed successfully!")
        logger.info("✅ Core LiveKit Audio Foundation is working correctly")
        logger.info("📋 Ready to proceed to Step 2: N8N Data Pipeline Foundation")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())
