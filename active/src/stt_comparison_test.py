#!/usr/bin/env python3
"""
STT Comparison Test: Whisper vs Speechmatics

This script allows you to test both OpenAI Whisper and Speechmatics STT
side by side to compare accuracy, speed, and language detection capabilities.

Usage:
    python stt_comparison_test.py --test-audio path/to/audio.wav
    python stt_comparison_test.py --live-test  # For live microphone testing
"""

import asyncio
import logging
import time
import argparse
from typing import Dict, Any, Optional
import wave
import io

# Import both STT implementations
from speechmatics_stt import create_speechmatics_stt

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API Keys
SPEECHMATICS_API_KEY = "v47WfwRBYZd51Ei2NTCwyAHHmZrrpHF3"
OPENAI_API_KEY = "*******************************************************************************************************************************************************************"

class STTComparison:
    """
    Compare different STT implementations for accuracy and performance.
    """
    
    def __init__(self):
        """Initialize STT comparison with both engines."""
        logger.info("🔧 Initializing STT Comparison Test...")
        
        # Initialize Speechmatics STT
        self.speechmatics_stt = create_speechmatics_stt(
            api_key=SPEECHMATICS_API_KEY,
            language="en",
            enable_partials=True,
            add_language_markers=True,
            supported_languages=["en", "ar"]
        )
        logger.info("✅ Speechmatics STT initialized")
        
        # Note: Whisper STT would be initialized here if we had the implementation
        # For now, we'll focus on testing Speechmatics
        logger.info("ℹ️ Whisper STT comparison will be added when needed")
    
    async def test_audio_file(self, audio_path: str) -> Dict[str, Any]:
        """
        Test both STT engines with an audio file.
        
        Args:
            audio_path: Path to audio file (WAV format recommended)
            
        Returns:
            Comparison results dictionary
        """
        logger.info(f"🎵 Testing audio file: {audio_path}")
        
        # Load audio file
        try:
            with wave.open(audio_path, 'rb') as wav_file:
                audio_data = wav_file.readframes(wav_file.getnframes())
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                
            logger.info(f"📊 Audio info: {sample_rate}Hz, {channels} channels, {len(audio_data)} bytes")
            
        except Exception as e:
            logger.error(f"❌ Failed to load audio file: {e}")
            return {"error": f"Failed to load audio: {e}"}
        
        results = {
            "audio_file": audio_path,
            "audio_info": {
                "sample_rate": sample_rate,
                "channels": channels,
                "size_bytes": len(audio_data)
            },
            "speechmatics": {},
            "whisper": {"status": "not_implemented"}
        }
        
        # Test Speechmatics STT
        logger.info("🎤 Testing Speechmatics STT...")
        speechmatics_start = time.time()
        
        try:
            # Create audio buffer (simplified for testing)
            from livekit.agents.utils import AudioBuffer
            import numpy as np
            
            # Convert audio data to numpy array (assuming 16-bit PCM)
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            audio_buffer = AudioBuffer(data=audio_array.tobytes(), sample_rate=sample_rate)
            
            # Run Speechmatics recognition
            speech_event = await self.speechmatics_stt._recognize_impl(audio_buffer)
            
            speechmatics_end = time.time()
            speechmatics_duration = speechmatics_end - speechmatics_start
            
            if speech_event.alternatives:
                speechmatics_result = speech_event.alternatives[0]
                results["speechmatics"] = {
                    "text": speechmatics_result.text,
                    "language": speechmatics_result.language,
                    "confidence": speechmatics_result.confidence,
                    "processing_time": speechmatics_duration,
                    "status": "success"
                }
                logger.info(f"✅ Speechmatics result: '{speechmatics_result.text}'")
            else:
                results["speechmatics"] = {
                    "status": "no_result",
                    "processing_time": speechmatics_duration
                }
                logger.warning("⚠️ Speechmatics returned no results")
                
        except Exception as e:
            speechmatics_end = time.time()
            speechmatics_duration = speechmatics_end - speechmatics_start
            results["speechmatics"] = {
                "status": "error",
                "error": str(e),
                "processing_time": speechmatics_duration
            }
            logger.error(f"❌ Speechmatics error: {e}")
        
        return results
    
    def print_comparison_results(self, results: Dict[str, Any]):
        """
        Print formatted comparison results.
        
        Args:
            results: Results dictionary from test_audio_file
        """
        print("\n" + "="*60)
        print("🔍 STT COMPARISON RESULTS")
        print("="*60)
        
        if "error" in results:
            print(f"❌ Error: {results['error']}")
            return
        
        print(f"📁 Audio File: {results['audio_file']}")
        print(f"📊 Audio Info: {results['audio_info']['sample_rate']}Hz, "
              f"{results['audio_info']['channels']} channels")
        
        print("\n🎤 SPEECHMATICS RESULTS:")
        sm_results = results["speechmatics"]
        if sm_results.get("status") == "success":
            print(f"   ✅ Text: '{sm_results['text']}'")
            print(f"   🌍 Language: {sm_results['language']}")
            print(f"   📈 Confidence: {sm_results['confidence']:.2f}")
            print(f"   ⏱️ Processing Time: {sm_results['processing_time']:.2f}s")
        else:
            print(f"   ❌ Status: {sm_results.get('status', 'unknown')}")
            if "error" in sm_results:
                print(f"   🚨 Error: {sm_results['error']}")
        
        print("\n🤖 WHISPER RESULTS:")
        whisper_results = results["whisper"]
        print(f"   ℹ️ Status: {whisper_results['status']}")
        
        print("\n" + "="*60)


async def main():
    """Main function to run STT comparison tests."""
    parser = argparse.ArgumentParser(description="Compare STT engines: Whisper vs Speechmatics")
    parser.add_argument("--test-audio", help="Path to audio file for testing")
    parser.add_argument("--live-test", action="store_true", help="Run live microphone test")
    
    args = parser.parse_args()
    
    # Initialize comparison
    comparison = STTComparison()
    
    if args.test_audio:
        # Test with audio file
        results = await comparison.test_audio_file(args.test_audio)
        comparison.print_comparison_results(results)
        
    elif args.live_test:
        print("🎙️ Live microphone testing not implemented yet")
        print("   Use --test-audio with a WAV file for now")
        
    else:
        print("❓ Please specify either --test-audio or --live-test")
        print("   Example: python stt_comparison_test.py --test-audio test.wav")


if __name__ == "__main__":
    asyncio.run(main())
