#!/usr/bin/env python3
"""
Simple Juno Voice Agent with Speechmatics STT

A simplified version that works with the current LiveKit setup.
This agent demonstrates Speechmatics STT integration for live testing.

Usage:
    python juno_simple_speechmatics.py

Features:
- Speechmatics STT for Arabic-English recognition
- Basic voice interaction
- Real-time testing
"""

import asyncio
import logging
import os
from livekit.agents import (
    JobContext,
    WorkerOptions,
    cli,
)
from livekit.plugins import openai, elevenlabs, silero

# Import our Speechmatics STT
from speechmatics_stt import create_speechmatics_stt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# API Keys
SPEECHMATICS_API_KEY = "v47WfwRBYZd51Ei2NTCwyAHHmZrrpHF3"
ELEVENLABS_API_KEY = "***************************************************"
OPENAI_API_KEY = "*******************************************************************************************************************************************************************"
ELEVENLABS_VOICE_ID = "QRq5hPRAKf5ZhSlTBH6r"

async def entrypoint(ctx: JobContext):
    """
    Simple voice agent entrypoint with Speechmatics STT.
    """
    logger.info("🚀 Starting Simple Juno with Speechmatics STT...")
    
    # Connect to the room
    await ctx.connect()
    logger.info("🔗 Connected to LiveKit room")
    
    # Create Speechmatics STT
    logger.info("🎤 Initializing Speechmatics STT...")
    try:
        speechmatics_stt = create_speechmatics_stt(
            api_key=SPEECHMATICS_API_KEY,
            language="en",
            enable_partials=True,
            add_language_markers=True,
            supported_languages=["en", "ar"]
        )
        logger.info("✅ Speechmatics STT initialized successfully!")
    except Exception as e:
        logger.error(f"❌ Failed to initialize Speechmatics STT: {e}")
        return
    
    # Create TTS
    logger.info("🔊 Initializing ElevenLabs TTS...")
    try:
        tts = elevenlabs.TTS(
            voice=ELEVENLABS_VOICE_ID,
            model="eleven_turbo_v2_5",
            api_key=ELEVENLABS_API_KEY,
        )
        logger.info("✅ ElevenLabs TTS initialized!")
    except Exception as e:
        logger.error(f"❌ Failed to initialize TTS: {e}")
        return
    
    # Test Speechmatics STT with a simple audio buffer
    logger.info("🧪 Testing Speechmatics STT...")
    try:
        # Create a simple test (this is just to verify the STT works)
        import numpy as np
        from livekit.agents.utils import AudioBuffer
        
        # Create a small test audio buffer (silence)
        test_audio = np.zeros(16000, dtype=np.int16)  # 1 second of silence
        test_buffer = AudioBuffer(data=test_audio.tobytes(), sample_rate=16000)
        
        # Test the STT
        result = await speechmatics_stt._recognize_impl(test_buffer)
        logger.info(f"🧪 STT test result: {result}")
        
    except Exception as e:
        logger.error(f"❌ STT test failed: {e}")
    
    # Generate a test TTS message
    logger.info("🔊 Testing TTS...")
    try:
        # Create a simple TTS test
        tts_stream = tts.synthesize("Hello, Speechmatics STT is working! مرحبا، تقنية سبيتشماتيكس تعمل!")
        logger.info("✅ TTS test successful!")
    except Exception as e:
        logger.error(f"❌ TTS test failed: {e}")
    
    logger.info("🎯 Simple Speechmatics integration test complete!")
    logger.info("📢 Ready for live voice testing!")
    logger.info("🔍 Check logs above for any errors")
    
    # Keep the agent running
    logger.info("⏳ Agent running... Press Ctrl+C to stop")
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("🛑 Agent stopped by user")


if __name__ == "__main__":
    # Run the simple agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        ),
    )
