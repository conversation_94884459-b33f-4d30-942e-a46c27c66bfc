#!/usr/bin/env python3
"""
Juno Live Voice Agent with Speechmatics STT

Ready-to-run live voice agent using Speechmatics for immediate testing.
This agent is configured for live voice interaction with Arabic-English support.

Usage:
    python juno_live_speechmatics.py

Features:
- Live Speechmatics STT with Arabic-English detection
- Wake word detection ("Juno")
- ElevenLabs TTS for responses
- Real-time voice interaction
"""

import asyncio
import logging
import os
from typing import Annotated
from livekit.agents import (
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli,
    llm,
)
from livekit.agents import VoiceAssistant
from livekit.plugins import openai, elevenlabs, silero

# Import our Speechmatics STT
from speechmatics_stt import create_speechmatics_stt

# Configure logging for live testing
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# API Keys - Ready for immediate use
SPEECHMATICS_API_KEY = "v47WfwRBYZd51Ei2NTCwyAHHmZrrpHF3"
ELEVENLABS_API_KEY = "***************************************************"
OPENAI_API_KEY = "*******************************************************************************************************************************************************************"

# Your preferred ElevenLabs voice
ELEVENLABS_VOICE_ID = "QRq5hPRAKf5ZhSlTBH6r"

async def entrypoint(ctx: JobContext):
    """
    Live voice agent entrypoint with Speechmatics STT.
    """
    logger.info("🚀 Starting Juno LIVE Voice Agent with Speechmatics STT...")
    logger.info("🎯 Ready for immediate voice testing!")

    # Connect to the room
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)
    logger.info("🔗 Connected to LiveKit room - Ready for voice input!")

    # Create Speechmatics STT with optimized settings for live use
    logger.info("🎤 Initializing Speechmatics STT for live testing...")
    speechmatics_stt = create_speechmatics_stt(
        api_key=SPEECHMATICS_API_KEY,
        language="en",  # Primary language, auto-detects Arabic
        enable_partials=True,  # Real-time partial results
        add_language_markers=True,  # [EN]/[AR] markers
        supported_languages=["en", "ar"],
        sample_rate=16000  # Optimized for LiveKit
    )
    logger.info("✅ Speechmatics STT ready for Arabic-English detection!")

    # Create LLM with optimized settings for live interaction
    assistant_llm = openai.LLM(
        model="gpt-4o-mini",
        temperature=0.1,  # Consistent responses
        api_key=OPENAI_API_KEY
    )
    logger.info("🧠 OpenAI LLM ready for conversation!")

    # Create ElevenLabs TTS with your preferred voice
    assistant_tts = elevenlabs.TTS(
        voice=ELEVENLABS_VOICE_ID,
        model="eleven_turbo_v2_5",  # Fast model for live interaction
        api_key=ELEVENLABS_API_KEY,
    )
    logger.info("🔊 ElevenLabs TTS ready with your preferred voice!")

    # Create Voice Assistant with Speechmatics
    assistant = VoiceAssistant(
        vad=silero.VAD.load(),  # Voice activity detection
        stt=speechmatics_stt,   # Using Speechmatics STT
        llm=assistant_llm,
        tts=assistant_tts,
        chat_ctx=llm.ChatContext().append(
            role="system",
            text="""You are Juno, a helpful AI meeting assistant with Speechmatics STT.

LIVE TESTING MODE:
- You are now using Speechmatics STT for improved Arabic-English recognition
- Test phrases: "Juno, hello", "جونو، مرحبا", "Juno, مرحبا how are you?"
- You should notice improved accuracy, especially for Arabic speech

BEHAVIOR:
- PASSIVE MODE by default - only listen and transcribe
- ONLY respond when someone says "Juno" followed by a question
- When activated, provide helpful response, then return to passive mode
- Keep responses concise and professional

LANGUAGE SUPPORT:
- Respond in the same language the user spoke
- Arabic responses for Arabic input: "مرحبا، كيف يمكنني مساعدتك؟"
- English responses for English input: "Hello, how can I help you?"
- Handle Arabic-English code-switching naturally

TESTING FEEDBACK:
- Mention when you detect language switches
- Acknowledge if you notice improved recognition quality

GREETING:
"Hi, Juno Online with Speechmatics STT! مرحبا أنا جونو مع تقنية سبيتشماتيكس"
"""
        ),
    )

    logger.info("🤖 Voice Assistant created with Speechmatics STT!")

    # Start the assistant
    assistant.start(ctx.room)
    logger.info("✅ Juno LIVE Voice Agent is ready for testing!")

    # Generate initial greeting to confirm everything works
    await assistant.say(
        "Hi, Juno Online with Speechmatics STT! مرحبا أنا جونو مع تقنية سبيتشماتيكس",
        allow_interruptions=True
    )

    # Live testing instructions
    logger.info("🎯 LIVE TESTING READY!")
    logger.info("📢 Try these test phrases:")
    logger.info("   English: 'Juno, hello how are you?'")
    logger.info("   Arabic: 'جونو، مرحبا كيف حالك؟'")
    logger.info("   Mixed: 'Juno, مرحبا how are you today?'")
    logger.info("🔍 Watch for [EN]/[AR] language markers in logs")
    logger.info("⚡ Notice improved Arabic recognition vs Whisper")


if __name__ == "__main__":
    # Run the live agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        ),
    )
