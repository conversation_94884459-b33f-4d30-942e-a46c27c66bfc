#!/usr/bin/env python3
"""
Juno FINAL: ElevenLabs ONLY Agent - Complete System Reboot

This is the DEFINITIVE agent that uses ONLY ElevenLabs TTS.
No fallbacks, no Cartesia, no confusion.

Features:
- OpenAI Whisper STT with Arabic-English language detection
- ElevenLabs TTS ONLY with specific voice ID
- No fallback mechanisms whatsoever
- Clear logging to verify TTS provider
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerO<PERSON>s, cli, Agent, AgentSession
from livekit.plugins import openai, silero, elevenlabs

# Import our custom Whisper STT with language detection (WORKING CONFIGURATION)
from whisper_language_stt import create_whisper_language_stt

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections.
    """
    logger.info("🔥 SYSTEM REBOOT: Prewarming <PERSON> with ElevenLabs ONLY...")
    logger.info("- Custom Whisper STT (WORKING Arabic-English detection)")
    logger.info("- OpenAI GPT-4o-mini LLM")
    logger.info("- ElevenLabs TTS ONLY (NO CARTESIA)")
    logger.info("- Silero VAD")
    proc.wait_for_participant = False
    logger.info("🔥 SYSTEM REBOOT: Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for Juno FINAL with ElevenLabs ONLY.
    """
    logger.info("🔥 SYSTEM REBOOT: Starting Juno FINAL with ElevenLabs ONLY...")
    logger.info("🚫 NO CARTESIA - ElevenLabs ONLY MODE")

    # Connect to the room first
    await ctx.connect()
    logger.info("✅ Connected to LiveKit room")

    # Create the Juno agent with improved professional system message
    agent = Agent(
        instructions="""You are Juno, a sophisticated multilingual meeting assistant AI designed to participate in Arabic-English business meetings and conversations. Your primary function is to serve as an intelligent, passive observer who can be activated on-demand to provide assistance.

Default State:
You are in PASSIVE MODE by default. In this mode, you only listen and transcribe. DO NOT respond to anything unless specifically activated.

Activation Rule:
You ONLY activate and respond when you detect the exact phrase: "Juno please activate conversation mode"
If you hear any other words, phrases, or questions - DO NOT respond at all, just remain silent.
When activated, respond to ONE complete question/request, then automatically return to PASSIVE MODE.
Do NOT announce mode changes during the conversation.

Language Handling:
You will receive transcripts with language markers like [EN]text[/EN] and [AR]text[/AR].
Process both English and Arabic content naturally.
ALWAYS respond in the same language as the user's question.
If the user speaks in Arabic, respond in Arabic.
If the user speaks in English, respond in English.
Handle code-switching (mixing languages) gracefully.

Voice Rules (when activated):
Keep responses brief and conversational.
Be friendly and helpful in both languages.
Don't use markdown or special characters in responses.
Don't read out language markers like [EN] or [AR].
Use natural pronunciation for each language.

Processing Instructions:
1. Carefully listen to the conversation.
2. Identify if and when the activation phrase is used.
3. If the activation phrase is detected, locate the question or request that immediately follows it.
4. Prepare a response to that specific question or request in the appropriate language.
5. If no activation phrase is found, do not generate any response.

Remember: Only respond when explicitly activated, then return to passive listening mode immediately after responding."""
    )

    # Initialize components with STRICT ElevenLabs-only configuration
    try:
        # Initialize WORKING custom Whisper STT with Arabic-English detection
        # Note: This is the EXACT configuration that was working for Arabic detection
        whisper_stt = create_whisper_language_stt(
            model="whisper-1",
            detect_language=True,
            supported_languages=["en", "ar"],
            add_language_markers=True
        )
        logger.info("✅ CUSTOM WHISPER STT: Initialized with WORKING Arabic-English detection")
        logger.info("🔄 RESTORED: Using proven working configuration for Arabic")

        # Initialize ElevenLabs TTS with specific voice ID - NO ALTERNATIVES
        elevenlabs_tts = elevenlabs.TTS(
            model="eleven_turbo_v2_5",
            voice_id="QRq5hPRAKf5ZhSlTBH6r",  # Your specific voice ID
            language="en",
            enable_ssml_parsing=True,
        )
        logger.info("✅ ELEVENLABS TTS: Initialized with voice ID QRq5hPRAKf5ZhSlTBH6r")
        logger.info("🚫 NO CARTESIA: ElevenLabs is the ONLY TTS provider")

        # Create agent session with WORKING STT + ElevenLabs TTS
        session = AgentSession(
            vad=silero.VAD.load(),
            stt=whisper_stt,  # WORKING: Custom Whisper STT with Arabic-English detection
            llm=openai.LLM(model="gpt-4.1-mini", temperature=0.1),
            tts=elevenlabs_tts,  # ONLY ElevenLabs - no list, no fallbacks
        )
        logger.info("✅ AGENT SESSION: Created with ElevenLabs ONLY")

    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR: Cannot initialize ElevenLabs TTS: {e}")
        logger.error("❌ SYSTEM HALT: No fallbacks allowed - fix ElevenLabs configuration")
        raise e  # Fail completely rather than use Cartesia

    # Start the session
    await session.start(agent=agent, room=ctx.room)
    logger.info("✅ AGENT SESSION: Started successfully with ElevenLabs ONLY")

    # Send short initial greeting
    await session.say(
        "Hi, Juno Online and in Passive Mode. مرحبا أنا جونو"
    )

    logger.info("🎉 JUNO FINAL: Ready with OPTIMIZED STT + ElevenLabs TTS!")
    logger.info("🔄 STT PROVIDER: OpenAI Whisper (optimized for Arabic-English)")
    logger.info("🔊 TTS PROVIDER: ElevenLabs ONLY (voice: QRq5hPRAKf5ZhSlTBH6r)")
    logger.info("🚫 NO CARTESIA: System configured to prevent any fallbacks")
    logger.info("📝 Test with Arabic: 'مرحبا جونو، كيف حالك اليوم؟'")
    logger.info("📝 Test with English: 'Hello Juno, how are you today?'")
    logger.info("🌍 MULTILINGUAL: Perfect Arabic-English code-switching support!")

if __name__ == "__main__":
    # Verify environment variables
    required_env_vars = [
        "OPENAI_API_KEY",
        "ELEVEN_API_KEY",
        # "DEEPGRAM_API_KEY",  # Not needed for OpenAI Whisper STT
        "LIVEKIT_URL",
        "LIVEKIT_API_KEY",
        "LIVEKIT_API_SECRET"
    ]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        logger.error(f"❌ MISSING ENV VARS: {missing_vars}")
        logger.error("❌ SYSTEM HALT: Fix environment configuration")
        exit(1)

    logger.info("✅ ENV VARS: All required variables found")
    logger.info(f"🔑 ElevenLabs API Key: {os.getenv('ELEVEN_API_KEY')[:10]}...")
    logger.info(f"🔄 OpenAI API Key: {os.getenv('OPENAI_API_KEY')[:10]}...")
    logger.info(f"🎤 Voice ID: QRq5hPRAKf5ZhSlTBH6r")
    logger.info("🔥 SYSTEM REBOOT: OPTIMIZED STT + ElevenLabs ONLY mode")

    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
