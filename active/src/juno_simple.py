#!/usr/bin/env python3
"""
Juno FINAL: ElevenLabs ONLY Agent - Complete System Reboot

This is the DEFINITIVE agent that uses ONLY ElevenLabs TTS.
No fallbacks, no Cartesia, no confusion.

Features:
- OpenAI Whisper STT with Arabic-English language detection
- ElevenLabs TTS ONLY with specific voice ID
- No fallback mechanisms whatsoever
- Clear logging to verify TTS provider
"""

import asyncio
import logging
import os
import json
import uuid
import aiohttp
from datetime import datetime
from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession
from livekit.plugins import openai, silero, elevenlabs

# Import our custom Whisper STT with language detection (WORKING CONFIGURATION)
from whisper_language_stt import create_whisper_language_stt

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TranscriptionBatcher:
    """
    Batches transcriptions for optimal storage in Supabase database.
    Optimized for long meetings with word-count and time-based batching.
    """
    def __init__(self, webhook_url: str = None, batch_word_limit: int = 85, batch_time_limit: int = 30):
        self.webhook_url = webhook_url or "https://n8n.srv753196.hstgr.cloud/webhook/livekit-master"
        self.meeting_id = str(uuid.uuid4())
        self.batch_word_limit = batch_word_limit  # Optimal for long meetings: 75-100 words
        self.batch_time_limit = batch_time_limit  # 30 seconds fallback

        # Batching state
        self.current_batch = []
        self.current_word_count = 0
        self.batch_start_time = datetime.utcnow()
        self.segment_counter = 1

        logger.info(f"📦 BATCH CONFIG: {batch_word_limit} words or {batch_time_limit}s intervals")

    def _count_words(self, text: str) -> int:
        """Count words in text, handling both English and Arabic."""
        return len(text.split())

    async def add_transcription(self, text: str, speaker: str = "participant", language: str = "en"):
        """
        Add transcription to current batch and send when batch is full.
        """
        if not text.strip():
            return

        word_count = self._count_words(text)
        current_time = datetime.utcnow()

        # Add to current batch
        self.current_batch.append({
            "text": text,
            "speaker": speaker,
            "language": language,
            "timestamp": current_time.isoformat() + "Z",
            "word_count": word_count
        })
        self.current_word_count += word_count

        # Check if we should send the batch
        time_elapsed = (current_time - self.batch_start_time).total_seconds()
        should_send = (
            self.current_word_count >= self.batch_word_limit or  # Word limit reached
            time_elapsed >= self.batch_time_limit or            # Time limit reached
            len(self.current_batch) >= 20                       # Safety: max 20 segments
        )

        if should_send:
            await self._send_batch()

        logger.info(f"📝 BATCH: {self.current_word_count}/{self.batch_word_limit} words, {len(self.current_batch)} segments")

    async def _send_batch(self):
        """
        Send current batch to Supabase via webhook.
        """
        if not self.current_batch:
            return

        # Calculate batch duration
        start_time = datetime.fromisoformat(self.current_batch[0]["timestamp"].replace("Z", "+00:00"))
        end_time = datetime.fromisoformat(self.current_batch[-1]["timestamp"].replace("Z", "+00:00"))
        duration_ms = int((end_time - start_time).total_seconds() * 1000)

        # Combine all text for the audio segment
        combined_text = " ".join([item["text"] for item in self.current_batch])

        # Detect primary language (majority language in batch)
        language_counts = {}
        for item in self.current_batch:
            lang = item["language"]
            language_counts[lang] = language_counts.get(lang, 0) + item["word_count"]
        primary_language = max(language_counts, key=language_counts.get)

        # Create audio segment payload for Supabase
        audio_segment = {
            "meeting_id": self.meeting_id,
            "start_time": start_time.isoformat() + "Z",
            "duration_ms": max(duration_ms, 2500),  # Minimum 2.5 seconds
            "audio_url": None,  # No audio file for transcription-only
            "transcription": combined_text,
            "language": primary_language,
            "speaker_id": self.current_batch[0]["speaker"],  # Use first speaker as primary
            "created_at": datetime.utcnow().isoformat() + "Z"
        }

        # Send to webhook (N8N will handle Supabase insertion)
        payload = {
            "type": "audio_segment",
            "audio_segment": audio_segment,
            "batch_info": {
                "segment_number": self.segment_counter,
                "word_count": self.current_word_count,
                "segment_count": len(self.current_batch),
                "duration_seconds": duration_ms / 1000
            }
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        logger.info(f"✅ BATCH #{self.segment_counter}: Sent {self.current_word_count} words to Supabase")
                    else:
                        logger.error(f"❌ BATCH #{self.segment_counter}: Failed, status: {response.status}")
        except Exception as e:
            logger.error(f"❌ BATCH #{self.segment_counter}: Error sending: {e}")

        # Reset batch
        self.current_batch = []
        self.current_word_count = 0
        self.batch_start_time = datetime.utcnow()
        self.segment_counter += 1

    async def force_send_batch(self):
        """
        Force send current batch (for meeting end).
        """
        if self.current_batch:
            logger.info(f"🔚 FINAL BATCH: Sending remaining {self.current_word_count} words")
            await self._send_batch()

class MeetingWebhookSender:
    """
    Sends structured meeting data to N8N webhook endpoint.
    """
    def __init__(self, webhook_url: str = None):
        self.webhook_url = webhook_url or "https://n8n.srv753196.hstgr.cloud/webhook/livekit-master"
        self.meeting_id = str(uuid.uuid4())
        self.events = []

    async def send_meeting_event(self, event_type: str, content: str, speaker: str = None):
        """
        Send a meeting event to N8N in the specified JSON format.
        """
        # Add event to our events list
        event = {
            "event_type": event_type,
            "content": content,
            "speaker": speaker,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        self.events.append(event)

        # Create the meeting payload
        payload = {
            "meeting_id": self.meeting_id,
            "meeting_datetime": datetime.utcnow().isoformat() + "Z",
            "summary": {
                "en": f"Meeting event: {content}",
                "ar": f"حدث الاجتماع: {content}"
            },
            "events": [event]  # Send just this event for real-time processing
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        logger.info(f"✅ WEBHOOK: Sent {event_type} event to N8N")
                    else:
                        logger.error(f"❌ WEBHOOK: Failed to send event, status: {response.status}")
        except Exception as e:
            logger.error(f"❌ WEBHOOK: Error sending event: {e}")

    async def send_full_meeting_summary(self):
        """
        Send complete meeting summary with all events.
        """
        if not self.events:
            return

        # Generate summary based on events
        en_summary = f"Meeting with {len(self.events)} events including decisions and questions to Juno."
        ar_summary = f"اجتماع مع {len(self.events)} أحداث تشمل قرارات وأسئلة لجونو."

        payload = {
            "meeting_id": self.meeting_id,
            "meeting_datetime": datetime.utcnow().isoformat() + "Z",
            "summary": {
                "en": en_summary,
                "ar": ar_summary
            },
            "events": self.events
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        logger.info(f"✅ WEBHOOK: Sent complete meeting summary to N8N")
                    else:
                        logger.error(f"❌ WEBHOOK: Failed to send summary, status: {response.status}")
        except Exception as e:
            logger.error(f"❌ WEBHOOK: Error sending summary: {e}")

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections.
    """
    logger.info("🔥 SYSTEM REBOOT: Prewarming Juno with ElevenLabs ONLY...")
    logger.info("- Custom Whisper STT (WORKING Arabic-English detection)")
    logger.info("- OpenAI GPT-4o-mini LLM")
    logger.info("- ElevenLabs TTS ONLY (NO CARTESIA)")
    logger.info("- Silero VAD")
    proc.wait_for_participant = False
    logger.info("🔥 SYSTEM REBOOT: Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for Juno FINAL with ElevenLabs ONLY.
    """
    logger.info("🔥 SYSTEM REBOOT: Starting Juno FINAL with ElevenLabs ONLY...")
    logger.info("🚫 NO CARTESIA - ElevenLabs ONLY MODE")

    # Connect to the room first
    await ctx.connect()
    logger.info("✅ Connected to LiveKit room")

    # Initialize webhook sender for N8N integration
    webhook_sender = MeetingWebhookSender()
    logger.info(f"🔗 WEBHOOK: Initialized for meeting ID: {webhook_sender.meeting_id}")

    # Initialize transcription batcher for Supabase storage
    transcription_batcher = TranscriptionBatcher(
        batch_word_limit=85,  # Optimal for long meetings
        batch_time_limit=30   # 30-second fallback
    )
    logger.info(f"📦 TRANSCRIPTION BATCHER: Initialized for meeting ID: {transcription_batcher.meeting_id}")
    logger.info(f"📦 BATCH STRATEGY: 85 words or 30 seconds - optimized for long meetings")



    # Initialize components with STRICT ElevenLabs-only configuration
    try:
        # Initialize WORKING custom Whisper STT with Arabic-English detection
        # Note: This is the EXACT configuration that was working for Arabic detection
        whisper_stt = create_whisper_language_stt(
            model="whisper-1",
            detect_language=True,
            supported_languages=["en", "ar"],
            add_language_markers=True
        )
        logger.info("✅ CUSTOM WHISPER STT: Initialized with WORKING Arabic-English detection")
        logger.info("🔄 RESTORED: Using proven working configuration for Arabic")

        # Initialize ElevenLabs TTS with specific voice ID - NO ALTERNATIVES
        elevenlabs_tts = elevenlabs.TTS(
            model="eleven_turbo_v2_5",
            voice_id="QRq5hPRAKf5ZhSlTBH6r",  # Your specific voice ID
            language="en",
            enable_ssml_parsing=True,
        )
        logger.info("✅ ELEVENLABS TTS: Initialized with voice ID QRq5hPRAKf5ZhSlTBH6r")
        logger.info("🚫 NO CARTESIA: ElevenLabs is the ONLY TTS provider")

        # Create custom STT wrapper that includes webhook detection and transcription batching
        class WebhookSTT:
            def __init__(self, base_stt, webhook_sender, transcription_batcher):
                self.base_stt = base_stt
                self.webhook_sender = webhook_sender
                self.transcription_batcher = transcription_batcher

            def _extract_language_from_text(self, text: str) -> str:
                """Extract language from text with language markers."""
                if '[AR]' in text or 'ar]' in text.lower():
                    return 'ar'
                elif '[EN]' in text or 'en]' in text.lower():
                    return 'en'
                else:
                    # Simple heuristic: if text contains Arabic characters
                    arabic_chars = any('\u0600' <= char <= '\u06FF' for char in text)
                    return 'ar' if arabic_chars else 'en'

            def _clean_text_for_batching(self, text: str) -> str:
                """Clean text by removing language markers for batching."""
                import re
                # Remove language markers like [EN], [AR], [/EN], [/AR]
                cleaned = re.sub(r'\[/?(?:EN|AR)\]', '', text, flags=re.IGNORECASE)
                return cleaned.strip()

            async def detect_meeting_events(self, user_speech_text: str):
                """
                Detect meeting events from user speech and send webhooks.
                """
                text = user_speech_text.lower()
                logger.info(f"🔍 WEBHOOK: Analyzing text: '{text}'")

                # Detect decision events
                if any(keyword in text for keyword in ["we decided", "decision", "let's do", "we'll go with", "agreed"]):
                    logger.info("🎯 WEBHOOK: Decision event detected!")
                    await self.webhook_sender.send_meeting_event(
                        event_type="decision",
                        content=user_speech_text,
                        speaker="participant"
                    )

                # Detect questions to Juno
                if "juno" in text and any(keyword in text for keyword in ["what do you think", "your opinion", "what's your view"]):
                    logger.info("🎯 WEBHOOK: Question to Juno detected!")
                    await self.webhook_sender.send_meeting_event(
                        event_type="question_to_juno",
                        content=user_speech_text,
                        speaker="participant"
                    )

                # Detect action items
                if any(keyword in text for keyword in ["action item", "todo", "follow up", "next step"]):
                    logger.info("🎯 WEBHOOK: Action item detected!")
                    await self.webhook_sender.send_meeting_event(
                        event_type="action_item",
                        content=user_speech_text,
                        speaker="participant"
                    )

            async def recognize(self, buffer, language=None, conn_options=None):
                # Call the base STT with all parameters
                result = await self.base_stt.recognize(buffer, language=language, conn_options=conn_options)

                # If we got text, process it for both events and batching
                if hasattr(result, 'text') and result.text:
                    original_text = result.text

                    # 1. Check for webhook events (using original text with markers)
                    await self.detect_meeting_events(original_text)

                    # 2. Add to transcription batch (using cleaned text)
                    cleaned_text = self._clean_text_for_batching(original_text)
                    if cleaned_text:  # Only batch if there's actual content
                        detected_language = self._extract_language_from_text(original_text)
                        await self.transcription_batcher.add_transcription(
                            text=cleaned_text,
                            speaker="participant",
                            language=detected_language
                        )

                        logger.info(f"📝 TRANSCRIPTION: Added {len(cleaned_text.split())} words ({detected_language})")

                return result

            def __getattr__(self, name):
                # Delegate all other attributes to the base STT
                return getattr(self.base_stt, name)

        # Wrap the STT with webhook detection and transcription batching
        webhook_stt = WebhookSTT(whisper_stt, webhook_sender, transcription_batcher)

        # Create the Juno agent with improved professional system message
        agent = Agent(
            instructions="""You are Juno, a sophisticated multilingual meeting assistant AI designed to participate in Arabic-English business meetings and conversations. Your primary function is to serve as an intelligent, passive observer who can be activated on-demand to provide assistance.

Default State:
You are in PASSIVE MODE by default. In this mode, you only listen and transcribe. DO NOT respond to anything unless specifically activated.

Activation Rules:
You activate and respond in TWO scenarios:
1. CONVERSATION MODE: When you detect "Juno please activate conversation mode" - enter active conversation mode and respond to ALL questions/requests until someone says "Juno go back to passive mode"
2. SINGLE QUESTION MODE: When you detect "Juno answer this question" followed by a question - respond immediately to that specific question, then return to passive mode

Mode Management:
- In PASSIVE MODE: Only listen, do not respond to anything except activation phrases
- In CONVERSATION MODE: Respond to all questions and requests normally until deactivated
- To exit CONVERSATION MODE: Wait for "Juno go back to passive mode" then return to passive listening

If you hear any other words, phrases, or questions while in PASSIVE MODE - DO NOT respond at all, just remain silent.
Do NOT announce mode changes during the conversation.

Language Handling:
You will receive transcripts with language markers like [EN]text[/EN] and [AR]text[/AR].
Process both English and Arabic content naturally.
ALWAYS respond in the same language as the user's question.
If the user speaks in Arabic, respond in Arabic.
If the user speaks in English, respond in English.
Handle code-switching (mixing languages) gracefully.

Voice Rules (when activated):
Keep responses brief and conversational.
Be friendly and helpful in both languages.
Don't use markdown or special characters in responses.
Don't read out language markers like [EN] or [AR].
Use natural pronunciation for each language.

Processing Instructions:
1. Carefully listen to the conversation.
2. Identify your current mode and any mode change commands:
   - "Juno please activate conversation mode" (enter conversation mode)
   - "Juno answer this question" (single question mode)
   - "Juno go back to passive mode" (return to passive mode)
3. In CONVERSATION MODE: Respond to all questions and requests normally
4. In SINGLE QUESTION MODE: Respond to the specific question, then return to passive mode
5. In PASSIVE MODE: Do not respond to anything except activation phrases
6. Prepare responses in the same language as the question/request.

Remember:
- CONVERSATION MODE stays active until explicitly deactivated
- SINGLE QUESTION MODE returns to passive after one response
- PASSIVE MODE only responds to activation phrases"""
        )

        # Create agent session with WORKING STT + ElevenLabs TTS + Webhook detection
        session = AgentSession(
            vad=silero.VAD.load(),
            stt=webhook_stt,  # WORKING: Custom Whisper STT with webhook detection
            llm=openai.LLM(model="gpt-4.1-mini", temperature=0.1),
            tts=elevenlabs_tts,  # ONLY ElevenLabs - no list, no fallbacks
        )
        logger.info("✅ AGENT SESSION: Created with ElevenLabs ONLY and webhook detection")

    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR: Cannot initialize ElevenLabs TTS: {e}")
        logger.error("❌ SYSTEM HALT: No fallbacks allowed - fix ElevenLabs configuration")
        raise e  # Fail completely rather than use Cartesia

    # Add shutdown callback to send final meeting summary and remaining batch
    async def send_final_summary():
        # Send any remaining transcription batch
        await transcription_batcher.force_send_batch()
        logger.info("📦 FINAL BATCH: Sent remaining transcriptions to Supabase")

        # Send final meeting summary
        await webhook_sender.send_full_meeting_summary()
        logger.info("📋 MEETING: Final summary sent to N8N")

    ctx.add_shutdown_callback(send_final_summary)

    # Start the session
    await session.start(agent=agent, room=ctx.room)
    logger.info("✅ AGENT SESSION: Started successfully with ElevenLabs ONLY and webhook detection")

    # Send short initial greeting
    await session.say(
        "Hi, Juno Online and in Passive Mode. مرحبا أنا جونو"
    )

    logger.info("🎉 JUNO FINAL: Ready with OPTIMIZED STT + ElevenLabs TTS + Supabase Batching!")
    logger.info("🔄 STT PROVIDER: OpenAI Whisper (optimized for Arabic-English)")
    logger.info("🔊 TTS PROVIDER: ElevenLabs ONLY (voice: QRq5hPRAKf5ZhSlTBH6r)")
    logger.info("🔗 WEBHOOK PROVIDER: N8N Direct Integration (bypassing Dify)")
    logger.info(f"📋 MEETING ID (Events): {webhook_sender.meeting_id}")
    logger.info(f"📦 MEETING ID (Transcriptions): {transcription_batcher.meeting_id}")
    logger.info("📦 TRANSCRIPTION BATCHING: 85 words or 30 seconds - OPTIMIZED FOR LONG MEETINGS")
    logger.info("🚫 NO CARTESIA: System configured to prevent any fallbacks")
    logger.info("📝 Test with Arabic: 'مرحبا جونو، كيف حالك اليوم؟'")
    logger.info("📝 Test with English: 'Hello Juno, how are you today?'")
    logger.info("🌍 MULTILINGUAL: Perfect Arabic-English code-switching support!")
    logger.info("🎯 WEBHOOK EVENTS: Detecting decisions, questions to Juno, and action items")
    logger.info("📦 SUPABASE STORAGE: Automatic transcription batching for audio_segments table")
    logger.info("🎯 WEBHOOK URL: https://n8n.srv753196.hstgr.cloud/webhook/livekit-master")
    logger.info("⚡ LONG MEETING OPTIMIZED: Efficient batching prevents data loss and reduces API calls")

if __name__ == "__main__":
    # Verify environment variables
    required_env_vars = [
        "OPENAI_API_KEY",
        "ELEVEN_API_KEY",
        # "DEEPGRAM_API_KEY",  # Not needed for OpenAI Whisper STT
        "LIVEKIT_URL",
        "LIVEKIT_API_KEY",
        "LIVEKIT_API_SECRET"
    ]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        logger.error(f"❌ MISSING ENV VARS: {missing_vars}")
        logger.error("❌ SYSTEM HALT: Fix environment configuration")
        exit(1)

    logger.info("✅ ENV VARS: All required variables found")
    logger.info(f"🔑 ElevenLabs API Key: {os.getenv('ELEVEN_API_KEY')[:10]}...")
    logger.info(f"🔄 OpenAI API Key: {os.getenv('OPENAI_API_KEY')[:10]}...")
    logger.info(f"🎤 Voice ID: QRq5hPRAKf5ZhSlTBH6r")
    logger.info("🔥 SYSTEM REBOOT: OPTIMIZED STT + ElevenLabs ONLY mode")

    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
