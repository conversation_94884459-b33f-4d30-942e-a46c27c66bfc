#!/usr/bin/env python3

import asyncio
import logging
import os
import httpx
import json

from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession, RunContext, function_tool

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Dify API Configuration
DIFY_API_URL = "https://api.dify.ai/v1"
DIFY_API_KEY = "app-X8v7YaWCGBH57SZgn2AyCsQU"

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections before the agent starts.
    This helps reduce latency when the agent first connects.
    """
    logger.info("Prewarming project management agent components...")
    proc.wait_for_participant = False
    logger.info("Prewarm completed successfully")

def clean_response_for_speech(text: str) -> str:
    """Remove all markdown formatting and special characters for clean speech"""
    import re

    # Remove markdown formatting
    text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # **bold** -> bold
    text = re.sub(r'\*([^*]+)\*', r'\1', text)      # *italic* -> italic
    text = re.sub(r'`([^`]+)`', r'\1', text)        # `code` -> code
    text = re.sub(r'#{1,6}\s*', '', text)           # Remove # headers

    # Clean up list formatting
    text = re.sub(r'^\s*[-*+]\s*', '', text, flags=re.MULTILINE)  # Remove bullet points
    text = re.sub(r'^\s*\d+\.\s*', '', text, flags=re.MULTILINE)  # Remove numbered lists

    # Remove other special characters that sound bad when spoken
    text = re.sub(r'[_~`]', '', text)               # Remove underscores, tildes, backticks
    text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)  # [link text](url) -> link text

    # Clean up extra whitespace
    text = re.sub(r'\n+', ' ', text)                # Multiple newlines -> single space
    text = re.sub(r'\s+', ' ', text)                # Multiple spaces -> single space
    text = text.strip()

    return text

async def call_dify_agent(user_message: str) -> str:
    """
    Call the Dify Project Management Builder agent and return clean response for speech
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{DIFY_API_URL}/chat-messages",
                headers={
                    "Authorization": f"Bearer {DIFY_API_KEY}",
                    "Content-Type": "application/json"
                },
                json={
                    "inputs": {},
                    "query": user_message,
                    "response_mode": "streaming",
                    "conversation_id": "",
                    "user": "livekit-user"
                }
            )

            if response.status_code == 200:
                # Handle streaming response with improved processing
                full_response = ""
                first_chunk_received = False

                for line in response.iter_lines():
                    if line:
                        line_text = line if isinstance(line, str) else line.decode('utf-8')
                        if line_text.startswith('data: '):
                            try:
                                data = json.loads(line_text[6:])  # Remove 'data: ' prefix

                                if data.get('event') == 'message':
                                    chunk = data.get('answer', '')
                                    if chunk:
                                        full_response += chunk

                                        # Log first chunk for debugging
                                        if not first_chunk_received:
                                            logger.info(f"First Dify chunk received: {chunk[:50]}...")
                                            first_chunk_received = True

                                elif data.get('event') == 'message_end':
                                    logger.info("Dify streaming completed")
                                    break
                                elif data.get('event') == 'error':
                                    logger.error(f"Dify streaming error: {data}")
                                    break

                            except json.JSONDecodeError as e:
                                logger.warning(f"Failed to parse streaming data: {line_text[:100]}")
                                continue

                # Process the complete response
                if full_response.strip():
                    clean_response = clean_response_for_speech(full_response)
                    logger.info(f"Dify complete response: {full_response[:100]}...")
                    logger.info(f"Cleaned for speech: {clean_response[:100]}...")
                    return clean_response
                else:
                    logger.warning("No content received from Dify streaming")
                    return "I'm having trouble understanding. Could you please rephrase that?"
            else:
                logger.error(f"Dify API error: {response.status_code} - {response.text}")
                return "Sorry, there was an error connecting to the system."

    except Exception as e:
        logger.error(f"Error calling Dify API: {e}")
        return "Sorry, there was an error processing your request."

def is_simple_query(user_message: str) -> bool:
    """Determine if a query can be handled locally to reduce latency"""
    simple_patterns = [
        "hello", "hi", "hey", "good morning", "good afternoon", "good evening",
        "thank you", "thanks", "bye", "goodbye", "see you",
        "yes", "no", "okay", "ok", "sure", "alright",
        "what can you do", "help", "what do you do"
    ]

    message_lower = user_message.lower().strip()

    # Check for exact matches or simple greetings
    for pattern in simple_patterns:
        if pattern in message_lower:
            return True

    # Check for very short responses (likely confirmations)
    if len(message_lower.split()) <= 2:
        return True

    return False

def handle_locally(user_message: str) -> str:
    """Handle simple queries locally for better responsiveness"""
    message_lower = user_message.lower().strip()

    # Greetings
    if any(greeting in message_lower for greeting in ["hello", "hi", "hey", "good morning", "good afternoon", "good evening"]):
        return "Hello! I'm Alex, your project management assistant. How can I help you with your projects today?"

    # Thanks
    if any(thanks in message_lower for thanks in ["thank you", "thanks"]):
        return "You're welcome! Is there anything else I can help you with?"

    # Goodbyes
    if any(bye in message_lower for bye in ["bye", "goodbye", "see you"]):
        return "Goodbye! Feel free to come back anytime you need help with your projects."

    # Confirmations
    if any(confirm in message_lower for confirm in ["yes", "okay", "ok", "sure", "alright"]):
        return "Great! What would you like to work on?"

    # Help requests
    if any(help_word in message_lower for help_word in ["help", "what can you do", "what do you do"]):
        return "I can help you create and manage projects. Just tell me you want to create a new project and I'll guide you through the process!"

    # Default for other simple queries
    return "I'm here to help with your projects. Would you like to create a new project?"

@function_tool
async def smart_conversation_handler(
    context: RunContext,
    user_message: str = ""
) -> str:
    """Intelligently route messages - local for simple queries, Dify for complex ones"""
    logger.info(f"Processing user message: {user_message}")

    # Handle simple queries locally for better responsiveness
    if is_simple_query(user_message):
        logger.info("Handling query locally for reduced latency")
        return handle_locally(user_message)

    # Route complex queries to Dify brain
    logger.info("Routing complex query to Dify brain")
    try:
        response = await call_dify_agent(user_message)
        logger.info("Dify brain response received successfully")
        return response
    except Exception as e:
        logger.error(f"Error calling Dify brain: {e}")
        return "Sorry, there was an error processing your request. Please try again."

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for the project management voice agent.
    """
    logger.info("Starting LiveKit Project Management Voice Agent...")

    # CRITICAL: Connect to the room first
    await ctx.connect()
    logger.info("Connected to LiveKit room")

    # Import plugins
    from livekit.plugins import deepgram, openai, cartesia, silero

    # Create the agent with intelligent routing
    agent = Agent(
        instructions="""You are Alex, a project management voice assistant with intelligent response routing.

For EVERY user input, use the smart_conversation_handler function which will:
- Handle simple queries (greetings, thanks, confirmations) locally for fast responses
- Route complex queries (project creation, detailed questions) to Dify for intelligent processing

Always use the smart_conversation_handler function for all user interactions.""",
        tools=[smart_conversation_handler]
    )

    # Create the agent session with all components
    session = AgentSession(
        vad=silero.VAD.load(),  # Use Silero VAD as recommended
        stt=deepgram.STT(model="nova-2-general", language="en"),
        llm=openai.LLM(model="gpt-4o-mini", temperature=0.7),
        tts=cartesia.TTS(model="sonic-english"),
    )

    # Start the session with the agent and room
    await session.start(agent=agent, room=ctx.room)
    logger.info("Project management agent session started successfully")

    # Generate initial greeting
    await session.generate_reply(
        instructions="Greet the user as Alex, your project management assistant, and ask how you can help them with their projects today"
    )
    logger.info("Project management voice agent is ready!")

if __name__ == "__main__":
    # Run the agent using LiveKit's CLI
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
