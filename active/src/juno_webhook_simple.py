#!/usr/bin/env python3
"""
Juno with Simple Webhook Detection
"""

import asyncio
import logging
import os
import json
import uuid
import aiohttp
from datetime import datetime
from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession
from livekit.plugins import openai, silero, elevenlabs

# Import our custom Whisper STT with language detection (WORKING CONFIGURATION)
from whisper_language_stt import create_whisper_language_stt

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global webhook sender
webhook_sender = None

class SimpleWebhookSender:
    """Simple webhook sender for N8N integration."""
    def __init__(self):
        self.webhook_url = "https://n8n.srv753196.hstgr.cloud/webhook/livekit-master"
        self.meeting_id = str(uuid.uuid4())
        
    async def send_event(self, event_type: str, content: str):
        """Send event to N8N webhook."""
        payload = {
            "meeting_id": self.meeting_id,
            "meeting_datetime": datetime.utcnow().isoformat() + "Z",
            "summary": {
                "en": f"Meeting event: {content}",
                "ar": f"حدث الاجتماع: {content}"
            },
            "events": [{
                "event_type": event_type,
                "content": content,
                "speaker": "participant",
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }]
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        logger.info(f"✅ WEBHOOK: Sent {event_type} event to N8N")
                    else:
                        logger.error(f"❌ WEBHOOK: Failed, status: {response.status}")
        except Exception as e:
            logger.error(f"❌ WEBHOOK: Error: {e}")

async def check_for_events(text: str):
    """Check text for webhook events."""
    global webhook_sender
    if not webhook_sender:
        return
        
    text_lower = text.lower()
    logger.info(f"🔍 WEBHOOK: Analyzing text: '{text_lower}'")
    
    # Check for decision events
    if any(keyword in text_lower for keyword in ["we decided", "decision", "agreed"]):
        logger.info("🎯 WEBHOOK: Decision detected!")
        await webhook_sender.send_event("decision", text)
    
    # Check for questions to Juno
    if "juno" in text_lower and any(keyword in text_lower for keyword in ["what do you think", "your opinion"]):
        logger.info("🎯 WEBHOOK: Question to Juno detected!")
        await webhook_sender.send_event("question_to_juno", text)
    
    # Check for action items
    if any(keyword in text_lower for keyword in ["action item", "todo", "follow up"]):
        logger.info("🎯 WEBHOOK: Action item detected!")
        await webhook_sender.send_event("action_item", text)

def prewarm(proc: JobContext):
    """Prewarm function."""
    logger.info("🔥 SYSTEM REBOOT: Prewarming Juno with ElevenLabs ONLY...")
    logger.info("- Custom Whisper STT (WORKING Arabic-English detection)")
    logger.info("- OpenAI GPT-4o-mini LLM")
    logger.info("- ElevenLabs TTS ONLY (NO CARTESIA)")
    logger.info("- Silero VAD")
    proc.wait_for_participant = False
    logger.info("🔥 SYSTEM REBOOT: Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """Main entrypoint."""
    global webhook_sender
    
    logger.info("🔥 SYSTEM REBOOT: Starting Juno FINAL with ElevenLabs ONLY...")
    logger.info("🚫 NO CARTESIA - ElevenLabs ONLY MODE")

    # Connect to the room first
    await ctx.connect()
    logger.info("✅ Connected to LiveKit room")

    # Initialize webhook sender
    webhook_sender = SimpleWebhookSender()
    logger.info(f"🔗 WEBHOOK: Initialized for meeting ID: {webhook_sender.meeting_id}")

    # Create the Juno agent
    agent = Agent(
        instructions="""You are Juno, a sophisticated multilingual meeting assistant AI designed to participate in Arabic-English business meetings and conversations. Your primary function is to serve as an intelligent, passive observer who can be activated on-demand to provide assistance.

Default State:
You are in PASSIVE MODE by default. In this mode, you only listen and transcribe. DO NOT respond to anything unless specifically activated.

Activation Rules:
You activate and respond in TWO scenarios:
1. CONVERSATION MODE: When you detect "Juno please activate conversation mode" - enter active conversation mode and respond to ALL questions/requests until someone says "Juno go back to passive mode"
2. SINGLE QUESTION MODE: When you detect "Juno answer this question" followed by a question - respond immediately to that specific question, then return to passive mode

Mode Management:
- In PASSIVE MODE: Only listen, do not respond to anything except activation phrases
- In CONVERSATION MODE: Respond to all questions and requests normally until deactivated
- To exit CONVERSATION MODE: Wait for "Juno go back to passive mode" then return to passive listening

If you hear any other words, phrases, or questions while in PASSIVE MODE - DO NOT respond at all, just remain silent.
Do NOT announce mode changes during the conversation.

Language Handling:
You will receive transcripts with language markers like [EN]text[/EN] and [AR]text[/AR].
Process both English and Arabic content naturally.
ALWAYS respond in the same language as the user's question.
If the user speaks in Arabic, respond in Arabic.
If the user speaks in English, respond in English.
Handle code-switching (mixing languages) gracefully.

Voice Rules (when activated):
Keep responses brief and conversational.
Be friendly and helpful in both languages.
Don't use markdown or special characters in responses.
Don't read out language markers like [EN] or [AR].
Use natural pronunciation for each language.

Processing Instructions:
1. Carefully listen to the conversation.
2. Identify your current mode and any mode change commands:
   - "Juno please activate conversation mode" (enter conversation mode)
   - "Juno answer this question" (single question mode)
   - "Juno go back to passive mode" (return to passive mode)
3. In CONVERSATION MODE: Respond to all questions and requests normally
4. In SINGLE QUESTION MODE: Respond to the specific question, then return to passive mode
5. In PASSIVE MODE: Do not respond to anything except activation phrases
6. Prepare responses in the same language as the question/request.

Remember:
- CONVERSATION MODE stays active until explicitly deactivated
- SINGLE QUESTION MODE returns to passive after one response
- PASSIVE MODE only responds to activation phrases"""
    )

    # Initialize components
    try:
        # Initialize WORKING custom Whisper STT with Arabic-English detection
        whisper_stt = create_whisper_language_stt(
            model="whisper-1",
            detect_language=True,
            supported_languages=["en", "ar"],
            add_language_markers=True
        )
        logger.info("✅ CUSTOM WHISPER STT: Initialized with WORKING Arabic-English detection")

        # Initialize ElevenLabs TTS with specific voice ID
        elevenlabs_tts = elevenlabs.TTS(
            model="eleven_turbo_v2_5",
            voice_id="QRq5hPRAKf5ZhSlTBH6r",
            language="en",
            enable_ssml_parsing=True,
        )
        logger.info("✅ ELEVENLABS TTS: Initialized with voice ID QRq5hPRAKf5ZhSlTBH6r")

        # Create agent session
        session = AgentSession(
            vad=silero.VAD.load(),
            stt=whisper_stt,
            llm=openai.LLM(model="gpt-4.1-mini", temperature=0.1),
            tts=elevenlabs_tts,
        )
        logger.info("✅ AGENT SESSION: Created with ElevenLabs ONLY")

    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR: {e}")
        raise e

    # Add event listener for user speech
    @session.on("user_speech_committed")
    async def on_user_speech(speech):
        logger.info(f"📝 USER SPEECH: {speech.text}")
        await check_for_events(speech.text)

    # Start the session
    await session.start(agent=agent, room=ctx.room)
    logger.info("✅ AGENT SESSION: Started successfully")

    # Send initial greeting
    await session.say("Hi, Juno Online and in Passive Mode. مرحبا أنا جونو")

    logger.info("🎉 JUNO: Ready with webhook detection!")
    logger.info("🎯 WEBHOOK URL: https://n8n.srv753196.hstgr.cloud/webhook/livekit-master")

if __name__ == "__main__":
    # Verify environment variables
    required_env_vars = [
        "OPENAI_API_KEY",
        "ELEVEN_API_KEY",
        "LIVEKIT_URL",
        "LIVEKIT_API_KEY",
        "LIVEKIT_API_SECRET"
    ]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        logger.error(f"❌ MISSING ENV VARS: {missing_vars}")
        exit(1)

    logger.info("✅ ENV VARS: All required variables found")
    logger.info(f"🔑 ElevenLabs API Key: {os.getenv('ELEVEN_API_KEY')[:10]}...")
    logger.info(f"🔄 OpenAI API Key: {os.getenv('OPENAI_API_KEY')[:10]}...")
    logger.info("🔥 SYSTEM REBOOT: OPTIMIZED STT + ElevenLabs ONLY mode")

    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
