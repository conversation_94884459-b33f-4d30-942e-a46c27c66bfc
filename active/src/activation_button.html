<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Juno Activation Button</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            margin-bottom: 2rem;
            font-size: 2.5rem;
        }
        .status {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            padding: 1rem;
            border-radius: 10px;
            font-weight: bold;
        }
        .listening {
            background: rgba(255, 107, 107, 0.3);
            border: 2px solid #ff6b6b;
        }
        .responsive {
            background: rgba(81, 207, 102, 0.3);
            border: 2px solid #51cf66;
        }
        .toggle-btn {
            font-size: 1.5rem;
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            min-width: 200px;
        }
        .toggle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .btn-listening {
            background: #51cf66;
            color: white;
        }
        .btn-responsive {
            background: #ff6b6b;
            color: white;
        }
        .instructions {
            margin-top: 2rem;
            font-size: 1rem;
            opacity: 0.8;
            line-height: 1.6;
        }
        .mode-description {
            margin-top: 1rem;
            font-size: 1.1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Juno Voice Assistant</h1>
        
        <div id="status" class="status listening">
            🔴 LISTENING MODE
        </div>
        
        <div class="mode-description">
            <span id="mode-desc">Juno is transcribing but not responding</span>
        </div>
        
        <button id="toggleBtn" class="toggle-btn btn-listening" onclick="toggleMode()">
            🟢 ACTIVATE RESPONSIVE
        </button>
        
        <div class="instructions">
            <p><strong>LISTENING MODE:</strong> Juno transcribes conversations but doesn't respond</p>
            <p><strong>RESPONSIVE MODE:</strong> Juno can respond to questions and participate</p>
            <p>Click the button above to toggle between modes</p>
        </div>
    </div>

    <script>
        let currentMode = 'LISTENING';
        
        async function toggleMode() {
            try {
                const response = await fetch('/toggle-mode', { 
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentMode = data.mode;
                    updateUI();
                    console.log('Mode changed to:', data.mode);
                } else {
                    console.error('Failed to toggle mode');
                }
            } catch (error) {
                console.error('Error toggling mode:', error);
                // Fallback to local toggle for demo purposes
                if (currentMode === 'LISTENING') {
                    currentMode = 'RESPONSIVE';
                } else {
                    currentMode = 'LISTENING';
                }
                updateUI();
            }
        }
        
        function updateUI() {
            const status = document.getElementById('status');
            const toggleBtn = document.getElementById('toggleBtn');
            const modeDesc = document.getElementById('mode-desc');
            
            if (currentMode === 'LISTENING') {
                status.className = 'status listening';
                status.textContent = '🔴 LISTENING MODE';
                toggleBtn.className = 'toggle-btn btn-listening';
                toggleBtn.textContent = '🟢 ACTIVATE RESPONSIVE';
                modeDesc.textContent = 'Juno is transcribing but not responding';
            } else {
                status.className = 'status responsive';
                status.textContent = '🟢 RESPONSIVE MODE';
                toggleBtn.className = 'toggle-btn btn-responsive';
                toggleBtn.textContent = '🔴 SWITCH TO LISTENING';
                modeDesc.textContent = 'Juno can respond to questions and participate';
            }
        }
        
        // Keyboard shortcut: Space bar to toggle
        document.addEventListener('keydown', function(event) {
            if (event.code === 'Space' && !event.target.matches('input, textarea')) {
                event.preventDefault();
                toggleMode();
            }
        });
        
        // Initialize UI and get current status
        async function initializeUI() {
            try {
                const response = await fetch('/status');
                if (response.ok) {
                    const data = await response.json();
                    currentMode = data.mode;
                }
            } catch (error) {
                console.log('Could not get initial status, using default');
            }
            updateUI();
        }
        
        // Initialize when page loads
        initializeUI();
    </script>
</body>
</html>