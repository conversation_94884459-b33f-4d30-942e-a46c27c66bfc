#!/usr/bin/env python3

import asyncio
import logging
import os

from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def prewarm(proc: JobContext):
    """Prewarm function to initialize <PERSON> before starting"""
    logger.info("Prewarming <PERSON> (Finance Agent)...")
    proc.wait_for_participant = False
    logger.info("<PERSON> is ready to provide financial guidance!")

async def entrypoint(ctx: JobContext):
    """
    Marcus - The Finance Expert
    Specialized in financial planning, investment advice, and money management
    """
    logger.info("Starting Marcus - Your Personal Finance Expert...")

    # Connect to LiveKit room
    await ctx.connect()
    logger.info("Marcus connected to LiveKit room")

    # Import LiveKit plugins
    from livekit.plugins import deepgram, cartesia, silero, openai

    # <PERSON>'s personality and expertise
    marcus_instructions = """You are <PERSON>, a professional financial advisor and expert.

PERSONALITY:
- Professional, confident, and analytical
- Speak with authority but remain approachable
- Use clear, practical language
- Focus on actionable financial advice

VOICE RULES (CRITICAL):
- Use ONLY plain text in responses
- NO asterisks, markdown, or special characters
- NO bullet points or numbered lists
- Speak naturally and conversationally
- Keep responses concise for voice interaction

EXPERTISE AREAS:
- Personal budgeting and expense tracking
- Investment strategies and portfolio management
- Retirement planning and 401k optimization
- Debt management and credit improvement
- Tax planning and optimization
- Real estate and mortgage advice
- Business finance and cash flow
- Insurance and risk management
- Market analysis and economic trends

CONVERSATION STYLE:
- Ask clarifying questions to understand their financial situation
- Provide specific, actionable recommendations
- Explain complex concepts in simple terms
- Always consider risk tolerance and time horizon
- Emphasize the importance of emergency funds and diversification

SAMPLE RESPONSES:
- "Let me help you create a budget that actually works for your lifestyle"
- "Based on your age and goals, here's what I recommend for your investment strategy"
- "The key to building wealth is consistency and starting early"

Remember: You're here to provide practical financial guidance that people can implement immediately."""

    # Create Marcus agent
    marcus = Agent(instructions=marcus_instructions)

    # Add financial planning functions
    @marcus.function()
    async def create_budget_plan(
        monthly_income: float,
        fixed_expenses: float,
        variable_expenses: float,
        financial_goals: str
    ):
        """Create a personalized budget plan"""
        disposable_income = monthly_income - fixed_expenses - variable_expenses
        savings_rate = (disposable_income / monthly_income) * 100 if monthly_income > 0 else 0
        
        if savings_rate >= 20:
            assessment = "Excellent! You're saving at a healthy rate."
        elif savings_rate >= 10:
            assessment = "Good start, but there's room for improvement."
        else:
            assessment = "We need to work on increasing your savings rate."
            
        return f"Based on your income of ${monthly_income:,.2f}, you have ${disposable_income:,.2f} available monthly. Your current savings rate is {savings_rate:.1f}%. {assessment} Let's work on optimizing your budget to reach your goals: {financial_goals}"

    @marcus.function()
    async def investment_recommendation(
        age: int,
        risk_tolerance: str,
        investment_amount: float,
        time_horizon: str
    ):
        """Provide investment recommendations based on profile"""
        if age < 30:
            stock_allocation = 90
        elif age < 50:
            stock_allocation = 70
        else:
            stock_allocation = 50
            
        bond_allocation = 100 - stock_allocation
        
        if risk_tolerance.lower() == "conservative":
            stock_allocation -= 20
            bond_allocation += 20
        elif risk_tolerance.lower() == "aggressive":
            stock_allocation += 10
            bond_allocation -= 10
            
        return f"For someone your age with {risk_tolerance} risk tolerance, I recommend {stock_allocation}% stocks and {bond_allocation}% bonds. With ${investment_amount:,.2f} to invest over {time_horizon}, consider low-cost index funds and dollar-cost averaging for consistent growth."

    # Create agent session with Marcus's voice
    session = AgentSession(
        vad=silero.VAD.load(),
        stt=deepgram.STT(model="nova-2-general", language="en"),
        llm=openai.LLM(model="gpt-4o-mini", temperature=0.2),  # Low temperature for consistent financial advice
        tts=cartesia.TTS(
            model="sonic-english",
            voice=os.getenv("MARCUS_VOICE_ID", "a0e99841-438c-4a64-b679-ae501e7d6091"),  # Professional male voice
            speed=1.0,
            emotion=["confidence:high", "professionalism:high"]
        ),
    )

    # Start the session
    await session.start(agent=marcus, room=ctx.room)
    logger.info("Marcus is now active and ready to help with financial planning!")

    # Send Marcus's greeting
    await session.generate_reply(
        instructions="Introduce yourself as Marcus, a financial advisor. Ask how you can help with their financial goals today. Be warm but professional. Use only plain text."
    )

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
