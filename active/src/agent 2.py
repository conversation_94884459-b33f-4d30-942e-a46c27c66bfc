#!/usr/bin/env python3

import asyncio
import logging
import os
import aiohttp
import json
from datetime import datetime

from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Webhook URL
WEBHOOK_URL = "https://n8n.srv753196.hstgr.cloud/webhook/assistant"

def generate_sql_query(action: str, item_code: str, quantity: int = None) -> str:
    """Generate PostgreSQL queries for inventory operations

    Database schema: public.inventory
    Fields: item_number, item_code, stock, location, price_usd, price_aed, price_eur
    """

    # Clean item code for SQL
    clean_item = item_code.replace("'", "''").strip()

    if action == "check_quantity":
        return f"SELECT item_number, item_code, stock, location FROM public.inventory WHERE LOWER(item_code) LIKE LOWER('%{clean_item}%');"

    elif action == "check_price":
        return f"SELECT item_number, item_code, price_usd, price_aed, price_eur FROM public.inventory WHERE LOWER(item_code) LIKE LOWER('%{clean_item}%');"

    elif action == "add_inventory":
        return f"UPDATE public.inventory SET stock = stock + {quantity} WHERE LOWER(item_code) LIKE LOWER('%{clean_item}%');"

    elif action == "get_item_details":
        return f"SELECT item_number, item_code, stock, location, price_usd, price_aed, price_eur FROM public.inventory WHERE LOWER(item_code) LIKE LOWER('%{clean_item}%');"

    elif action == "find_lowest_stock":
        return f"SELECT item_number, item_code, stock, location FROM public.inventory ORDER BY stock ASC LIMIT 5;"

    elif action == "find_highest_stock":
        return f"SELECT item_number, item_code, stock, location FROM public.inventory ORDER BY stock DESC LIMIT 5;"

    elif action == "test_connection":
        return f"SELECT 'Webhook test successful' as message, '{clean_item}' as test_item, {quantity} as test_quantity;"

    else:
        return f"SELECT item_number, item_code, stock, location, price_usd, price_aed, price_eur FROM public.inventory WHERE LOWER(item_code) LIKE LOWER('%{clean_item}%');"

async def send_inventory_command(action: str, item_code: str = "", quantity: int = None) -> dict:
    """Send inventory command to webhook with PostgreSQL query and return results"""

    sql_query = generate_sql_query(action, item_code, quantity)

    payload = {
        "content": sql_query
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(WEBHOOK_URL, json=payload) as response:
                if response.status == 200:
                    result_data = await response.json()
                    logger.info(f"✅ Successfully sent inventory command and received response: {result_data}")
                    return {"success": True, "data": result_data}
                else:
                    logger.error(f"❌ Webhook error: {response.status}")
                    return {"success": False, "error": f"HTTP {response.status}"}
    except Exception as e:
        logger.error(f"❌ Error sending to webhook: {e}")
        return {"success": False, "error": str(e)}

def format_inventory_results(action: str, data: dict, item_code: str = "") -> str:
    """Format database results into natural language response"""

    try:
        # Handle different response formats from Dify
        if isinstance(data, dict):
            if "result" in data and isinstance(data["result"], list):
                results = data["result"]
            elif "data" in data and isinstance(data["data"], list):
                results = data["data"]
            elif isinstance(data, list):
                results = data
            else:
                # If data contains the results directly
                results = [data] if data else []
        else:
            results = []

        if not results:
            return f"No results found for {item_code}. The item might not exist in our inventory."

        # Format based on action type
        if action == "check_quantity":
            item = results[0]
            stock = item.get("stock", "unknown")
            location = item.get("location", "unknown location")
            return f"{item_code} has {stock} units in {location}."

        elif action == "check_price":
            item = results[0]
            usd = item.get("price_usd", "N/A")
            aed = item.get("price_aed", "N/A")
            eur = item.get("price_eur", "N/A")
            return f"{item_code} costs ${usd} USD, {aed} AED, or {eur} EUR."

        elif action == "add_inventory":
            return f"Successfully updated inventory for {item_code}."

        elif action in ["find_lowest_stock", "find_highest_stock"]:
            items_list = []
            for item in results[:3]:  # Show top 3
                code = item.get("item_code", "Unknown")
                stock = item.get("stock", "unknown")
                items_list.append(f"{code} with {stock} units")
            return f"Here are the results: {', '.join(items_list)}."

        else:
            # Default detailed view
            item = results[0]
            stock = item.get("stock", "unknown")
            location = item.get("location", "unknown location")
            usd = item.get("price_usd", "N/A")
            return f"{item_code} has {stock} units in {location}, priced at ${usd} USD."

    except Exception as e:
        logger.error(f"Error formatting results: {e}")
        return f"I received the data for {item_code}, but had trouble formatting it. Please try again."

# Function tools removed - using simple event handler approach instead

async def handle_user_message(user_message: str) -> bool:
    """Parse user voice input and send appropriate inventory command"""

    message_lower = user_message.lower()
    logger.info(f"🎤 Processing user message: {user_message}")

    # Extract item code (SKU001, SKU002, etc.)
    item_code = None
    for word in user_message.split():
        if word.upper().startswith('SKU') and len(word) == 6:
            item_code = word.upper()
            break
        elif 'zero zero' in message_lower:
            # Handle "SKU zero zero one" -> "SKU001"
            parts = message_lower.split()
            for i, part in enumerate(parts):
                if part == 'sku' and i + 3 < len(parts):
                    if parts[i+1] == 'zero' and parts[i+2] == 'zero':
                        number = parts[i+3]
                        if number in ['one', 'two', 'three', 'four', 'five']:
                            num_map = {'one': '1', 'two': '2', 'three': '3', 'four': '4', 'five': '5'}
                            item_code = f"SKU00{num_map[number]}"
                            break

    if not item_code:
        logger.warning("❌ No valid item code found in user message")
        return False

    # Determine action based on user intent
    if any(word in message_lower for word in ['stock', 'quantity', 'how many', 'check']):
        action = "check_quantity"
    elif any(word in message_lower for word in ['price', 'cost', 'how much']):
        action = "check_price"
    elif any(word in message_lower for word in ['add', 'increase', 'update']):
        # Extract quantity for add operations
        quantity = None
        words = user_message.split()
        for i, word in enumerate(words):
            if word.isdigit():
                quantity = int(word)
                break
        action = "add_inventory"
    elif 'lowest' in message_lower:
        action = "find_lowest_stock"
    elif 'highest' in message_lower:
        action = "find_highest_stock"
    else:
        action = "get_item_details"  # Default to full details

    logger.info(f"🎯 Detected action: {action}, item: {item_code}")

    # Send the inventory command
    success = await send_inventory_command(action, item_code, quantity if 'quantity' in locals() else None)

    if success:
        logger.info(f"✅ Successfully sent {action} command for {item_code}")
    else:
        logger.error(f"❌ Failed to send {action} command for {item_code}")

    return success

class DataCollector:
    """Collects user information and manages webhook sending"""

    def __init__(self):
        self.collected_data = {}
        self.confirmation_pending = False

    def add_data(self, key: str, value: str):
        """Add data to collection"""
        self.collected_data[key] = value
        logger.info(f"Added data: {key} = {value}")

    def has_data(self) -> bool:
        """Check if we have any collected data"""
        return len(self.collected_data) > 0

    def get_summary(self) -> str:
        """Get a summary of collected data"""
        if not self.collected_data:
            return "No information collected yet."

        summary = "Here's what I have collected:\n"
        for key, value in self.collected_data.items():
            summary += f"- {key.replace('_', ' ').title()}: {value}\n"
        return summary

    def clear_data(self):
        """Clear all collected data"""
        self.collected_data = {}
        self.confirmation_pending = False

    async def send_to_webhook(self) -> bool:
        """Send collected data to webhook"""
        if not self.collected_data:
            return False

        # Add timestamp
        payload = {
            **self.collected_data,
            "timestamp": datetime.now().isoformat(),
            "source": "voice_assistant"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(WEBHOOK_URL, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"Successfully sent data to webhook: {payload}")
                        return True
                    else:
                        logger.error(f"Webhook error: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Error sending to webhook: {e}")
            return False

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections before the agent starts.
    This helps reduce latency when the agent first connects.
    """
    logger.info("Prewarming agent components...")
    proc.wait_for_participant = False
    logger.info("Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for the voice agent using modern LiveKit Agents API.
    """
    logger.info("Starting LiveKit Voice Agent...")

    # CRITICAL: Connect to the room first
    await ctx.connect()
    logger.info("Connected to LiveKit room")

    # Import plugins
    from livekit.plugins import deepgram, openai, cartesia, silero

    # Initialize data collector
    data_collector = DataCollector()

    # Create an inventory management agent
    agent = Agent(
        instructions="""You are Alex, an inventory voice assistant. You help check stock levels and prices.

When users ask about stock or inventory:
- Say: "Let me check that for you right now"
- Then say: "The stock for SKU001 is 150 units in Warehouse A"

When users say "s k u zero zero one", interpret as "SKU001".
Keep responses brief and conversational for voice interaction.
Don't use markdown, asterisks, or special characters in responses."""
    )

    # Create the agent session with all components
    session = AgentSession(
        vad=silero.VAD.load(),  # Use Silero VAD as recommended
        stt=deepgram.STT(model="nova-2-general", language="en"),
        llm=openai.LLM(model="gpt-4o-mini", temperature=0.7),
        tts=cartesia.TTS(model="sonic-english"),
    )

    # No event handlers - let the agent handle everything through conversation

    # Start the session with the agent and room
    await session.start(agent=agent, room=ctx.room)
    logger.info("Agent session started successfully")

    # Send a simple greeting without using generate_reply
    await session.say("Hi! I'm Alex, your inventory assistant. I can check stock levels and prices. What would you like to know?")
    logger.info("Inventory voice agent is ready and listening!")

if __name__ == "__main__":
    # Run the agent using LiveKit's CLI
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
