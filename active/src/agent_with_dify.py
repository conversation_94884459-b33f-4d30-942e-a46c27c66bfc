#!/usr/bin/env python3

import asyncio
import logging
import os

from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, llm
from dify_integration import DifyIntegration

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global Dify integration instance
dify_integration = None

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections before the agent starts.
    This helps reduce latency when the agent first connects.
    """
    global dify_integration
    logger.info("Prewarming agent components...")
    proc.wait_for_participant = False
    
    # Initialize Dify integration
    dify_integration = DifyIntegration()
    logger.info("Dify integration initialized")
    
    logger.info("Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for the voice agent with Dify integration.
    This function sets up and runs a voice pipeline agent that connects to Dify.
    """
    global dify_integration
    logger.info("Starting LiveKit Voice Agent with Dify integration...")
    
    # Wait for the first participant to connect
    await ctx.wait_for_participant()
    logger.info("Participant connected!")
    
    # Import plugins here to avoid startup issues
    from livekit.plugins import deepgram, openai, cartesia, turn_detector
    from livekit.agents.voice import Agent as VoiceAgent
    
    # Define the initial system prompt for the agent
    initial_ctx = llm.ChatContext().append(
        role="system",
        text=(
            "You are Alex, an AI voice assistant that helps with project management. "
            "You can create projects, manage workflows, and help with task organization. "
            "Keep responses brief and conversational for voice interaction. "
            "When users ask to create projects or manage tasks, you'll process their requests through Dify and N8n. "
            "Be friendly and helpful."
        ),
    )
    
    # Create a custom LLM wrapper that uses Dify
    class DifyLLMWrapper:
        def __init__(self, fallback_llm):
            self.fallback_llm = fallback_llm
            self.dify = dify_integration
        
        async def agenerate(self, messages):
            try:
                # Extract the last user message
                if messages and len(messages) > 0:
                    user_message = messages[-1].content
                    logger.info(f"Processing user message: {user_message}")
                    
                    # Check if this is a project management request
                    pm_keywords = ["project", "create", "workflow", "task", "manage", "organize"]
                    is_pm_request = any(keyword in user_message.lower() for keyword in pm_keywords)
                    
                    if is_pm_request and self.dify:
                        # Use Dify for project management requests
                        logger.info("Routing to Dify for project management")
                        dify_response = await self.dify.process_message(user_message)
                        
                        # Create response object
                        class DifyResponse:
                            def __init__(self, content):
                                self.content = content
                        
                        return DifyResponse(dify_response)
                    else:
                        # Use regular OpenAI for general conversation
                        logger.info("Using OpenAI for general conversation")
                        return await self.fallback_llm.agenerate(messages)
                else:
                    # Fallback to OpenAI
                    return await self.fallback_llm.agenerate(messages)
                    
            except Exception as e:
                logger.error(f"Error in DifyLLMWrapper: {e}")
                # Fallback to OpenAI on error
                return await self.fallback_llm.agenerate(messages)
    
    # Configure components
    openai_llm = openai.LLM(model="gpt-4o-mini", temperature=0.7, max_tokens=150)
    llm_instance = DifyLLMWrapper(openai_llm)
    tts_instance = cartesia.TTS(model="sonic-english")
    stt_instance = deepgram.STT(model="nova-2-general", language="en")
    turn_detection = turn_detector.EOUPlugin()
    
    # Create and start the voice agent
    agent = VoiceAgent(
        vad=turn_detection,
        stt=stt_instance,
        llm=llm_instance,
        tts=tts_instance,
        chat_ctx=initial_ctx,
    )
    
    agent.start(ctx.room)
    await agent.say("Hello! I'm Alex, your AI assistant for project management. I can help you create projects, manage workflows, and organize tasks. How can I help you today?")
    
    logger.info("Voice agent with Dify integration is ready!")

if __name__ == "__main__":
    # Run the agent using LiveKit's CLI
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
