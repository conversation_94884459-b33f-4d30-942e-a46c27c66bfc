#!/usr/bin/env python3
"""
Simple Juno Test Agent - Working LiveKit Agent for Playground Testing

This creates a standard LiveKit agent that will work with the playground
and demonstrate that our environment and setup is working correctly.
"""

import asyncio
import logging
import os

from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections before the agent starts.
    This helps reduce latency when the agent first connects.
    """
    logger.info("Prewarming Juno test agent components...")
    proc.wait_for_participant = False
    logger.info("Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for the Juno test agent using modern LiveKit Agents API.
    """
    logger.info("🚀 Starting Juno Test Agent...")

    # CRITICAL: Connect to the room first
    await ctx.connect()
    logger.info("✅ Connected to LiveKit room")

    # Import plugins
    from livekit.plugins import deepgram, openai, cartesia, silero

    # Create the Juno test agent with instructions
    agent = Agent(
        instructions="""You are <PERSON>, a meeting assistant AI in test mode. 

You are designed to be a passive meeting assistant that:
- Listens continuously to meetings
- Records and transcribes conversations
- Can answer questions when called by name
- Takes notes and extracts action items
- Automates follow-up tasks

For this test, you are in active mode to verify the connection works.
Keep responses brief and conversational for voice interaction.
Be friendly and helpful. Don't use markdown or special characters in responses."""
    )

    # Create the agent session with all components
    session = AgentSession(
        vad=silero.VAD.load(),  # Use Silero VAD as recommended
        stt=deepgram.STT(model="nova-2-general", language="en"),
        llm=openai.LLM(model="gpt-4o-mini", temperature=0.3),  # Low temperature for consistency
        tts=cartesia.TTS(model="sonic-english"),
    )

    # Start the session with the agent and room
    await session.start(agent=agent, room=ctx.room)
    logger.info("✅ Agent session started successfully")

    # Send initial greeting
    await session.say("Hello! I'm Juno, your meeting assistant. I'm currently in test mode to verify our connection is working. You can speak to me and I'll respond to confirm everything is set up correctly.")
    logger.info("🎉 Juno test agent is ready and listening!")

if __name__ == "__main__":
    # Run the agent using LiveKit's CLI
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
