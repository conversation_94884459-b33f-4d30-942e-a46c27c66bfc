#!/usr/bin/env python3
"""
Test suite for Juno Audio Core functionality.

Tests the core audio processing components including:
- Audio buffer operations
- Continuous streaming capability
- Mode switching (passive/active)
- Connection stability
- Quality monitoring
"""

import asyncio
import pytest
import time
import numpy as np
from unittest.mock import Mock, AsyncMock, patch

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from core.audio_buffer import CircularAudioBuffer, AudioChunk, AudioQualityMonitor
from core.juno_audio_core import JunoAudioCore, JunoMode, AudioConfig


class TestAudioBuffer:
    """Test cases for the CircularAudioBuffer class."""

    def test_audio_chunk_creation(self):
        """Test AudioChunk creation and properties."""
        test_data = b'\x00\x01' * 1000  # 2000 bytes of test audio
        timestamp = time.time()

        chunk = AudioChunk(test_data, timestamp)

        assert chunk.data == test_data
        assert chunk.timestamp == timestamp
        assert chunk.sample_rate == 16000
        assert chunk.channels == 1
        assert len(chunk) == len(test_data)

        # Test numpy conversion
        audio_array = chunk.to_numpy()
        assert isinstance(audio_array, np.ndarray)
        assert len(audio_array) == len(test_data) // 2  # 16-bit audio

    def test_circular_buffer_initialization(self):
        """Test CircularAudioBuffer initialization."""
        buffer = CircularAudioBuffer(max_duration_seconds=10.0, chunk_size_ms=100)

        assert buffer.max_duration == 10.0
        assert buffer.sample_rate == 16000
        assert buffer.channels == 1
        assert buffer.chunk_size_ms == 100
        assert buffer.max_chunks == 100  # 10 seconds / 0.1 seconds per chunk
        assert len(buffer) == 0
        assert not buffer  # Empty buffer should be falsy

    def test_buffer_add_chunk(self):
        """Test adding chunks to the buffer."""
        buffer = CircularAudioBuffer(max_duration_seconds=1.0, chunk_size_ms=100)
        test_data = b'\x00\x01' * 800  # 1600 bytes

        # Add a chunk
        chunk = buffer.add_chunk(test_data)

        assert len(buffer) == 1
        assert buffer  # Non-empty buffer should be truthy
        assert chunk.data == test_data
        assert buffer.total_chunks_processed == 1

    def test_buffer_circular_behavior(self):
        """Test that buffer properly overwrites old data when full."""
        buffer = CircularAudioBuffer(max_duration_seconds=0.3, chunk_size_ms=100)  # Max 3 chunks
        test_data = b'\x00\x01' * 800

        # Add 5 chunks (more than max)
        chunks = []
        for i in range(5):
            chunk_data = test_data + bytes([i])  # Make each chunk unique
            chunk = buffer.add_chunk(chunk_data)
            chunks.append(chunk)

        # Buffer should only contain last 3 chunks (due to maxlen=3)
        assert len(buffer) <= buffer.max_chunks  # Should not exceed max
        assert buffer.total_chunks_processed == 5

        # Verify the buffer contains the most recent chunks
        buffer_chunks = buffer.get_all_audio()
        assert len(buffer_chunks) <= buffer.max_chunks

        # The last chunk should always be the most recent one added
        if buffer_chunks:
            assert buffer_chunks[-1].data == chunks[-1].data  # Last chunk added

    def test_get_recent_audio(self):
        """Test retrieving recent audio by duration."""
        buffer = CircularAudioBuffer(max_duration_seconds=2.0, chunk_size_ms=100)
        test_data = b'\x00\x01' * 800

        # Add chunks with specific timestamps
        base_time = time.time()
        timestamps = [base_time - 1.5, base_time - 1.0, base_time - 0.5, base_time]

        for i, timestamp in enumerate(timestamps):
            chunk_data = test_data + bytes([i])
            buffer.add_chunk(chunk_data, timestamp)

        # Get recent audio for last 1 second
        recent_chunks = buffer.get_recent_audio(1.0)

        # Should get last 2 chunks (within 1 second)
        assert len(recent_chunks) == 2
        assert recent_chunks[0].data == test_data + bytes([2])
        assert recent_chunks[1].data == test_data + bytes([3])

    def test_callback_registration(self):
        """Test callback registration."""
        buffer = CircularAudioBuffer()

        # Simple callback functions
        def wake_word_callback(chunk):
            pass

        def transcription_callback(chunk):
            pass

        def analysis_callback(chunk):
            pass

        # Register callbacks
        buffer.register_wake_word_callback(wake_word_callback)
        buffer.register_transcription_callback(transcription_callback)
        buffer.register_analysis_callback(analysis_callback)

        assert len(buffer.wake_word_callbacks) == 1
        assert len(buffer.transcription_callbacks) == 1
        assert len(buffer.analysis_callbacks) == 1

    def test_buffer_stats(self):
        """Test buffer statistics."""
        buffer = CircularAudioBuffer(max_duration_seconds=1.0, chunk_size_ms=100)
        test_data = b'\x00\x01' * 800

        # Initial stats
        stats = buffer.get_buffer_stats()
        assert stats['total_chunks'] == 0
        assert stats['total_processed'] == 0
        assert stats['buffer_utilization'] == 0.0

        # Add some chunks
        for i in range(5):
            buffer.add_chunk(test_data)

        # Updated stats
        stats = buffer.get_buffer_stats()
        assert stats['total_chunks'] == 5
        assert stats['total_processed'] == 5
        assert stats['buffer_utilization'] == 0.5  # 5/10 chunks


class TestAudioQualityMonitor:
    """Test cases for the AudioQualityMonitor class."""

    def test_quality_monitor_initialization(self):
        """Test AudioQualityMonitor initialization."""
        monitor = AudioQualityMonitor(window_size=50)

        assert monitor.window_size == 50
        assert len(monitor.volume_history) == 0
        assert monitor.silence_threshold == 0.01

    def test_analyze_chunk_silent(self):
        """Test analysis of silent audio chunk."""
        monitor = AudioQualityMonitor()

        # Create silent audio (all zeros)
        silent_data = b'\x00' * 1600  # 800 samples of 16-bit audio
        chunk = AudioChunk(silent_data, time.time())

        metrics = monitor.analyze_chunk(chunk)


        assert metrics['rms_volume'] == 0.0
        assert metrics['is_silent'] is True
        assert metrics['peak_amplitude'] == 0.0
        assert metrics['zero_crossing_rate'] == 0.0

    def test_analyze_chunk_with_audio(self):
        """Test analysis of audio chunk with content."""
        monitor = AudioQualityMonitor()

        # Create audio with some content (sine wave)
        sample_rate = 16000
        duration = 0.1  # 100ms
        frequency = 440  # A4 note

        t = np.linspace(0, duration, int(sample_rate * duration))
        sine_wave = np.sin(2 * np.pi * frequency * t)

        # Convert to 16-bit integers
        audio_data = (sine_wave * 16384).astype(np.int16).tobytes()
        chunk = AudioChunk(audio_data, time.time())

        metrics = monitor.analyze_chunk(chunk)

        assert metrics['rms_volume'] > 0.0
        assert metrics['is_silent'] is False
        assert metrics['peak_amplitude'] > 0.0
        assert metrics['zero_crossing_rate'] > 0.0


class TestJunoAudioCore:
    """Test cases for the JunoAudioCore class."""

    def test_audio_core_initialization(self):
        """Test JunoAudioCore initialization."""
        config = AudioConfig(
            sample_rate=16000,
            channels=1,
            buffer_duration_seconds=30.0
        )

        core = JunoAudioCore(config)

        assert core.config == config
        assert core.mode == JunoMode.PASSIVE
        assert core.is_connected is False
        assert core.audio_buffer is not None
        assert core.quality_monitor is not None

    def test_mode_switching(self):
        """Test mode switching functionality."""
        core = JunoAudioCore()

        # Test initial mode
        assert core.mode == JunoMode.PASSIVE

        # Mock the room and track for testing
        core.room = Mock()
        core.audio_track = Mock()
        core.room.local_participant = Mock()
        core.room.local_participant.track_publications = {}
        core.room.local_participant.publish_track = AsyncMock()
        core.room.local_participant.unpublish_track = AsyncMock()

        # Test activation
        async def test_activation():
            await core.activate_conversation_mode()
            assert core.mode == JunoMode.ACTIVE
            assert core.stats['mode_changes'] == 1

        asyncio.run(test_activation())

        # Test deactivation
        async def test_deactivation():
            await core.deactivate_conversation_mode()
            assert core.mode == JunoMode.PASSIVE
            assert core.stats['mode_changes'] == 2

        asyncio.run(test_deactivation())

    def test_callback_registration(self):
        """Test mode change callback registration."""
        core = JunoAudioCore()

        passive_callback = Mock()
        active_callback = Mock()

        core.register_mode_change_callback(JunoMode.PASSIVE, passive_callback)
        core.register_mode_change_callback(JunoMode.ACTIVE, active_callback)

        assert len(core.mode_change_callbacks[JunoMode.PASSIVE]) == 1
        assert len(core.mode_change_callbacks[JunoMode.ACTIVE]) == 1

    def test_stats_collection(self):
        """Test statistics collection."""
        core = JunoAudioCore()

        stats = core.get_stats()

        assert 'mode' in stats
        assert 'is_connected' in stats
        assert 'uptime_seconds' in stats
        assert 'total_audio_processed' in stats
        assert 'buffer_stats' in stats

        assert stats['mode'] == JunoMode.PASSIVE.value
        assert stats['is_connected'] is False
        assert stats['total_audio_processed'] == 0


@pytest.mark.asyncio
async def test_integration_audio_processing():
    """Integration test for audio processing pipeline."""
    config = AudioConfig(
        buffer_duration_seconds=2.0,
        chunk_size_ms=100
    )

    core = JunoAudioCore(config)

    # Mock audio data
    test_audio = b'\x00\x01' * 800  # 1600 bytes

    # Add audio to buffer
    chunk = core.audio_buffer.add_chunk(test_audio)

    # Verify processing
    assert len(core.audio_buffer) == 1
    assert core.audio_buffer.total_chunks_processed == 1

    # Test quality monitoring
    if core.quality_monitor:
        metrics = core.quality_monitor.analyze_chunk(chunk)
        assert 'rms_volume' in metrics
        assert 'is_silent' in metrics


if __name__ == "__main__":
    # Run basic tests
    print("Running Juno Audio Core tests...")

    # Test audio buffer
    print("Testing AudioBuffer...")
    test_buffer = TestAudioBuffer()
    test_buffer.test_audio_chunk_creation()
    test_buffer.test_circular_buffer_initialization()
    test_buffer.test_buffer_add_chunk()
    test_buffer.test_buffer_circular_behavior()
    test_buffer.test_get_recent_audio()
    test_buffer.test_callback_registration()
    test_buffer.test_buffer_stats()
    print("✓ AudioBuffer tests passed")

    # Test quality monitor
    print("Testing AudioQualityMonitor...")
    test_monitor = TestAudioQualityMonitor()
    test_monitor.test_quality_monitor_initialization()
    test_monitor.test_analyze_chunk_silent()
    test_monitor.test_analyze_chunk_with_audio()
    print("✓ AudioQualityMonitor tests passed")

    # Test audio core
    print("Testing JunoAudioCore...")
    test_core = TestJunoAudioCore()
    test_core.test_audio_core_initialization()
    test_core.test_callback_registration()
    test_core.test_stats_collection()
    print("✓ JunoAudioCore tests passed")

    # Run integration test
    print("Running integration test...")
    asyncio.run(test_integration_audio_processing())
    print("✓ Integration test passed")

    print("\n🎉 All tests passed! Step 1 implementation is working correctly.")
