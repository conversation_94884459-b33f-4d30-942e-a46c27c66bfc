#!/usr/bin/env python3
"""
Test Suite for Arabic-English Language Detection

This module contains comprehensive tests for the Whisper language detection
implementation in Juno Step 2A.
"""

import asyncio
import unittest
import logging
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

# Import our modules
from whisper_language_stt import WhisperLanguageSTT, create_whisper_language_stt
from language_detection_config import (
    get_language_config,
    format_transcript_with_markers,
    detect_code_switching_context,
    create_test_scenarios
)

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestLanguageDetectionConfig(unittest.TestCase):
    """Test language detection configuration."""
    
    def test_default_config(self):
        """Test default configuration loading."""
        config = get_language_config("default")
        
        self.assertEqual(config.supported_languages, ["en", "ar"])
        self.assertTrue(config.enable_language_detection)
        self.assertTrue(config.add_language_markers)
        self.assertEqual(config.whisper_model, "whisper-1")
    
    def test_english_only_config(self):
        """Test English-only configuration."""
        config = get_language_config("english_only")
        
        self.assertEqual(config.supported_languages, ["en"])
        self.assertFalse(config.enable_language_detection)
        self.assertFalse(config.add_language_markers)
    
    def test_arabic_only_config(self):
        """Test Arabic-only configuration."""
        config = get_language_config("arabic_only")
        
        self.assertEqual(config.supported_languages, ["ar"])
        self.assertFalse(config.enable_language_detection)
        self.assertFalse(config.add_language_markers)
    
    def test_format_transcript_with_markers(self):
        """Test transcript formatting with language markers."""
        config = get_language_config("default")
        
        # Test English text
        result = format_transcript_with_markers("Hello world", "en", config)
        self.assertEqual(result, "[EN]Hello world[/EN]")
        
        # Test Arabic text
        result = format_transcript_with_markers("مرحبا", "ar", config)
        self.assertEqual(result, "[AR]مرحبا[/AR]")
        
        # Test empty text
        result = format_transcript_with_markers("", "en", config)
        self.assertEqual(result, "")
    
    def test_code_switching_detection(self):
        """Test code-switching context detection."""
        # Test mixed content
        scores = detect_code_switching_context("We need the budget قبل نهاية الأسبوع")
        self.assertGreater(scores["en"], 0)
        self.assertGreater(scores["ar"], 0)
        
        # Test English-only content
        scores = detect_code_switching_context("Hello how are you today")
        self.assertGreaterEqual(scores["en"], scores["ar"])

class TestWhisperLanguageSTT(unittest.TestCase):
    """Test WhisperLanguageSTT implementation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_openai_client = Mock()
        self.mock_openai_client.audio = Mock()
        self.mock_openai_client.audio.transcriptions = Mock()
        
    def test_initialization(self):
        """Test STT initialization."""
        stt = create_whisper_language_stt()
        
        self.assertTrue(stt.detect_language)
        self.assertEqual(stt.supported_languages, ["en", "ar"])
        self.assertIsInstance(stt, WhisperLanguageSTT)
    
    def test_language_marker_formatting(self):
        """Test language marker addition."""
        stt = create_whisper_language_stt()
        
        # Test English text
        result = stt._add_language_markers_to_text("Hello world", "en")
        self.assertEqual(result, "[EN]Hello world[/EN]")
        
        # Test Arabic text
        result = stt._add_language_markers_to_text("مرحبا", "ar")
        self.assertEqual(result, "[AR]مرحبا[/AR]")
    
    def test_settings_update(self):
        """Test updating language settings."""
        stt = create_whisper_language_stt()
        
        # Update settings
        stt.update_language_settings(
            supported_languages=["en", "ar", "fr"],
            add_language_markers=False,
            detect_language=False
        )
        
        self.assertEqual(stt.supported_languages, ["en", "ar", "fr"])
        self.assertFalse(stt._add_language_markers)
        self.assertFalse(stt.detect_language)

class TestLanguageDetectionIntegration(unittest.TestCase):
    """Integration tests for language detection."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        self.test_scenarios = create_test_scenarios()
    
    def test_scenario_definitions(self):
        """Test that test scenarios are properly defined."""
        self.assertGreater(len(self.test_scenarios), 0)
        
        for scenario in self.test_scenarios:
            self.assertIn("name", scenario)
            self.assertIn("input", scenario)
            self.assertIn("expected_language", scenario)
            self.assertIn("expected_output", scenario)
    
    def test_english_scenario_formatting(self):
        """Test English scenario formatting."""
        config = get_language_config("default")
        
        english_scenario = next(
            s for s in self.test_scenarios 
            if s["name"] == "Pure English"
        )
        
        result = format_transcript_with_markers(
            english_scenario["input"],
            "en",
            config
        )
        
        self.assertTrue(result.startswith("[EN]"))
        self.assertTrue(result.endswith("[/EN]"))
    
    def test_arabic_scenario_formatting(self):
        """Test Arabic scenario formatting."""
        config = get_language_config("default")
        
        arabic_scenario = next(
            s for s in self.test_scenarios 
            if s["name"] == "Pure Arabic"
        )
        
        result = format_transcript_with_markers(
            arabic_scenario["input"],
            "ar",
            config
        )
        
        self.assertTrue(result.startswith("[AR]"))
        self.assertTrue(result.endswith("[/AR]"))

class TestErrorHandling(unittest.TestCase):
    """Test error handling in language detection."""
    
    def test_invalid_language_code(self):
        """Test handling of invalid language codes."""
        config = get_language_config("default")
        
        # Test with unsupported language
        result = format_transcript_with_markers("Bonjour", "fr", config)
        # Should return original text since 'fr' is not in language_markers
        self.assertEqual(result, "Bonjour")
    
    def test_empty_input_handling(self):
        """Test handling of empty inputs."""
        config = get_language_config("default")
        
        # Test empty string
        result = format_transcript_with_markers("", "en", config)
        self.assertEqual(result, "")
        
        # Test whitespace-only string
        result = format_transcript_with_markers("   ", "en", config)
        self.assertEqual(result, "   ")
    
    def test_none_input_handling(self):
        """Test handling of None inputs."""
        stt = create_whisper_language_stt()
        
        # Test with None text
        result = stt._add_language_markers_to_text(None, "en")
        # Should handle gracefully (implementation dependent)
        self.assertIsNotNone(result)

def run_performance_tests():
    """Run performance tests for language detection."""
    logger.info("Running performance tests...")
    
    # Test with various input sizes
    test_texts = [
        "Short text",
        "Medium length text that contains several words and should test processing speed",
        "Very long text " * 100 + "that tests performance with large inputs and multiple segments"
    ]
    
    stt = create_whisper_language_stt()
    
    for i, text in enumerate(test_texts):
        import time
        start_time = time.time()
        
        result = stt._add_language_markers_to_text(text, "en")
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        logger.info(f"Test {i+1}: {len(text)} chars processed in {processing_time:.4f}s")
        logger.info(f"Result length: {len(result)} chars")

def run_manual_test_scenarios():
    """Run manual test scenarios for validation."""
    logger.info("Running manual test scenarios...")
    
    scenarios = create_test_scenarios()
    config = get_language_config("default")
    
    for scenario in scenarios:
        logger.info(f"\n--- Testing: {scenario['name']} ---")
        logger.info(f"Input: {scenario['input']}")
        logger.info(f"Expected Language: {scenario['expected_language']}")
        
        # For pure language scenarios, test formatting
        if scenario['expected_language'] in ['en', 'ar']:
            result = format_transcript_with_markers(
                scenario['input'],
                scenario['expected_language'],
                config
            )
            logger.info(f"Formatted Output: {result}")
            logger.info(f"Expected Output: {scenario['expected_output']}")
            logger.info(f"Match: {result == scenario['expected_output']}")

if __name__ == "__main__":
    # Run unit tests
    logger.info("Starting language detection tests...")
    
    # Run unittest suite
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run additional tests
    run_performance_tests()
    run_manual_test_scenarios()
    
    logger.info("All tests completed!")
