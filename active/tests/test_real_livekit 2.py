#!/usr/bin/env python3
"""
Real LiveKit Test for Juno Audio Core

This script tests the Juno Audio Core with a real LiveKit connection.
Requires LiveKit API keys to be set in environment variables.

Usage:
    export LIVEKIT_URL=wss://your-project.livekit.cloud
    export LIVEKIT_API_KEY=your-api-key
    export LIVEKIT_API_SECRET=your-api-secret
    python test_real_livekit.py
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

from livekit.agents import JobContext, WorkerOptions, cli
from core.juno_audio_core import JunoAudioCore, AudioConfig

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_real_livekit(ctx: JobContext):
    """Test Juno Audio Core with real LiveKit connection."""
    logger.info("🚀 Starting Real LiveKit Test for Juno Audio Core")
    
    try:
        # Create audio configuration
        config = AudioConfig(
            sample_rate=16000,
            channels=1,
            buffer_duration_seconds=30.0,
            quality_monitoring=True,
            auto_reconnect=True
        )
        
        # Initialize Juno Audio Core
        core = JunoAudioCore(config)
        
        # Initialize with real LiveKit context
        await core.initialize(ctx)
        
        logger.info("✅ Juno Audio Core initialized with real LiveKit")
        logger.info(f"Room: {ctx.room.name}")
        logger.info(f"Mode: {core.mode.value}")
        
        # Test mode switching
        logger.info("Testing mode switching...")
        await core.activate_conversation_mode()
        logger.info(f"Activated: {core.mode.value}")
        
        await asyncio.sleep(2)
        
        await core.deactivate_conversation_mode()
        logger.info(f"Deactivated: {core.mode.value}")
        
        # Start continuous listening for a short period
        logger.info("Starting continuous listening for 30 seconds...")
        logger.info("Join the room to test audio processing!")
        
        # Create a task for continuous listening
        listening_task = asyncio.create_task(core.start_continuous_listening())
        
        # Wait for 30 seconds or until interrupted
        try:
            await asyncio.wait_for(listening_task, timeout=30.0)
        except asyncio.TimeoutError:
            logger.info("30-second test period completed")
            listening_task.cancel()
        
        # Get final statistics
        stats = core.get_stats()
        logger.info("Final Statistics:")
        logger.info(f"  Uptime: {stats['uptime_seconds']:.1f}s")
        logger.info(f"  Audio Processed: {stats['total_audio_processed']} bytes")
        logger.info(f"  Mode Changes: {stats['mode_changes']}")
        logger.info(f"  Buffer Utilization: {stats['buffer_stats']['buffer_utilization']:.1%}")
        
        # Shutdown gracefully
        await core.shutdown()
        logger.info("✅ Real LiveKit test completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Real LiveKit test failed: {e}")
        raise


def check_environment():
    """Check if required environment variables are set."""
    required_vars = ['LIVEKIT_URL', 'LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error("❌ Missing required environment variables:")
        for var in missing_vars:
            logger.error(f"  - {var}")
        logger.error("\nPlease set these variables and try again:")
        logger.error("  export LIVEKIT_URL=wss://your-project.livekit.cloud")
        logger.error("  export LIVEKIT_API_KEY=your-api-key")
        logger.error("  export LIVEKIT_API_SECRET=your-api-secret")
        return False
    
    return True


if __name__ == "__main__":
    if not check_environment():
        exit(1)
    
    logger.info("🔧 Environment variables found")
    logger.info("🚀 Starting real LiveKit test...")
    logger.info("💡 This will create a LiveKit room that you can join to test audio")
    
    # Run the agent using LiveKit's CLI
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=test_real_livekit,
        ),
    )
