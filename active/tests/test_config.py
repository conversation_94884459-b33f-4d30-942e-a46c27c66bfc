#!/usr/bin/env python3

"""
Configuration Test Script for LiveKit Voice Agent
This script verifies that all API keys and configurations are properly set.
"""

import os
import sys
from dotenv import load_dotenv

def test_configuration():
    """Test all configuration settings and API keys."""

    print("🔍 Testing LiveKit Voice Agent Configuration")
    print("=" * 50)

    # Load environment variables
    load_dotenv()

    # Test results
    tests_passed = 0
    tests_total = 0

    # Test 1: LiveKit Configuration
    tests_total += 1
    print("\n1. Testing LiveKit Configuration...")

    livekit_url = os.getenv("LIVEKIT_URL")
    livekit_api_key = os.getenv("LIVEKIT_API_KEY")
    livekit_api_secret = os.getenv("LIVEKIT_API_SECRET")

    if livekit_url and livekit_api_key and livekit_api_secret:
        print("   ✅ LiveKit configuration found")
        print(f"   📡 URL: {livekit_url}")
        print(f"   🔑 API Key: {livekit_api_key[:10]}...")
        tests_passed += 1
    else:
        print("   ❌ LiveKit configuration missing")
        print("   💡 Check LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET")

    # Test 2: OpenAI API Key
    tests_total += 1
    print("\n2. Testing OpenAI Configuration...")

    openai_key = os.getenv("OPENAI_API_KEY")
    if openai_key and openai_key != "your_openai_api_key_here":
        print("   ✅ OpenAI API key found")
        print(f"   🔑 Key: {openai_key[:10]}...")
        tests_passed += 1
    else:
        print("   ❌ OpenAI API key missing or not configured")
        print("   💡 Set OPENAI_API_KEY in .env file")

    # Test 3: Cartesia API Key
    tests_total += 1
    print("\n3. Testing Cartesia Configuration...")

    cartesia_key = os.getenv("CARTESIA_API_KEY")
    if cartesia_key and cartesia_key != "your_cartesia_api_key_here":
        print("   ✅ Cartesia API key found")
        print(f"   🔑 Key: {cartesia_key[:10]}...")
        tests_passed += 1
    else:
        print("   ❌ Cartesia API key missing or not configured")
        print("   💡 Set CARTESIA_API_KEY in .env file")

    # Test 4: DeepGram API Key
    tests_total += 1
    print("\n4. Testing DeepGram Configuration...")

    deepgram_key = os.getenv("DEEPGRAM_API_KEY")
    if deepgram_key and deepgram_key != "your_deepgram_api_key_here":
        print("   ✅ DeepGram API key found")
        print(f"   🔑 Key: {deepgram_key[:10]}...")
        tests_passed += 1
    else:
        print("   ❌ DeepGram API key missing or not configured")
        print("   💡 Set DEEPGRAM_API_KEY in .env file")

    # Test 5: Python Dependencies
    tests_total += 1
    print("\n5. Testing Python Dependencies...")

    try:
        import livekit
        import livekit.agents
        from livekit.plugins import turn_detector
        # Test if EOUPlugin is available
        eou_plugin = turn_detector.EOUPlugin
        print("   ✅ LiveKit packages installed")
        print("   ✅ EOU turn detector available")
        tests_passed += 1
    except ImportError as e:
        print(f"   ❌ LiveKit packages missing: {e}")
        print("   💡 Run: pip install -r requirements.txt")

    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{tests_total} passed")

    if tests_passed == tests_total:
        print("🎉 All tests passed! Your agent is ready to run.")
        print("\n🚀 Next steps:")
        print("   1. Run: python3 agent.py dev")
        print("   2. Open the provided URL in your browser")
        print("   3. Allow microphone access and start talking!")
        return True
    else:
        print("⚠️  Some tests failed. Please fix the issues above.")
        print("\n🔧 Common fixes:")
        print("   - Edit .env file with your actual API keys")
        print("   - Run: pip install -r requirements.txt")
        print("   - Ensure all API keys are valid and active")
        return False

def test_api_connectivity():
    """Test actual API connectivity (optional)."""
    print("\n🌐 Testing API Connectivity (Optional)")
    print("-" * 30)

    # Test OpenAI
    try:
        import openai
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key and openai_key != "your_openai_api_key_here":
            # Note: We don't actually call the API to avoid charges
            print("   ✅ OpenAI client can be initialized")
        else:
            print("   ⏭️  OpenAI key not set, skipping connectivity test")
    except Exception as e:
        print(f"   ⚠️  OpenAI connectivity issue: {e}")

    print("   💡 Full API connectivity will be tested when you run the agent")

if __name__ == "__main__":
    success = test_configuration()

    # Optional connectivity test
    if success:
        test_api_connectivity()

    sys.exit(0 if success else 1)
