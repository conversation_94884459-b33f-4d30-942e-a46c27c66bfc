#!/usr/bin/env python3
"""
Juno Step 2A: Proper ElevenLabs Integration with LiveKit

This agent implements the correct way to integrate ElevenLabs TTS with LiveKit
based on the official documentation and troubleshooting guide.

Features:
- OpenAI Whisper STT with Arabic-English language detection
- Proper ElevenLabs TTS integration using LiveKit plugin
- Multilingual model (eleven_multilingual_v2) for Arabic support
- Language-aware voice synthesis
- SSML support for pronunciation control
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession
from livekit.plugins import openai, silero, elevenlabs

# Import our custom Whisper STT with language detection
from whisper_language_stt import create_whisper_language_stt

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections.
    """
    logger.info("Prewarming Juno Step 2A with proper ElevenLabs integration...")
    logger.info("- OpenAI Whisper STT with Arabic-English detection")
    logger.info("- OpenAI GPT-4o-mini LLM")
    logger.info("- ElevenLabs Multilingual TTS (proper LiveKit plugin)")
    logger.info("- Silero VAD")
    proc.wait_for_participant = False
    logger.info("Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for Juno Step 2A with proper ElevenLabs integration.
    """
    logger.info("🚀 Starting Juno Step 2A with Proper ElevenLabs Integration...")

    # Connect to the room first
    await ctx.connect()
    logger.info("✅ Connected to LiveKit room")

    # Create the Juno agent with enhanced multilingual instructions
    agent = Agent(
        instructions="""You are Juno, a multilingual meeting assistant AI with Arabic-English language detection capabilities.

أنت جونو، مساعد اجتماعات ذكي متعدد اللغات مع قدرات كشف اللغة العربية والإنجليزية.

LANGUAGE HANDLING:
- You receive transcripts with language markers like [EN]text[/EN] and [AR]text[/AR]
- Process both English and Arabic content naturally
- ALWAYS respond in the same language as the user's question
- If user speaks Arabic, respond in Arabic
- If user speaks English, respond in English
- If user mixes languages, respond in the dominant language or ask which they prefer
- Handle code-switching (mixing languages) gracefully

MULTILINGUAL RESPONSES:
- For Arabic input: Respond completely in Arabic with natural flow
- For English input: Respond completely in English
- Be conversational and helpful in both languages
- Use appropriate cultural greetings and expressions

VOICE RULES:
- Keep responses brief and conversational
- Be friendly and helpful in both languages
- Don't use markdown or special characters in responses
- Don't read out language markers like [EN] or [AR]
- Use natural pronunciation for each language

TESTING MODE:
You are currently in active testing mode to verify Arabic-English detection and proper ElevenLabs TTS works.
Respond enthusiastically to test both languages and encourage users to try different scenarios."""
    )

    # Create the agent session with proper ElevenLabs integration
    try:
        # Initialize our custom Whisper STT with language detection
        whisper_stt = create_whisper_language_stt(
            model="whisper-1",
            detect_language=True,
            supported_languages=["en", "ar"],
            add_language_markers=True
        )
        logger.info("✅ Initialized Whisper STT with Arabic-English detection")

        # Initialize ElevenLabs TTS with proper LiveKit plugin and specific voice ID
        # Using eleven_turbo_v2_5 for good balance of quality and latency
        elevenlabs_tts = elevenlabs.TTS(
            model="eleven_turbo_v2_5",  # Good balance for Arabic-English multilingual
            voice_id="gOkFV1JMCt0G0n9xmBwV",  # Specific ElevenLabs voice ID provided
            language="en",  # Default language, will be updated dynamically
            enable_ssml_parsing=True,  # Enable SSML for pronunciation control
            # API key will be read from ELEVEN_API_KEY environment variable
        )
        logger.info("✅ Initialized ElevenLabs TTS with multilingual model")

        # Create agent session with all components
        session = AgentSession(
            vad=silero.VAD.load(),  # Voice Activity Detection
            stt=whisper_stt,  # Our custom Whisper STT with language detection
            llm=openai.LLM(model="gpt-4o-mini", temperature=0.1),  # Very low temperature for consistency
            tts=elevenlabs_tts,  # Proper ElevenLabs TTS with multilingual support
        )
        logger.info("✅ Agent session components initialized with proper ElevenLabs")

    except Exception as e:
        logger.error(f"❌ Error initializing enhanced agent session: {e}")
        # Fallback to basic ElevenLabs configuration (still use ElevenLabs, not Cartesia)
        logger.info("🔄 Falling back to basic ElevenLabs configuration...")
        session = AgentSession(
            vad=silero.VAD.load(),
            stt=openai.STT(),  # Fallback to basic OpenAI STT
            llm=openai.LLM(model="gpt-4o-mini", temperature=0.1),
            tts=elevenlabs.TTS(
                voice_id="gOkFV1JMCt0G0n9xmBwV",  # Use same voice ID in fallback
                model="eleven_turbo_v2_5"  # Use same model in fallback
            ),
        )

    # Start the session
    await session.start(agent=agent, room=ctx.room)
    logger.info("✅ Agent session started successfully")

    # Send initial greeting in English (ElevenLabs with specific voice ID)
    await session.say(
        "Hello! I'm Juno with Arabic-English language detection and ElevenLabs voice synthesis. "
        "I'm now using a specific ElevenLabs voice for consistent, natural pronunciation. "
        "I can understand both Arabic and English speech perfectly. "
        "Please try speaking to me in Arabic, English, or mixing both languages to test the detection. "
        "I will respond in the same language you use with proper ElevenLabs pronunciation."
    )

    logger.info("🎉 Juno Step 2A with proper ElevenLabs is ready!")
    logger.info("📝 Arabic Testing Scenarios:")
    logger.info("   1. Pure Arabic: 'مرحبا جونو، كيف حالك اليوم؟'")
    logger.info("   2. Arabic question: 'ما هو الطقس اليوم؟'")
    logger.info("   3. Pure English: 'Hello Juno, how are you today?'")
    logger.info("   4. Code-switching: 'We need to finish the budget قبل نهاية الأسبوع by Friday'")
    logger.info("   5. Mixed conversation: Start in Arabic, then switch to English")
    logger.info("🎯 Expected: Agent responds in same language with proper ElevenLabs pronunciation")

if __name__ == "__main__":
    # Verify environment variables
    required_env_vars = [
        "OPENAI_API_KEY",
        "ELEVEN_API_KEY",  # ElevenLabs API key (proper name)
        "LIVEKIT_URL",
        "LIVEKIT_API_KEY",
        "LIVEKIT_API_SECRET"
    ]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {missing_vars}")
        logger.error("Please check your .env file and ensure all required API keys are set.")
        logger.error("Note: ElevenLabs requires ELEVEN_API_KEY environment variable")
        exit(1)

    logger.info("✅ All required environment variables found")
    logger.info(f"🔑 ElevenLabs API Key: {os.getenv('ELEVEN_API_KEY')[:10]}...")

    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
