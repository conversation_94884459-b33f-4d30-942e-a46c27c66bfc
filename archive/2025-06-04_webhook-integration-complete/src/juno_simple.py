#!/usr/bin/env python3
"""
Juno FINAL: ElevenLabs ONLY Agent - Complete System Reboot

This is the DEFINITIVE agent that uses ONLY ElevenLabs TTS.
No fallbacks, no Cartesia, no confusion.

Features:
- OpenAI Whisper STT with Arabic-English language detection
- ElevenLabs TTS ONLY with specific voice ID
- No fallback mechanisms whatsoever
- Clear logging to verify TTS provider
"""

import asyncio
import logging
import os
import json
import uuid
import aiohttp
from datetime import datetime
from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession
from livekit.plugins import openai, silero, elevenlabs

# Import our custom Whisper STT with language detection (WORKING CONFIGURATION)
from whisper_language_stt import create_whisper_language_stt

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MeetingWebhookSender:
    """
    Sends structured meeting data to N8N webhook endpoint.
    """
    def __init__(self, webhook_url: str = None):
        self.webhook_url = webhook_url or "https://n8n.srv753196.hstgr.cloud/webhook/livekit-master"
        self.meeting_id = str(uuid.uuid4())
        self.events = []

    async def send_meeting_event(self, event_type: str, content: str, speaker: str = None):
        """
        Send a meeting event to N8N in the specified JSON format.
        """
        # Add event to our events list
        event = {
            "event_type": event_type,
            "content": content,
            "speaker": speaker,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        self.events.append(event)

        # Create the meeting payload
        payload = {
            "meeting_id": self.meeting_id,
            "meeting_datetime": datetime.utcnow().isoformat() + "Z",
            "summary": {
                "en": f"Meeting event: {content}",
                "ar": f"حدث الاجتماع: {content}"
            },
            "events": [event]  # Send just this event for real-time processing
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        logger.info(f"✅ WEBHOOK: Sent {event_type} event to N8N")
                    else:
                        logger.error(f"❌ WEBHOOK: Failed to send event, status: {response.status}")
        except Exception as e:
            logger.error(f"❌ WEBHOOK: Error sending event: {e}")

    async def send_full_meeting_summary(self):
        """
        Send complete meeting summary with all events.
        """
        if not self.events:
            return

        # Generate summary based on events
        en_summary = f"Meeting with {len(self.events)} events including decisions and questions to Juno."
        ar_summary = f"اجتماع مع {len(self.events)} أحداث تشمل قرارات وأسئلة لجونو."

        payload = {
            "meeting_id": self.meeting_id,
            "meeting_datetime": datetime.utcnow().isoformat() + "Z",
            "summary": {
                "en": en_summary,
                "ar": ar_summary
            },
            "events": self.events
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        logger.info(f"✅ WEBHOOK: Sent complete meeting summary to N8N")
                    else:
                        logger.error(f"❌ WEBHOOK: Failed to send summary, status: {response.status}")
        except Exception as e:
            logger.error(f"❌ WEBHOOK: Error sending summary: {e}")

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections.
    """
    logger.info("🔥 SYSTEM REBOOT: Prewarming Juno with ElevenLabs ONLY...")
    logger.info("- Custom Whisper STT (WORKING Arabic-English detection)")
    logger.info("- OpenAI GPT-4o-mini LLM")
    logger.info("- ElevenLabs TTS ONLY (NO CARTESIA)")
    logger.info("- Silero VAD")
    proc.wait_for_participant = False
    logger.info("🔥 SYSTEM REBOOT: Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for Juno FINAL with ElevenLabs ONLY.
    """
    logger.info("🔥 SYSTEM REBOOT: Starting Juno FINAL with ElevenLabs ONLY...")
    logger.info("🚫 NO CARTESIA - ElevenLabs ONLY MODE")

    # Connect to the room first
    await ctx.connect()
    logger.info("✅ Connected to LiveKit room")

    # Initialize webhook sender for N8N integration
    webhook_sender = MeetingWebhookSender()
    logger.info(f"🔗 WEBHOOK: Initialized for meeting ID: {webhook_sender.meeting_id}")

    # Event detection function
    async def detect_meeting_events(user_speech_text: str):
        """
        Detect meeting events from user speech and send webhooks.
        """
        text = user_speech_text.lower()
        logger.info(f"🔍 WEBHOOK: Analyzing text: '{text}'")

        # Detect decision events
        if any(keyword in text for keyword in ["we decided", "decision", "let's do", "we'll go with", "agreed"]):
            logger.info("🎯 WEBHOOK: Decision event detected!")
            await webhook_sender.send_meeting_event(
                event_type="decision",
                content=user_speech_text,
                speaker="participant"
            )

        # Detect questions to Juno
        if "juno" in text and any(keyword in text for keyword in ["what do you think", "your opinion", "what's your view"]):
            logger.info("🎯 WEBHOOK: Question to Juno detected!")
            await webhook_sender.send_meeting_event(
                event_type="question_to_juno",
                content=user_speech_text,
                speaker="participant"
            )

        # Detect action items
        if any(keyword in text for keyword in ["action item", "todo", "follow up", "next step"]):
            logger.info("🎯 WEBHOOK: Action item detected!")
            await webhook_sender.send_meeting_event(
                event_type="action_item",
                content=user_speech_text,
                speaker="participant"
            )

    # Initialize components with STRICT ElevenLabs-only configuration
    try:
        # Initialize WORKING custom Whisper STT with Arabic-English detection
        # Note: This is the EXACT configuration that was working for Arabic detection
        whisper_stt = create_whisper_language_stt(
            model="whisper-1",
            detect_language=True,
            supported_languages=["en", "ar"],
            add_language_markers=True
        )
        logger.info("✅ CUSTOM WHISPER STT: Initialized with WORKING Arabic-English detection")
        logger.info("🔄 RESTORED: Using proven working configuration for Arabic")

        # Initialize ElevenLabs TTS with specific voice ID - NO ALTERNATIVES
        elevenlabs_tts = elevenlabs.TTS(
            model="eleven_turbo_v2_5",
            voice_id="QRq5hPRAKf5ZhSlTBH6r",  # Your specific voice ID
            language="en",
            enable_ssml_parsing=True,
        )
        logger.info("✅ ELEVENLABS TTS: Initialized with voice ID QRq5hPRAKf5ZhSlTBH6r")
        logger.info("🚫 NO CARTESIA: ElevenLabs is the ONLY TTS provider")

        # Create custom STT wrapper that includes webhook detection
        class WebhookSTT:
            def __init__(self, base_stt, webhook_sender):
                self.base_stt = base_stt
                self.webhook_sender = webhook_sender

            async def recognize(self, buffer, language=None):
                # Call the base STT
                result = await self.base_stt.recognize(buffer, language)

                # If we got text, check for webhook events
                if hasattr(result, 'text') and result.text:
                    await detect_meeting_events(result.text)

                return result

            def __getattr__(self, name):
                # Delegate all other attributes to the base STT
                return getattr(self.base_stt, name)

        # Wrap the STT with webhook detection
        webhook_stt = WebhookSTT(whisper_stt, webhook_sender)

        # Create the Juno agent with improved professional system message
        agent = Agent(
            instructions="""You are Juno, a sophisticated multilingual meeting assistant AI designed to participate in Arabic-English business meetings and conversations. Your primary function is to serve as an intelligent, passive observer who can be activated on-demand to provide assistance.

Default State:
You are in PASSIVE MODE by default. In this mode, you only listen and transcribe. DO NOT respond to anything unless specifically activated.

Activation Rules:
You activate and respond in TWO scenarios:
1. CONVERSATION MODE: When you detect "Juno please activate conversation mode" - enter active conversation mode and respond to ALL questions/requests until someone says "Juno go back to passive mode"
2. SINGLE QUESTION MODE: When you detect "Juno answer this question" followed by a question - respond immediately to that specific question, then return to passive mode

Mode Management:
- In PASSIVE MODE: Only listen, do not respond to anything except activation phrases
- In CONVERSATION MODE: Respond to all questions and requests normally until deactivated
- To exit CONVERSATION MODE: Wait for "Juno go back to passive mode" then return to passive listening

If you hear any other words, phrases, or questions while in PASSIVE MODE - DO NOT respond at all, just remain silent.
Do NOT announce mode changes during the conversation.

Language Handling:
You will receive transcripts with language markers like [EN]text[/EN] and [AR]text[/AR].
Process both English and Arabic content naturally.
ALWAYS respond in the same language as the user's question.
If the user speaks in Arabic, respond in Arabic.
If the user speaks in English, respond in English.
Handle code-switching (mixing languages) gracefully.

Voice Rules (when activated):
Keep responses brief and conversational.
Be friendly and helpful in both languages.
Don't use markdown or special characters in responses.
Don't read out language markers like [EN] or [AR].
Use natural pronunciation for each language.

Processing Instructions:
1. Carefully listen to the conversation.
2. Identify your current mode and any mode change commands:
   - "Juno please activate conversation mode" (enter conversation mode)
   - "Juno answer this question" (single question mode)
   - "Juno go back to passive mode" (return to passive mode)
3. In CONVERSATION MODE: Respond to all questions and requests normally
4. In SINGLE QUESTION MODE: Respond to the specific question, then return to passive mode
5. In PASSIVE MODE: Do not respond to anything except activation phrases
6. Prepare responses in the same language as the question/request.

Remember:
- CONVERSATION MODE stays active until explicitly deactivated
- SINGLE QUESTION MODE returns to passive after one response
- PASSIVE MODE only responds to activation phrases"""
        )

        # Create agent session with WORKING STT + ElevenLabs TTS + Webhook detection
        session = AgentSession(
            vad=silero.VAD.load(),
            stt=webhook_stt,  # WORKING: Custom Whisper STT with webhook detection
            llm=openai.LLM(model="gpt-4.1-mini", temperature=0.1),
            tts=elevenlabs_tts,  # ONLY ElevenLabs - no list, no fallbacks
        )
        logger.info("✅ AGENT SESSION: Created with ElevenLabs ONLY and webhook detection")

    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR: Cannot initialize ElevenLabs TTS: {e}")
        logger.error("❌ SYSTEM HALT: No fallbacks allowed - fix ElevenLabs configuration")
        raise e  # Fail completely rather than use Cartesia

    # Add shutdown callback to send final meeting summary
    async def send_final_summary():
        await webhook_sender.send_full_meeting_summary()
        logger.info("📋 MEETING: Final summary sent to N8N")

    ctx.add_shutdown_callback(send_final_summary)

    # Start the session
    await session.start(agent=agent, room=ctx.room)
    logger.info("✅ AGENT SESSION: Started successfully with ElevenLabs ONLY and webhook detection")

    # Send short initial greeting
    await session.say(
        "Hi, Juno Online and in Passive Mode. مرحبا أنا جونو"
    )

    logger.info("🎉 JUNO FINAL: Ready with OPTIMIZED STT + ElevenLabs TTS + N8N Webhooks!")
    logger.info("🔄 STT PROVIDER: OpenAI Whisper (optimized for Arabic-English)")
    logger.info("🔊 TTS PROVIDER: ElevenLabs ONLY (voice: QRq5hPRAKf5ZhSlTBH6r)")
    logger.info("🔗 WEBHOOK PROVIDER: N8N Direct Integration (bypassing Dify)")
    logger.info(f"📋 MEETING ID: {webhook_sender.meeting_id}")
    logger.info("🚫 NO CARTESIA: System configured to prevent any fallbacks")
    logger.info("📝 Test with Arabic: 'مرحبا جونو، كيف حالك اليوم؟'")
    logger.info("📝 Test with English: 'Hello Juno, how are you today?'")
    logger.info("🌍 MULTILINGUAL: Perfect Arabic-English code-switching support!")
    logger.info("🎯 WEBHOOK EVENTS: Detecting decisions, questions to Juno, and action items")
    logger.info("🎯 WEBHOOK URL: https://n8n.srv753196.hstgr.cloud/webhook/livekit-master")

if __name__ == "__main__":
    # Verify environment variables
    required_env_vars = [
        "OPENAI_API_KEY",
        "ELEVEN_API_KEY",
        # "DEEPGRAM_API_KEY",  # Not needed for OpenAI Whisper STT
        "LIVEKIT_URL",
        "LIVEKIT_API_KEY",
        "LIVEKIT_API_SECRET"
    ]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        logger.error(f"❌ MISSING ENV VARS: {missing_vars}")
        logger.error("❌ SYSTEM HALT: Fix environment configuration")
        exit(1)

    logger.info("✅ ENV VARS: All required variables found")
    logger.info(f"🔑 ElevenLabs API Key: {os.getenv('ELEVEN_API_KEY')[:10]}...")
    logger.info(f"🔄 OpenAI API Key: {os.getenv('OPENAI_API_KEY')[:10]}...")
    logger.info(f"🎤 Voice ID: QRq5hPRAKf5ZhSlTBH6r")
    logger.info("🔥 SYSTEM REBOOT: OPTIMIZED STT + ElevenLabs ONLY mode")

    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
