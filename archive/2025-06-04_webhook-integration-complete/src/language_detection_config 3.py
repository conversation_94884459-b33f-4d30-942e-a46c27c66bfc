#!/usr/bin/env python3
"""
Language Detection Configuration for Juno Step 2A

This module contains configuration settings and utilities for Arabic-English
language detection in the Juno meeting assistant.
"""

from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class LanguageConfig:
    """Configuration for language detection settings."""

    # Supported languages
    supported_languages: List[str]

    # Language markers for output formatting
    language_markers: Dict[str, str]

    # Whisper model settings
    whisper_model: str

    # Detection settings
    enable_language_detection: bool
    add_language_markers: bool

    # Performance settings
    confidence_threshold: float
    segment_min_length: float  # Minimum segment length in seconds

    # Fallback settings
    default_language: str
    fallback_to_english: bool

# Default configuration for Arabic-English detection
DEFAULT_ARABIC_ENGLISH_CONFIG = LanguageConfig(
    supported_languages=["en", "ar"],
    language_markers={
        "en": "[EN]",
        "ar": "[AR]",
        "unknown": "[UNK]"
    },
    whisper_model="whisper-1",
    enable_language_detection=True,
    add_language_markers=True,
    confidence_threshold=0.7,
    segment_min_length=0.5,
    default_language="en",
    fallback_to_english=True
)

# Language names for logging and display
LANGUAGE_NAMES = {
    "en": "English",
    "ar": "Arabic",
    "unknown": "Unknown"
}

# Common Arabic-English code-switching patterns
CODE_SWITCHING_PATTERNS = {
    "business_terms": {
        "en": ["budget", "meeting", "deadline", "project", "report", "presentation"],
        "ar": ["ميزانية", "اجتماع", "موعد نهائي", "مشروع", "تقرير", "عرض تقديمي"]
    },
    "time_expressions": {
        "en": ["today", "tomorrow", "yesterday", "week", "month", "year"],
        "ar": ["اليوم", "غدا", "أمس", "أسبوع", "شهر", "سنة"]
    },
    "common_phrases": {
        "en": ["how are you", "thank you", "please", "excuse me"],
        "ar": ["كيف حالك", "شكرا لك", "من فضلك", "عذرا"]
    }
}

def get_language_config(config_name: str = "default") -> LanguageConfig:
    """
    Get language configuration by name.

    Args:
        config_name: Name of the configuration to retrieve

    Returns:
        LanguageConfig instance
    """
    configs = {
        "default": DEFAULT_ARABIC_ENGLISH_CONFIG,
        "arabic_english": DEFAULT_ARABIC_ENGLISH_CONFIG,
        "english_only": LanguageConfig(
            supported_languages=["en"],
            language_markers={"en": "[EN]"},
            whisper_model="whisper-1",
            enable_language_detection=False,
            add_language_markers=False,
            confidence_threshold=0.8,
            segment_min_length=0.3,
            default_language="en",
            fallback_to_english=True
        ),
        "arabic_only": LanguageConfig(
            supported_languages=["ar"],
            language_markers={"ar": "[AR]"},
            whisper_model="whisper-1",
            enable_language_detection=False,
            add_language_markers=False,
            confidence_threshold=0.8,
            segment_min_length=0.3,
            default_language="ar",
            fallback_to_english=False
        )
    }

    return configs.get(config_name, DEFAULT_ARABIC_ENGLISH_CONFIG)

def validate_language_code(language_code: str, config: LanguageConfig) -> bool:
    """
    Validate if a language code is supported.

    Args:
        language_code: Language code to validate
        config: Language configuration

    Returns:
        True if language is supported, False otherwise
    """
    return language_code in config.supported_languages

def format_transcript_with_markers(
    text: str,
    language: str,
    config: LanguageConfig
) -> str:
    """
    Format transcript text with language markers.

    Args:
        text: Input text
        language: Detected language code
        config: Language configuration

    Returns:
        Formatted text with language markers
    """
    if not config.add_language_markers or not text.strip():
        return text

    # Only add markers for supported languages
    if language not in config.language_markers:
        return text

    marker_start = config.language_markers[language]
    marker_end = f"[/{language.upper()}]"
    return f"{marker_start}{text.strip()}{marker_end}"

def detect_code_switching_context(text: str) -> Dict[str, float]:
    """
    Analyze text for code-switching context clues.

    Args:
        text: Input text to analyze

    Returns:
        Dictionary with language confidence scores
    """
    text_lower = text.lower()
    scores = {"en": 0.0, "ar": 0.0}

    # Check for English patterns
    for category, patterns in CODE_SWITCHING_PATTERNS.items():
        for en_term in patterns.get("en", []):
            if en_term.lower() in text_lower:
                scores["en"] += 0.1

        for ar_term in patterns.get("ar", []):
            if ar_term in text:  # Arabic terms are case-sensitive
                scores["ar"] += 0.1

    # Normalize scores
    total_score = sum(scores.values())
    if total_score > 0:
        scores = {lang: score / total_score for lang, score in scores.items()}

    return scores

def get_language_display_name(language_code: str) -> str:
    """
    Get display name for language code.

    Args:
        language_code: Language code

    Returns:
        Human-readable language name
    """
    return LANGUAGE_NAMES.get(language_code, language_code.upper())

def create_test_scenarios() -> List[Dict[str, str]]:
    """
    Create test scenarios for language detection validation.

    Returns:
        List of test scenarios with expected results
    """
    return [
        {
            "name": "Pure English",
            "input": "Hello, how are you today? We need to discuss the project timeline.",
            "expected_language": "en",
            "expected_output": "[EN]Hello, how are you today? We need to discuss the project timeline.[/EN]"
        },
        {
            "name": "Pure Arabic",
            "input": "مرحبا، كيف حالك اليوم؟ نحتاج لمناقشة الجدول الزمني للمشروع.",
            "expected_language": "ar",
            "expected_output": "[AR]مرحبا، كيف حالك اليوم؟ نحتاج لمناقشة الجدول الزمني للمشروع.[/AR]"
        },
        {
            "name": "English-Arabic Code-switching",
            "input": "We need to finish the budget قبل نهاية الأسبوع by Friday",
            "expected_language": "mixed",
            "expected_output": "We need to finish the [EN]budget[/EN] [AR]قبل نهاية الأسبوع[/AR] [EN]by Friday[/EN]"
        },
        {
            "name": "Business Meeting Mixed",
            "input": "The presentation was good لكن نحتاج تعديلات on the financial projections",
            "expected_language": "mixed",
            "expected_output": "The [EN]presentation was good[/EN] [AR]لكن نحتاج تعديلات[/AR] [EN]on the financial projections[/EN]"
        },
        {
            "name": "Short English Response",
            "input": "Yes, exactly",
            "expected_language": "en",
            "expected_output": "[EN]Yes, exactly[/EN]"
        },
        {
            "name": "Short Arabic Response",
            "input": "نعم، بالضبط",
            "expected_language": "ar",
            "expected_output": "[AR]نعم، بالضبط[/AR]"
        }
    ]

# Export commonly used configurations
__all__ = [
    "LanguageConfig",
    "DEFAULT_ARABIC_ENGLISH_CONFIG",
    "LANGUAGE_NAMES",
    "CODE_SWITCHING_PATTERNS",
    "get_language_config",
    "validate_language_code",
    "format_transcript_with_markers",
    "detect_code_switching_context",
    "get_language_display_name",
    "create_test_scenarios"
]
