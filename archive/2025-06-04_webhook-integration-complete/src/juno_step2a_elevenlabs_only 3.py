#!/usr/bin/env python3
"""
Juno Step 2A: ElevenLabs ONLY - No Fallbacks

This agent ensures ONLY ElevenLabs TTS is used with no fallbacks to Cartesia.
Implements robust error handling to prevent any TTS switching.

Features:
- OpenAI Whisper STT with Arabic-English language detection
- ElevenLabs TTS ONLY - no fallbacks
- Specific voice ID for consistency
- Enhanced error handling to prevent TTS switching
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerO<PERSON>s, cli, Agent, AgentSession
from livekit.plugins import openai, silero, elevenlabs

# Import our custom Whisper STT with language detection
from whisper_language_stt import create_whisper_language_stt

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections.
    """
    logger.info("Prewarming Juno Step 2A with ElevenLabs ONLY (no fallbacks)...")
    logger.info("- OpenAI Whisper STT with Arabic-English detection")
    logger.info("- OpenAI GPT-4o-mini LLM")
    logger.info("- ElevenLabs TTS ONLY (specific voice ID)")
    logger.info("- Silero VAD")
    proc.wait_for_participant = False
    logger.info("Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for Juno Step 2A with ElevenLabs ONLY.
    """
    logger.info("🚀 Starting Juno Step 2A with ElevenLabs ONLY (no fallbacks)...")

    # Connect to the room first
    await ctx.connect()
    logger.info("✅ Connected to LiveKit room")

    # Create the Juno agent with enhanced multilingual instructions
    agent = Agent(
        instructions="""You are Juno, a multilingual meeting assistant AI with Arabic-English language detection capabilities.

أنت جونو، مساعد اجتماعات ذكي متعدد اللغات مع قدرات كشف اللغة العربية والإنجليزية.

LANGUAGE HANDLING:
- You receive transcripts with language markers like [EN]text[/EN] and [AR]text[/AR]
- Process both English and Arabic content naturally
- ALWAYS respond in the same language as the user's question
- If user speaks Arabic, respond in Arabic
- If user speaks English, respond in English
- If user mixes languages, respond in the dominant language or ask which they prefer
- Handle code-switching (mixing languages) gracefully

MULTILINGUAL RESPONSES:
- For Arabic input: Respond completely in Arabic with natural flow
- For English input: Respond completely in English
- Be conversational and helpful in both languages
- Use appropriate cultural greetings and expressions

VOICE RULES:
- Keep responses brief and conversational
- Be friendly and helpful in both languages
- Don't use markdown or special characters in responses
- Don't read out language markers like [EN] or [AR]
- Use natural pronunciation for each language

TESTING MODE:
You are currently in active testing mode to verify Arabic-English detection and ElevenLabs TTS consistency.
Respond enthusiastically to test both languages and encourage users to try different scenarios."""
    )

    # Initialize components with robust error handling
    try:
        # Initialize our custom Whisper STT with language detection
        whisper_stt = create_whisper_language_stt(
            model="whisper-1",
            detect_language=True,
            supported_languages=["en", "ar"],
            add_language_markers=True
        )
        logger.info("✅ Initialized Whisper STT with Arabic-English detection")

        # Initialize ElevenLabs TTS with specific configuration - NO FALLBACKS
        elevenlabs_tts = elevenlabs.TTS(
            model="eleven_turbo_v2_5",  # Good balance for Arabic-English multilingual
            voice_id="gOkFV1JMCt0G0n9xmBwV",  # Specific ElevenLabs voice ID provided
            language="en",  # Default language, will be updated dynamically
            enable_ssml_parsing=True,  # Enable SSML for pronunciation control
            # API key will be read from ELEVEN_API_KEY environment variable
        )
        logger.info("✅ Initialized ElevenLabs TTS with specific voice ID (NO FALLBACKS)")

        # Create agent session with all components - SINGLE TTS ONLY
        session = AgentSession(
            vad=silero.VAD.load(),  # Voice Activity Detection
            stt=whisper_stt,  # Our custom Whisper STT with language detection
            llm=openai.LLM(model="gpt-4o-mini", temperature=0.1),  # Very low temperature for consistency
            tts=elevenlabs_tts,  # ElevenLabs TTS ONLY - no fallback list
        )
        logger.info("✅ Agent session initialized with ElevenLabs ONLY")

    except Exception as e:
        logger.error(f"❌ Error initializing agent session: {e}")
        logger.error("❌ CRITICAL: Cannot initialize without ElevenLabs TTS")
        logger.error("❌ Check your ELEVEN_API_KEY and voice_id configuration")
        raise e  # Don't fallback - fail fast if ElevenLabs doesn't work

    # Start the session
    await session.start(agent=agent, room=ctx.room)
    logger.info("✅ Agent session started successfully with ElevenLabs ONLY")

    # Send initial greeting in English (ElevenLabs with specific voice ID)
    await session.say(
        "Hello! I'm Juno with Arabic-English language detection and ElevenLabs voice synthesis. "
        "I'm using ONLY ElevenLabs TTS with no fallbacks to ensure consistent voice quality. "
        "I can understand both Arabic and English speech perfectly. "
        "Please try speaking to me in Arabic, English, or mixing both languages to test the detection. "
        "I will respond in the same language you use with consistent ElevenLabs pronunciation."
    )
    
    logger.info("🎉 Juno Step 2A with ElevenLabs ONLY is ready!")
    logger.info("📝 Arabic Testing Scenarios:")
    logger.info("   1. Pure Arabic: 'مرحبا جونو، كيف حالك اليوم؟'")
    logger.info("   2. Arabic question: 'ما هو الطقس اليوم؟'")
    logger.info("   3. Pure English: 'Hello Juno, how are you today?'")
    logger.info("   4. Code-switching: 'We need to finish the budget قبل نهاية الأسبوع by Friday'")
    logger.info("   5. Mixed conversation: Start in Arabic, then switch to English")
    logger.info("🎯 Expected: Agent responds in same language with CONSISTENT ElevenLabs voice")
    logger.info("🚫 NO FALLBACKS: If ElevenLabs fails, agent will fail (no Cartesia fallback)")

if __name__ == "__main__":
    # Verify environment variables
    required_env_vars = [
        "OPENAI_API_KEY", 
        "ELEVEN_API_KEY",  # ElevenLabs API key (proper name)
        "LIVEKIT_URL", 
        "LIVEKIT_API_KEY", 
        "LIVEKIT_API_SECRET"
    ]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {missing_vars}")
        logger.error("Please check your .env file and ensure all required API keys are set.")
        logger.error("Note: ElevenLabs requires ELEVEN_API_KEY environment variable")
        exit(1)
    
    logger.info("✅ All required environment variables found")
    logger.info(f"🔑 ElevenLabs API Key: {os.getenv('ELEVEN_API_KEY')[:10]}...")
    logger.info(f"🎤 Voice ID: gOkFV1JMCt0G0n9xmBwV")
    logger.info("🚫 NO FALLBACKS: ElevenLabs ONLY mode enabled")
    
    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
