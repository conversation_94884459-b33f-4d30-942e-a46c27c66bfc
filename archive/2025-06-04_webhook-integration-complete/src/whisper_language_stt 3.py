#!/usr/bin/env python3
"""
OpenAI Whisper STT with Arabic-English Language Detection

This module implements OpenAI Whisper STT integration for LiveKit agents
with specific support for Arabic-English code-switching detection.

Features:
- Real-time Arabic-English language detection
- Language markers in transcription output
- Seamless code-switching within sentences
- Compatible with existing LiveKit agent architecture
"""

import asyncio
import logging
from typing import Optional, List, Dict, Any
import openai
from livekit.plugins import openai as livekit_openai

# Configure logging
logger = logging.getLogger(__name__)

class WhisperLanguageSTT(livekit_openai.STT):
    """
    OpenAI Whisper STT with Arabic-English language detection.

    This class extends the LiveKit OpenAI STT plugin to provide:
    - Automatic language detection between English and Arabic
    - Language markers in transcription output
    - Support for code-switching within the same audio segment
    """

    def __init__(
        self,
        *,
        model: str = "whisper-1",
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        language: Optional[str] = None,  # Set to None for auto-detection
        detect_language: bool = True,
        supported_languages: List[str] = ["en", "ar"],
        add_language_markers: bool = True,
        **kwargs
    ):
        """
        Initialize Whisper STT with language detection.

        Args:
            model: OpenAI Whisper model to use (default: "whisper-1")
            api_key: OpenAI API key (uses environment variable if None)
            base_url: Custom API base URL (optional)
            language: Fixed language code or None for auto-detection
            detect_language: Enable language detection
            supported_languages: List of supported language codes
            add_language_markers: Add language markers to output
            **kwargs: Additional arguments passed to OpenAI STT
        """
        # Initialize parent OpenAI STT with language detection disabled
        super().__init__(
            model=model,
            api_key=api_key,
            base_url=base_url,
            language=language if not detect_language else None,
            **kwargs
        )

        # Configuration for language detection
        self._detect_language = detect_language
        self._supported_languages = supported_languages
        self._add_language_markers = add_language_markers
        self._fixed_language = language

        # Language markers for output formatting
        self._language_markers = {
            "en": "[EN]",
            "ar": "[AR]"
        }

        logger.info(f"Initialized WhisperLanguageSTT with model={model}, "
                   f"language_detection={detect_language}, "
                   f"supported_languages={supported_languages}")

    async def _recognize_impl(
        self,
        buffer,
        *,
        language: Optional[str] = None,
        conn_options=None,
    ):
        """
        Implement speech recognition with language detection.

        This method overrides the parent implementation to add language detection
        and markers to the transcription output.
        """
        logger.info(f"🎤 Processing audio buffer of size: {len(buffer.data) if hasattr(buffer, 'data') else 'unknown'}")

        # First, get the standard transcription from parent class
        speech_event = await super()._recognize_impl(buffer, language=language, conn_options=conn_options)

        logger.info(f"📝 Received speech event: {speech_event.type}")
        if speech_event.alternatives:
            logger.info(f"🔤 Original text: '{speech_event.alternatives[0].text}'")
            logger.info(f"🌍 Detected language: {getattr(speech_event.alternatives[0], 'language', 'unknown')}")

        # If language detection is disabled, return as-is
        if not self._detect_language or not self._add_language_markers:
            logger.info("⚠️ Language detection or markers disabled, returning original")
            return speech_event

        # Process the transcription to add language markers
        if speech_event.alternatives:
            original_alternative = speech_event.alternatives[0]
            original_text = original_alternative.text
            detected_language = getattr(original_alternative, 'language', 'en')

            # Add language markers to the text
            processed_text = self._add_language_markers_to_text(
                original_text,
                detected_language
            )

            logger.info(f"✨ Processed text with markers: '{processed_text}'")

            # Create new speech data with processed text
            from livekit.agents import stt
            processed_alternative = stt.SpeechData(
                text=processed_text,
                language=detected_language,
                confidence=getattr(original_alternative, 'confidence', 1.0)
            )

            # Return new speech event with processed text
            return stt.SpeechEvent(
                type=speech_event.type,
                alternatives=[processed_alternative]
            )

        logger.warning("⚠️ No alternatives in speech event")
        return speech_event

    def _add_language_markers_to_text(self, text: str, language: str) -> str:
        """
        Add language markers to simple text.

        Args:
            text: Input text
            language: Detected language code

        Returns:
            Text with language markers
        """
        if not self._add_language_markers or not text.strip():
            return text

        if language in self._language_markers:
            marker_start = self._language_markers[language]
            marker_end = f"[/{language.upper()}]"
            return f"{marker_start}{text.strip()}{marker_end}"

        return text

    @property
    def supported_languages(self) -> List[str]:
        """Return list of supported language codes."""
        return self._supported_languages.copy()

    @property
    def detect_language(self) -> bool:
        """Return whether language detection is enabled."""
        return self._detect_language

    def update_language_settings(
        self,
        *,
        supported_languages: Optional[List[str]] = None,
        add_language_markers: Optional[bool] = None,
        detect_language: Optional[bool] = None
    ):
        """
        Update language detection settings.

        Args:
            supported_languages: New list of supported languages
            add_language_markers: Enable/disable language markers
            detect_language: Enable/disable language detection
        """
        if supported_languages is not None:
            self._supported_languages = supported_languages
            logger.info(f"Updated supported languages: {supported_languages}")

        if add_language_markers is not None:
            self._add_language_markers = add_language_markers
            logger.info(f"Updated language markers: {add_language_markers}")

        if detect_language is not None:
            self._detect_language = detect_language
            logger.info(f"Updated language detection: {detect_language}")


# Convenience function for easy integration
def create_whisper_language_stt(**kwargs) -> WhisperLanguageSTT:
    """
    Create a WhisperLanguageSTT instance with default settings for Arabic-English detection.

    Args:
        **kwargs: Additional arguments passed to WhisperLanguageSTT

    Returns:
        Configured WhisperLanguageSTT instance
    """
    default_config = {
        "model": "whisper-1",
        "detect_language": True,
        "supported_languages": ["en", "ar"],
        "add_language_markers": True,
        "language": None  # Auto-detect
    }

    # Merge user config with defaults
    config = {**default_config, **kwargs}

    return WhisperLanguageSTT(**config)
