#!/usr/bin/env python3
"""
Juno Step 2A: LiveKit Agent with Arabic-English Language Detection

This agent builds on Step 1 foundation and adds:
- OpenAI Whisper STT with language detection
- Arabic-English code-switching support
- Language markers in transcription output
- Maintains all Step 1 functionality (passive listening, wake-word ready)
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession
from livekit.plugins import openai, cartesia, silero

# Import our custom Whisper STT with language detection
from whisper_language_stt import create_whisper_language_stt

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections.
    """
    logger.info("Prewarming Juno Step 2A agent components...")
    logger.info("- OpenAI Whisper STT with Arabic-English detection")
    logger.info("- OpenAI GPT-4o-mini LLM")
    logger.info("- Cartesia TTS")
    logger.info("- Silero VAD")
    proc.wait_for_participant = False
    logger.info("Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for Juno Step 2A agent with language detection.
    """
    logger.info("🚀 Starting Juno Step 2A Agent with Arabic-English Detection...")

    # Connect to the room first
    await ctx.connect()
    logger.info("✅ Connected to LiveKit room")

    # Create the Juno agent with updated instructions for multilingual support
    agent = Agent(
        instructions="""You are Juno, a meeting assistant AI with Arabic-English language detection capabilities.

You are designed to be a passive meeting assistant that:
- Listens continuously to meetings in both English and Arabic
- Records and transcribes conversations with language detection
- Can answer questions when called by name in either language
- Takes notes and extracts action items from multilingual conversations
- Automates follow-up tasks

LANGUAGE HANDLING:
- You receive transcripts with language markers like [EN]text[/EN] and [AR]text[/AR]
- Process both English and Arabic content naturally
- Respond in the same language as the user's question
- Handle code-switching (mixing languages) gracefully
- Maintain conversation context across language switches

VOICE RULES:
- Keep responses brief and conversational for voice interaction
- Be friendly and helpful in both languages
- Don't use markdown or special characters in responses
- Speak naturally without reading out language markers

For this test, you are in active mode to verify the language detection works.
Test scenarios: pure English, pure Arabic, and mixed English-Arabic."""
    )

    # Create the agent session with our custom Whisper STT
    try:
        # Initialize our custom Whisper STT with language detection
        whisper_stt = create_whisper_language_stt(
            model="whisper-1",
            detect_language=True,
            supported_languages=["en", "ar"],
            add_language_markers=True
        )
        logger.info("✅ Initialized Whisper STT with Arabic-English detection")

        # Create agent session with all components
        session = AgentSession(
            vad=silero.VAD.load(),  # Voice Activity Detection
            stt=whisper_stt,  # Our custom Whisper STT with language detection
            llm=openai.LLM(model="gpt-4o-mini", temperature=0.1),  # Very low temperature for consistency
            tts=cartesia.TTS(model="sonic-english"),  # Text-to-Speech
        )
        logger.info("✅ Agent session components initialized")

    except Exception as e:
        logger.error(f"❌ Error initializing agent session: {e}")
        # Fallback to basic configuration
        logger.info("🔄 Falling back to basic OpenAI STT...")
        session = AgentSession(
            vad=silero.VAD.load(),
            stt=openai.STT(),  # Fallback to basic OpenAI STT
            llm=openai.LLM(model="gpt-4o-mini", temperature=0.1),
            tts=cartesia.TTS(model="sonic-english"),
        )

    # Start the session
    await session.start(agent=agent, room=ctx.room)
    logger.info("✅ Agent session started successfully")

    # Send initial greeting with language detection info
    await session.say(
        "Hello! I'm Juno with Arabic-English language detection. "
        "I can understand and process both English and Arabic speech, "
        "including when you switch between languages. "
        "Try speaking in English, Arabic, or mixing both languages to test the detection."
    )
    
    logger.info("🎉 Juno Step 2A agent is ready with language detection!")
    logger.info("📝 Testing scenarios:")
    logger.info("   1. Pure English: 'Hello, how are you today?'")
    logger.info("   2. Pure Arabic: 'مرحبا، كيف حالك اليوم؟'")
    logger.info("   3. Code-switching: 'We need to finish the budget قبل نهاية الأسبوع by Friday'")

if __name__ == "__main__":
    # Verify environment variables
    required_env_vars = ["OPENAI_API_KEY", "CARTESIA_API_KEY", "LIVEKIT_URL", "LIVEKIT_API_KEY", "LIVEKIT_API_SECRET"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {missing_vars}")
        logger.error("Please check your .env file and ensure all required API keys are set.")
        exit(1)
    
    logger.info("✅ All required environment variables found")
    
    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
