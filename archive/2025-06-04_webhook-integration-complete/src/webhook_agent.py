#!/usr/bin/env python3

import asyncio
import logging
import os
import aiohttp
import json
from datetime import datetime

from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Webhook URL
WEBHOOK_URL = "https://n8n.srv753196.hstgr.cloud/webhook/assistant"

def generate_sql_query(action: str, item_name: str, quantity: int = None, price: float = None) -> str:
    """Generate PostgreSQL queries for inventory operations

    Database schema: public.inventory
    Fields: item_code, stock, price_usd, price_aed, price_eur, location
    """

    # Clean item name for SQL
    clean_item = item_name.replace("'", "''").strip()

    if action == "check_quantity":
        return f"SELECT item_code, stock, location FROM public.inventory WHERE LOWER(item_code) LIKE LOWER('%{clean_item}%');"

    elif action == "check_price":
        return f"SELECT item_code, price_usd, price_aed, price_eur FROM public.inventory WHERE LOWER(item_code) LIKE LOWER('%{clean_item}%');"

    elif action == "add_inventory":
        return f"UPDATE public.inventory SET stock = stock + {quantity} WHERE LOWER(item_code) LIKE LOWER('%{clean_item}%');"

    elif action == "get_item_details":
        return f"SELECT item_code, stock, price_usd, price_aed, price_eur, location FROM public.inventory WHERE LOWER(item_code) LIKE LOWER('%{clean_item}%');"

    elif action == "find_lowest_stock":
        return f"SELECT item_code, stock, location FROM public.inventory ORDER BY stock ASC LIMIT 5;"

    elif action == "find_highest_stock":
        return f"SELECT item_code, stock, location FROM public.inventory ORDER BY stock DESC LIMIT 5;"

    else:
        return f"SELECT * FROM public.inventory WHERE LOWER(item_code) LIKE LOWER('%{clean_item}%');"

async def send_inventory_command(action: str, item_name: str, quantity: int = None, price: float = None) -> bool:
    """Send inventory command to webhook with PostgreSQL query"""

    sql_query = generate_sql_query(action, item_name, quantity, price)

    payload = {
        "action": action,
        "item_name": item_name,
        "quantity": quantity,
        "price": price,
        "sql_query": sql_query,
        "timestamp": datetime.now().isoformat(),
        "source": "inventory_voice_assistant"
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(WEBHOOK_URL, json=payload) as response:
                if response.status == 200:
                    logger.info(f"Successfully sent inventory command: {payload}")
                    return True
                else:
                    logger.error(f"Webhook error: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"Error sending to webhook: {e}")
        return False

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections before the agent starts.
    This helps reduce latency when the agent first connects.
    """
    logger.info("Prewarming agent components...")
    proc.wait_for_participant = False
    logger.info("Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for the voice agent using modern LiveKit Agents API.
    """
    global collected_data, confirmation_pending

    logger.info("Starting LiveKit Voice Agent...")

    # CRITICAL: Connect to the room first
    await ctx.connect()
    logger.info("Connected to LiveKit room")

    # Import plugins
    from livekit.plugins import deepgram, openai, cartesia, silero

    # Create the agent with inventory management instructions
    agent = Agent(
        instructions="""You are Alex, an inventory management voice assistant. You help users check inventory, prices, and update stock levels.

DATABASE INFO:
- Table: public.inventory
- Fields: item_code (SKU001-SKU030), stock, price_usd, price_aed, price_eur, location

Your capabilities:
1. CHECK QUANTITY: When users ask "How many X items are there?" or "What's the stock of SKU001?"
   - Extract the item code (like SKU001, SKU002, etc.)
   - Respond: "Let me check the stock for [item_code] in our inventory. Should I proceed?"
   - Use action: "check_quantity"

2. CHECK PRICE: When users ask "What's the price of SKU001?" or "How much does SKU002 cost?"
   - Extract the item code
   - Respond: "Let me check the current price for [item_code]. Should I proceed?"
   - Use action: "check_price"

3. ADD INVENTORY: When users say "Add 10 pieces of SKU001" or "Increase stock by 5 for SKU002"
   - Extract item code and quantity
   - Respond: "I'll add [quantity] pieces of [item_code] to the inventory. Should I proceed?"
   - Use action: "add_inventory"

4. FIND LOWEST STOCK: When users ask "What's the lowest stock item?" or "Which item has the least quantity?"
   - Respond: "Let me find the items with the lowest stock levels. Should I proceed?"
   - Use action: "find_lowest_stock"

5. GENERAL INFO: When users ask about an item without specifying quantity or price
   - Use action: "get_item_details"

IMPORTANT RULES:
- Always confirm the action before executing: "Should I proceed with this inventory query?"
- Wait for user confirmation (yes/no) before sending commands
- Keep responses brief and conversational for voice interaction
- Don't use markdown, asterisks, or special characters in responses
- Item codes are SKU001 through SKU030
- When users say numbers like "zero zero one", interpret as "SKU001"

Example interactions:
User: "How many SKU001 are there?"
You: "Let me check the stock for SKU001 in our inventory. Should I proceed?"

User: "Add 10 more SKU002"
You: "I'll add 10 pieces of SKU002 to the inventory. Should I proceed?"

User: "What's the lowest stock item?"
You: "Let me find the items with the lowest stock levels. Should I proceed?"

Remember: Always confirm before executing any inventory commands."""
    )

    # Create the agent session with all components
    session = AgentSession(
        vad=silero.VAD.load(),  # Use Silero VAD as recommended
        stt=deepgram.STT(model="nova-2-general", language="en"),
        llm=openai.LLM(model="gpt-4o-mini", temperature=0.1),  # Lower temperature for more consistent responses
        tts=cartesia.TTS(model="sonic-english"),
    )

    # Add webhook handling logic
    @session.on("user_speech_committed")
    async def on_user_speech(user_msg):
        """Handle user speech and trigger webhooks when confirmed"""
        user_text = user_msg.content.lower().strip()
        logger.info(f"User said: {user_msg.content}")

        # Check for confirmation responses
        if any(word in user_text for word in ['yes', 'yeah', 'yep', 'proceed', 'go ahead', 'do it']):
            # Look for the last assistant message to determine what action to take
            chat_ctx = session._chat_ctx
            if chat_ctx and len(chat_ctx.messages) >= 2:
                last_assistant_msg = None
                for msg in reversed(chat_ctx.messages):
                    if msg.role == 'assistant':
                        last_assistant_msg = msg.content.lower()
                        break

                if last_assistant_msg:
                    # Determine action and item from the assistant's last message
                    if "check the stock" in last_assistant_msg or "check the quantity" in last_assistant_msg:
                        if "sku" in last_assistant_msg:
                            # Extract SKU from assistant message
                            import re
                            sku_match = re.search(r'sku(\d+)', last_assistant_msg)
                            if sku_match:
                                item_code = f"SKU{sku_match.group(1).zfill(3)}"
                                success = await send_inventory_command("check_quantity", item_code)
                                if success:
                                    await session.say("Perfect! I've sent the stock query to our system.")
                                else:
                                    await session.say("Sorry, there was an issue with the query.")
                                return

                    elif "check the price" in last_assistant_msg:
                        if "sku" in last_assistant_msg:
                            import re
                            sku_match = re.search(r'sku(\d+)', last_assistant_msg)
                            if sku_match:
                                item_code = f"SKU{sku_match.group(1).zfill(3)}"
                                success = await send_inventory_command("check_price", item_code)
                                if success:
                                    await session.say("Perfect! I've sent the price query to our system.")
                                else:
                                    await session.say("Sorry, there was an issue with the query.")
                                return

                    elif "lowest stock" in last_assistant_msg:
                        success = await send_inventory_command("find_lowest_stock", "")
                        if success:
                            await session.say("Perfect! I've sent the lowest stock query to our system.")
                        else:
                            await session.say("Sorry, there was an issue with the query.")
                        return

                    elif "add" in last_assistant_msg and "pieces" in last_assistant_msg:
                        # Extract quantity and SKU from assistant message
                        import re
                        qty_match = re.search(r'add (\d+)', last_assistant_msg)
                        sku_match = re.search(r'sku(\d+)', last_assistant_msg)
                        if qty_match and sku_match:
                            quantity = int(qty_match.group(1))
                            item_code = f"SKU{sku_match.group(1).zfill(3)}"
                            success = await send_inventory_command("add_inventory", item_code, quantity)
                            if success:
                                await session.say(f"Perfect! I've added {quantity} pieces of {item_code} to the inventory.")
                            else:
                                await session.say("Sorry, there was an issue updating the inventory.")
                            return

    # Start the session with the agent and room
    await session.start(agent=agent, room=ctx.room)
    logger.info("Agent session started successfully")

    # Generate initial greeting
    await session.generate_reply(instructions="Greet the user as Alex, an inventory management assistant. Explain that you can help check inventory quantities, prices, and add items to inventory. Ask what they need help with.")
    logger.info("Inventory voice agent is ready and greeting sent!")

if __name__ == "__main__":
    # Run the agent using LiveKit's CLI
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
