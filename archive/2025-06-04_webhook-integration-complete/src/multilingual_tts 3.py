#!/usr/bin/env python3
"""
Multilingual TTS Wrapper for Arabic-English Support

This module provides a TTS wrapper that automatically selects the appropriate
TTS engine based on the detected language in the text:
- ElevenLabs for Arabic text (better pronunciation)
- Cartesia for English text (faster, good quality)
"""

import asyncio
import logging
import re
import os
from typing import Optional, AsyncIterator
from livekit.agents import tts
from livekit.plugins import cartesia
# import elevenlabs  # Commented out for now due to venv issues

# Configure logging
logger = logging.getLogger(__name__)

class MultilingualTTS(tts.TTS):
    """
    Multilingual TTS that routes to different engines based on language detection.

    - Arabic text -> ElevenLabs (better Arabic pronunciation)
    - English text -> Cartesia (faster, good quality)
    - Mixed text -> Split and route appropriately
    """

    def __init__(
        self,
        *,
        cartesia_api_key: Optional[str] = None,
        elevenlabs_api_key: Optional[str] = None,
        cartesia_model: str = "sonic-english",
        elevenlabs_model: str = "eleven_multilingual_v2",
        elevenlabs_voice: str = "Rachel",
        **kwargs
    ):
        """
        Initialize multilingual TTS with both engines.

        Args:
            cartesia_api_key: Cartesia API key (for English)
            elevenlabs_api_key: ElevenLabs API key (for Arabic)
            cartesia_model: Cartesia model to use
            elevenlabs_model: ElevenLabs model to use
            elevenlabs_voice: ElevenLabs voice to use
        """
        super().__init__()

        # Initialize Cartesia TTS for English
        self._cartesia_tts = cartesia.TTS(
            model=cartesia_model,
            api_key=cartesia_api_key or os.getenv("CARTESIA_API_KEY"),
            **kwargs
        )

        # ElevenLabs configuration (disabled for now)
        # self._elevenlabs_client = elevenlabs.ElevenLabs(
        #     api_key=elevenlabs_api_key or os.getenv("ELEVENLABS_API_KEY")
        # )
        self._elevenlabs_voice = elevenlabs_voice
        self._elevenlabs_model = elevenlabs_model
        self._elevenlabs_enabled = False  # Disabled for now

        # Language detection patterns
        self._arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]')

        logger.info(f"Initialized MultilingualTTS with Cartesia (English) and ElevenLabs (Arabic)")

    def _detect_language(self, text: str) -> str:
        """
        Detect if text contains Arabic characters.

        Args:
            text: Input text to analyze

        Returns:
            'ar' if Arabic detected, 'en' otherwise
        """
        # Remove language markers if present
        clean_text = re.sub(r'\[/?[A-Z]+\]', '', text)

        # Check for Arabic characters
        if self._arabic_pattern.search(clean_text):
            return 'ar'
        return 'en'

    def _split_by_language(self, text: str) -> list:
        """
        Split text into segments by language.

        Args:
            text: Input text with potential language markers

        Returns:
            List of (language, text) tuples
        """
        segments = []

        # Check for language markers first
        marker_pattern = r'\[([A-Z]+)\](.*?)\[/\1\]'
        matches = list(re.finditer(marker_pattern, text))

        if matches:
            # Process text with language markers
            last_end = 0
            for match in matches:
                # Add any text before this marker
                if match.start() > last_end:
                    before_text = text[last_end:match.start()].strip()
                    if before_text:
                        lang = self._detect_language(before_text)
                        segments.append((lang, before_text))

                # Add the marked text
                lang_code = match.group(1).lower()
                marked_text = match.group(2).strip()
                if marked_text:
                    segments.append((lang_code, marked_text))

                last_end = match.end()

            # Add any remaining text
            if last_end < len(text):
                remaining_text = text[last_end:].strip()
                if remaining_text:
                    lang = self._detect_language(remaining_text)
                    segments.append((lang, remaining_text))
        else:
            # No language markers, detect language for entire text
            lang = self._detect_language(text)
            segments.append((lang, text.strip()))

        return segments

    async def _synthesize_with_cartesia(self, text: str) -> AsyncIterator[tts.SynthesizedAudio]:
        """Synthesize English text with Cartesia."""
        logger.info(f"🔊 Using Cartesia for English: '{text[:50]}...'")
        async for audio in self._cartesia_tts.synthesize(text):
            yield audio

    async def _synthesize_with_elevenlabs(self, text: str) -> AsyncIterator[tts.SynthesizedAudio]:
        """Synthesize Arabic text with ElevenLabs (currently disabled)."""
        logger.warning(f"🔊 ElevenLabs disabled, using Cartesia for Arabic: '{text[:50]}...'")

        # For now, just use Cartesia for Arabic too
        # TODO: Enable ElevenLabs when venv issues are resolved
        async for audio in self._cartesia_tts.synthesize(text):
            yield audio

    async def synthesize(self, text: str) -> AsyncIterator[tts.SynthesizedAudio]:
        """
        Synthesize text using appropriate TTS engine based on language.

        Args:
            text: Text to synthesize (may contain language markers)

        Yields:
            SynthesizedAudio chunks
        """
        logger.info(f"🎯 Synthesizing: '{text}'")

        # Split text by language
        segments = self._split_by_language(text)
        logger.info(f"📝 Split into {len(segments)} segments: {[(lang, text[:30]) for lang, text in segments]}")

        for lang, segment_text in segments:
            if not segment_text.strip():
                continue

            if lang == 'ar':
                # Use ElevenLabs for Arabic
                async for audio in self._synthesize_with_elevenlabs(segment_text):
                    yield audio
            else:
                # Use Cartesia for English and other languages
                async for audio in self._synthesize_with_cartesia(segment_text):
                    yield audio

    async def aclose(self):
        """Close TTS resources."""
        if hasattr(self._cartesia_tts, 'aclose'):
            await self._cartesia_tts.aclose()
        # ElevenLabs client doesn't need explicit closing


def create_multilingual_tts(**kwargs) -> MultilingualTTS:
    """
    Create a MultilingualTTS instance with default settings.

    Args:
        **kwargs: Additional arguments passed to MultilingualTTS

    Returns:
        Configured MultilingualTTS instance
    """
    return MultilingualTTS(**kwargs)
