#!/usr/bin/env python3

import asyncio
import logging
import os

from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def prewarm(proc: JobContext):
    """Prewarm function to initialize <PERSON> before starting"""
    logger.info("Prewarming Sophia (Academic Agent)...")
    proc.wait_for_participant = False
    logger.info("Sophia is ready to support your academic journey!")

async def entrypoint(ctx: JobContext):
    """
    Sophia - The Academic Expert
    Specialized in education, research, and learning strategies
    """
    logger.info("Starting Sophia - Your Academic Learning Companion...")

    # Connect to LiveKit room
    await ctx.connect()
    logger.info("Sophia connected to LiveKit room")

    # Import LiveKit plugins
    from livekit.plugins import deepgram, cartesia, silero, openai

    # <PERSON>'s personality and expertise
    sophia_instructions = """You are <PERSON>, an experienced academic advisor and learning specialist.

PERSONALITY:
- Encouraging, knowledgeable, and supportive
- Patient and understanding with learning challenges
- Enthusiastic about education and personal growth
- Warm but professional in your approach

VOICE RULES (CRITICAL):
- Use ONLY plain text in responses
- NO asterisks, markdown, or special characters
- NO bullet points or numbered lists
- Speak naturally and conversationally
- Keep responses encouraging and clear for voice interaction

EXPERTISE AREAS:
- Study techniques and learning strategies
- Research methodology and academic writing
- Time management for students
- Exam preparation and test-taking strategies
- Literature reviews and citation methods
- Thesis and dissertation planning
- Academic goal setting and planning
- Note-taking and information organization
- Critical thinking and analysis skills
- Academic stress management

CONVERSATION STYLE:
- Ask about their specific academic challenges or goals
- Provide practical, implementable study strategies
- Break down complex academic tasks into manageable steps
- Encourage growth mindset and resilience
- Adapt advice to different learning styles
- Celebrate academic achievements and progress

SAMPLE RESPONSES:
- "Let's create a study plan that works with your learning style"
- "I can help you break down that research project into manageable steps"
- "What specific aspect of your studies would you like to improve?"
- "Remember, every expert was once a beginner"

Remember: You're here to empower students and learners to achieve their academic potential through proven strategies and supportive guidance."""

    # Create Sophia agent
    sophia = Agent(instructions=sophia_instructions)

    # Add academic support functions
    @sophia.function()
    async def create_study_schedule(
        subjects: str,
        available_hours_per_day: int,
        exam_dates: str,
        learning_style: str
    ):
        """Create a personalized study schedule"""
        subject_list = [s.strip() for s in subjects.split(",")]
        total_subjects = len(subject_list)
        hours_per_subject = available_hours_per_day // total_subjects if total_subjects > 0 else 0
        
        schedule_advice = f"With {available_hours_per_day} hours daily for {total_subjects} subjects, I recommend {hours_per_subject} hours per subject. "
        
        if learning_style.lower() == "visual":
            schedule_advice += "As a visual learner, include mind maps, diagrams, and color-coding in your study sessions."
        elif learning_style.lower() == "auditory":
            schedule_advice += "As an auditory learner, incorporate reading aloud, discussions, and recorded lectures."
        elif learning_style.lower() == "kinesthetic":
            schedule_advice += "As a kinesthetic learner, include hands-on activities, movement breaks, and practical applications."
        
        return f"{schedule_advice} For your exams on {exam_dates}, let's prioritize subjects based on difficulty and exam proximity."

    @sophia.function()
    async def research_project_plan(
        topic: str,
        deadline: str,
        project_type: str,
        current_progress: str
    ):
        """Help plan and organize a research project"""
        weeks_remaining = "several weeks"  # Simplified for voice interaction
        
        if "just starting" in current_progress.lower():
            next_steps = "Start with a literature review to understand existing research, then narrow your focus to a specific research question."
        elif "research done" in current_progress.lower():
            next_steps = "Begin organizing your findings and creating an outline for your paper structure."
        elif "writing" in current_progress.lower():
            next_steps = "Focus on creating clear arguments, proper citations, and smooth transitions between sections."
        else:
            next_steps = "Let's assess where you are and create specific next steps."
            
        return f"For your {project_type} on {topic} due {deadline}, here's what I recommend: {next_steps} Break this into daily tasks to avoid last-minute stress."

    @sophia.function()
    async def exam_preparation_strategy(
        subject: str,
        exam_type: str,
        time_until_exam: str,
        difficulty_areas: str
    ):
        """Create an exam preparation strategy"""
        if "multiple choice" in exam_type.lower():
            strategy = "Focus on understanding concepts rather than memorization. Practice with sample questions and eliminate wrong answers systematically."
        elif "essay" in exam_type.lower():
            strategy = "Create outlines for potential topics, practice timed writing, and prepare key examples and evidence."
        elif "practical" in exam_type.lower():
            strategy = "Focus on hands-on practice, review procedures step-by-step, and simulate exam conditions."
        else:
            strategy = "Use active recall techniques, spaced repetition, and practice under timed conditions."
            
        return f"For your {exam_type} in {subject} happening {time_until_exam}, I recommend: {strategy} Pay special attention to {difficulty_areas} - let's create targeted practice for these areas."

    # Create agent session with Sophia's voice
    session = AgentSession(
        vad=silero.VAD.load(),
        stt=deepgram.STT(model="nova-2-general", language="en"),
        llm=openai.LLM(model="gpt-4o-mini", temperature=0.3),  # Slightly higher temperature for creative learning approaches
        tts=cartesia.TTS(
            model="sonic-english",
            voice=os.getenv("SOPHIA_VOICE_ID", "79a125e8-cd45-4c13-8a67-188112f4dd22"),  # Warm female voice
            speed=1.0,
            emotion=["warmth:high", "encouragement:high"]
        ),
    )

    # Start the session
    await session.start(agent=sophia, room=ctx.room)
    logger.info("Sophia is now active and ready to support academic success!")

    # Send Sophia's greeting
    await session.generate_reply(
        instructions="Introduce yourself as Sophia, an academic learning specialist. Ask how you can help with their studies or learning goals today. Be warm and encouraging. Use only plain text."
    )

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
