#!/usr/bin/env python3

import asyncio
import logging
import os
import requests
import json
from typing import Optional

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DifyIntegration:
    """
    Simple integration with Dify API for processing voice agent requests
    """
    
    def __init__(self):
        # Dify API configuration
        self.dify_api_url = os.getenv("DIFY_API_URL", "http://localhost:5001/v1/chat-messages")
        self.dify_api_key = os.getenv("DIFY_API_KEY", "your-dify-api-key")
        self.conversation_id = ""  # Will be set when conversation starts
        
        # Headers for Dify API
        self.headers = {
            "Authorization": f"Bearer {self.dify_api_key}",
            "Content-Type": "application/json"
        }
        
        logger.info("Dify integration initialized")
    
    async def process_message(self, user_message: str, user_id: str = "livekit-agent") -> str:
        """
        Process a user message through Dify and return the response
        
        Args:
            user_message: The message from the user
            user_id: Identifier for the user (optional)
            
        Returns:
            Response from Dify
        """
        try:
            logger.info(f"Processing message through Dify: {user_message}")
            
            # Prepare the payload for Dify
            payload = {
                "inputs": {},
                "query": user_message,
                "response_mode": "blocking",
                "conversation_id": self.conversation_id,
                "user": user_id
            }
            
            # Make the API call to Dify
            response = requests.post(
                self.dify_api_url, 
                headers=self.headers, 
                json=payload, 
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # Update conversation ID for future messages
                if "conversation_id" in result:
                    self.conversation_id = result["conversation_id"]
                
                # Extract the response
                dify_response = result.get("answer", "I processed your request.")
                
                logger.info(f"Dify response: {dify_response}")
                
                # Check if this triggered any N8n workflows
                self._check_workflow_triggers(result)
                
                return dify_response
            else:
                logger.error(f"Dify API error: {response.status_code} - {response.text}")
                return "I'm having trouble processing your request right now. Please try again."
                
        except Exception as e:
            logger.error(f"Error calling Dify API: {e}")
            return "I encountered an error while processing your request."
    
    def _check_workflow_triggers(self, dify_result: dict):
        """
        Check if the Dify response triggered any N8n workflows
        """
        # This is where you can add logic to detect if Dify triggered N8n workflows
        # For example, check for specific keywords or metadata in the response
        
        if "workflow_triggered" in dify_result:
            workflow_id = dify_result.get("workflow_id")
            logger.info(f"N8n workflow triggered: {workflow_id}")
        
        # You could also check for project creation keywords
        message = dify_result.get("answer", "").lower()
        if any(keyword in message for keyword in ["project created", "workflow started", "task assigned"]):
            logger.info("Detected project management activity")
    
    async def test_connection(self) -> bool:
        """
        Test the connection to Dify
        """
        try:
            test_message = "Hello, this is a test message."
            response = await self.process_message(test_message)
            return "error" not in response.lower()
        except Exception as e:
            logger.error(f"Dify connection test failed: {e}")
            return False

# Example usage and testing
async def main():
    """
    Test the Dify integration
    """
    dify = DifyIntegration()
    
    # Test connection
    logger.info("Testing Dify connection...")
    if await dify.test_connection():
        logger.info("✅ Dify connection successful!")
    else:
        logger.error("❌ Dify connection failed!")
        return
    
    # Test project creation request
    test_messages = [
        "Hello, I need to create a new project",
        "Create a project called 'Voice AI Integration'",
        "Set up a workflow for project management",
        "What's the status of my projects?"
    ]
    
    for message in test_messages:
        logger.info(f"\n--- Testing message: {message} ---")
        response = await dify.process_message(message)
        logger.info(f"Response: {response}")

if __name__ == "__main__":
    asyncio.run(main())
