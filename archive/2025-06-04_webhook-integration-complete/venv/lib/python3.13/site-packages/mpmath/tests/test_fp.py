"""
Easy-to-use test-generating code:

cases = '''
exp 2.25
log 2.25
'''

from mpmath import *
mp.dps = 20
for test in cases.splitlines():
    if not test:
        continue
    words = test.split()
    fname = words[0]
    args = words[1:]
    argstr = ", ".join(args)
    testline = "%s(%s)" % (fname, argstr)
    ans = str(eval(testline))
    print "    assert ae(fp.%s, %s)" % (testline, ans)

"""

from mpmath import fp

def ae(x, y, tol=1e-12):
    if x == y:
        return True
    return abs(x-y) <= tol*abs(y)

def test_conj():
    assert fp.conj(4) == 4
    assert fp.conj(3+4j) == 3-4j
    assert fp.fdot([1,2],[3,2+1j], conjugate=True) == 7-2j

def test_fp_number_parts():
    assert ae(fp.arg(3), 0.0)
    assert ae(fp.arg(-3), 3.1415926535897932385)
    assert ae(fp.arg(3j), 1.5707963267948966192)
    assert ae(fp.arg(-3j), -1.5707963267948966192)
    assert ae(fp.arg(2+3j), 0.98279372324732906799)
    assert ae(fp.arg(-1-1j), -2.3561944901923449288)
    assert ae(fp.re(2.5), 2.5)
    assert ae(fp.re(2.5+3j), 2.5)
    assert ae(fp.im(2.5), 0.0)
    assert ae(fp.im(2.5+3j), 3.0)
    assert ae(fp.floor(2.5), 2.0)
    assert ae(fp.floor(2), 2.0)
    assert ae(fp.floor(2.0+0j), (2.0 + 0.0j))
    assert ae(fp.floor(-1.5-0.5j), (-2.0 - 1.0j))
    assert ae(fp.ceil(2.5), 3.0)
    assert ae(fp.ceil(2), 2.0)
    assert ae(fp.ceil(2.0+0j), (2.0 + 0.0j))
    assert ae(fp.ceil(-1.5-0.5j), (-1.0 + 0.0j))

def test_fp_cospi_sinpi():
    assert ae(fp.sinpi(0), 0.0)
    assert ae(fp.sinpi(0.25), 0.7071067811865475244)
    assert ae(fp.sinpi(0.5), 1.0)
    assert ae(fp.sinpi(0.75), 0.7071067811865475244)
    assert ae(fp.sinpi(1), 0.0)
    assert ae(fp.sinpi(1.25), -0.7071067811865475244)
    assert ae(fp.sinpi(1.5), -1.0)
    assert ae(fp.sinpi(1.75), -0.7071067811865475244)
    assert ae(fp.sinpi(2), 0.0)
    assert ae(fp.sinpi(2.25), 0.7071067811865475244)
    assert ae(fp.sinpi(0+3j), (0.0 + 6195.8238636085899556j))
    assert ae(fp.sinpi(0.25+3j), (4381.1091260582448033 + 4381.1090689950686908j))
    assert ae(fp.sinpi(0.5+3j), (6195.8239443081075259 + 0.0j))
    assert ae(fp.sinpi(0.75+3j), (4381.1091260582448033 - 4381.1090689950686908j))
    assert ae(fp.sinpi(1+3j), (0.0 - 6195.8238636085899556j))
    assert ae(fp.sinpi(1.25+3j), (-4381.1091260582448033 - 4381.1090689950686908j))
    assert ae(fp.sinpi(1.5+3j), (-6195.8239443081075259 + 0.0j))
    assert ae(fp.sinpi(1.75+3j), (-4381.1091260582448033 + 4381.1090689950686908j))
    assert ae(fp.sinpi(2+3j), (0.0 + 6195.8238636085899556j))
    assert ae(fp.sinpi(2.25+3j), (4381.1091260582448033 + 4381.1090689950686908j))
    assert ae(fp.sinpi(-0.75), -0.7071067811865475244)
    assert ae(fp.sinpi(-1e-10), -3.1415926535897933529e-10)
    assert ae(fp.sinpi(1e-10), 3.1415926535897933529e-10)
    assert ae(fp.sinpi(1e-10+1e-10j), (3.141592653589793353e-10 + 3.1415926535897933528e-10j))
    assert ae(fp.sinpi(1e-10-1e-10j), (3.141592653589793353e-10 - 3.1415926535897933528e-10j))
    assert ae(fp.sinpi(-1e-10+1e-10j), (-3.141592653589793353e-10 + 3.1415926535897933528e-10j))
    assert ae(fp.sinpi(-1e-10-1e-10j), (-3.141592653589793353e-10 - 3.1415926535897933528e-10j))
    assert ae(fp.cospi(0), 1.0)
    assert ae(fp.cospi(0.25), 0.7071067811865475244)
    assert ae(fp.cospi(0.5), 0.0)
    assert ae(fp.cospi(0.75), -0.7071067811865475244)
    assert ae(fp.cospi(1), -1.0)
    assert ae(fp.cospi(1.25), -0.7071067811865475244)
    assert ae(fp.cospi(1.5), 0.0)
    assert ae(fp.cospi(1.75), 0.7071067811865475244)
    assert ae(fp.cospi(2), 1.0)
    assert ae(fp.cospi(2.25), 0.7071067811865475244)
    assert ae(fp.cospi(0+3j), (6195.8239443081075259 + 0.0j))
    assert ae(fp.cospi(0.25+3j), (4381.1091260582448033 - 4381.1090689950686908j))
    assert ae(fp.cospi(0.5+3j), (0.0 - 6195.8238636085899556j))
    assert ae(fp.cospi(0.75+3j), (-4381.1091260582448033 - 4381.1090689950686908j))
    assert ae(fp.cospi(1+3j), (-6195.8239443081075259 + 0.0j))
    assert ae(fp.cospi(1.25+3j), (-4381.1091260582448033 + 4381.1090689950686908j))
    assert ae(fp.cospi(1.5+3j), (0.0 + 6195.8238636085899556j))
    assert ae(fp.cospi(1.75+3j), (4381.1091260582448033 + 4381.1090689950686908j))
    assert ae(fp.cospi(2+3j), (6195.8239443081075259 + 0.0j))
    assert ae(fp.cospi(2.25+3j), (4381.1091260582448033 - 4381.1090689950686908j))
    assert ae(fp.cospi(-0.75), -0.7071067811865475244)
    assert ae(fp.sinpi(-0.7), -0.80901699437494750611)
    assert ae(fp.cospi(-0.7), -0.5877852522924730163)
    assert ae(fp.cospi(-3+2j), (-267.74676148374822225 + 0.0j))
    assert ae(fp.sinpi(-3+2j), (0.0 - 267.74489404101651426j))
    assert ae(fp.sinpi(-0.7+2j), (-216.6116802292079471 - 157.37650009392034693j))
    assert ae(fp.cospi(-0.7+2j), (-157.37759774921754565 + 216.61016943630197336j))

def test_fp_expj():
    assert ae(fp.expj(0), (1.0 + 0.0j))
    assert ae(fp.expj(1), (0.5403023058681397174 + 0.84147098480789650665j))
    assert ae(fp.expj(2), (-0.416146836547142387 + 0.9092974268256816954j))
    assert ae(fp.expj(0.75), (0.73168886887382088631 + 0.68163876002333416673j))
    assert ae(fp.expj(2+3j), (-0.020718731002242879378 + 0.045271253156092975488j))
    assert ae(fp.expjpi(0), (1.0 + 0.0j))
    assert ae(fp.expjpi(1), (-1.0 + 0.0j))
    assert ae(fp.expjpi(2), (1.0 + 0.0j))
    assert ae(fp.expjpi(0.75), (-0.7071067811865475244 + 0.7071067811865475244j))
    assert ae(fp.expjpi(2+3j), (0.000080699517570304599239 + 0.0j))

def test_fp_bernoulli():
    assert ae(fp.bernoulli(0), 1.0)
    assert ae(fp.bernoulli(1), -0.5)
    assert ae(fp.bernoulli(2), 0.16666666666666666667)
    assert ae(fp.bernoulli(10), 0.075757575757575757576)
    assert ae(fp.bernoulli(11), 0.0)

def test_fp_gamma():
    assert ae(fp.gamma(1), 1.0)
    assert ae(fp.gamma(1.5), 0.88622692545275801365)
    assert ae(fp.gamma(10), 362880.0)
    assert ae(fp.gamma(-0.5), -3.5449077018110320546)
    assert ae(fp.gamma(-7.1), 0.0016478244570263333622)
    assert ae(fp.gamma(12.3), 83385367.899970000963)
    assert ae(fp.gamma(2+0j), (1.0 + 0.0j))
    assert ae(fp.gamma(-2.5+0j), (-0.94530872048294188123 + 0.0j))
    assert ae(fp.gamma(3+4j), (0.0052255384713692141947 - 0.17254707929430018772j))
    assert ae(fp.gamma(-3-4j), (0.00001460997305874775607 - 0.000020760733311509070396j))
    assert ae(fp.fac(0), 1.0)
    assert ae(fp.fac(1), 1.0)
    assert ae(fp.fac(20), 2432902008176640000.0)
    assert ae(fp.fac(-3.5), -0.94530872048294188123)
    assert ae(fp.fac(2+3j), (-0.44011340763700171113 - 0.06363724312631702183j))
    assert ae(fp.loggamma(1.0), 0.0)
    assert ae(fp.loggamma(2.0), 0.0)
    assert ae(fp.loggamma(3.0), 0.69314718055994530942)
    assert ae(fp.loggamma(7.25), 7.0521854507385394449)
    assert ae(fp.loggamma(1000.0), 5905.2204232091812118)
    assert ae(fp.loggamma(1e50), 1.1412925464970229298e+52)
    assert ae(fp.loggamma(1e25+1e25j), (5.6125802751733671621e+26 + 5.7696599078528568383e+26j))
    assert ae(fp.loggamma(3+4j), (-1.7566267846037841105 + 4.7426644380346579282j))
    assert ae(fp.loggamma(-0.5), (1.2655121234846453965 - 3.1415926535897932385j))
    assert ae(fp.loggamma(-1.25), (1.3664317612369762346 - 6.2831853071795864769j))
    assert ae(fp.loggamma(-2.75), (0.0044878975359557733115 - 9.4247779607693797154j))
    assert ae(fp.loggamma(-3.5), (-1.3090066849930420464 - 12.566370614359172954j))
    assert ae(fp.loggamma(-4.5), (-2.8130840817693161197 - 15.707963267948966192j))
    assert ae(fp.loggamma(-2+3j), (-6.776523813485657093 - 4.568791367260286402j))
    assert ae(fp.loggamma(-1000.3), (-5912.8440347785205041 - 3144.7342462433830317j))
    assert ae(fp.loggamma(-100-100j), (-632.35117666833135562 - 158.37641469650352462j))
    assert ae(fp.loggamma(1e-10), 23.025850929882735237)
    assert ae(fp.loggamma(-1e-10), (23.02585092999817837 - 3.1415926535897932385j))
    assert ae(fp.loggamma(1e-10j), (23.025850929940456804 - 1.5707963268526181857j))
    assert ae(fp.loggamma(1e-10j-1e-10), (22.679277339718205716 - 2.3561944902500664954j))

def test_fp_psi():
    assert ae(fp.psi(0, 3.7), 1.1671535393615114409)
    assert ae(fp.psi(0, 0.5), -1.9635100260214234794)
    assert ae(fp.psi(0, 1), -0.57721566490153286061)
    assert ae(fp.psi(0, -2.5), 1.1031566406452431872)
    assert ae(fp.psi(0, 12.9), 2.5179671503279156347)
    assert ae(fp.psi(0, 100), 4.6001618527380874002)
    assert ae(fp.psi(0, 2500.3), 7.8239660143238547877)
    assert ae(fp.psi(0, 1e40), 92.103403719761827391)
    assert ae(fp.psi(0, 1e200), 460.51701859880913677)
    assert ae(fp.psi(0, 3.7+0j), (1.1671535393615114409 + 0.0j))
    assert ae(fp.psi(1, 3), 0.39493406684822643647)
    assert ae(fp.psi(3, 2+3j), (-0.05383196209159972116 + 0.0076890935247364805218j))
    assert ae(fp.psi(4, -0.5+1j), (1.2719531355492328195 - 18.211833410936276774j))
    assert ae(fp.harmonic(0), 0.0)
    assert ae(fp.harmonic(1), 1.0)
    assert ae(fp.harmonic(2), 1.5)
    assert ae(fp.harmonic(100), 5.1873775176396202608)
    assert ae(fp.harmonic(-2.5), 1.2803723055467760478)
    assert ae(fp.harmonic(2+3j), (1.9390425294578375875 + 0.87336044981834544043j))
    assert ae(fp.harmonic(-5-4j), (2.3725754822349437733 - 2.4160904444801621j))

def test_fp_zeta():
    assert ae(fp.zeta(1e100), 1.0)
    assert ae(fp.zeta(3), 1.2020569031595942854)
    assert ae(fp.zeta(2+0j), (1.6449340668482264365 + 0.0j))
    assert ae(fp.zeta(0.93), -13.713619351638164784)
    assert ae(fp.zeta(1.74), 1.9796863545771774095)
    assert ae(fp.zeta(0.0), -0.5)
    assert ae(fp.zeta(-1.0), -0.083333333333333333333)
    assert ae(fp.zeta(-2.0), 0.0)
    assert ae(fp.zeta(-3.0), 0.0083333333333333333333)
    assert ae(fp.zeta(-500.0), 0.0)
    assert ae(fp.zeta(-7.4), 0.0036537321227995882447)
    assert ae(fp.zeta(2.1), 1.5602165335033620158)
    assert ae(fp.zeta(26.9), 1.0000000079854809935)
    assert ae(fp.zeta(26), 1.0000000149015548284)
    assert ae(fp.zeta(27), 1.0000000074507117898)
    assert ae(fp.zeta(28), 1.0000000037253340248)
    assert ae(fp.zeta(27.1), 1.000000006951755045)
    assert ae(fp.zeta(32.7), 1.0000000001433243232)
    assert ae(fp.zeta(100), 1.0)
    assert ae(fp.altzeta(3.5), 0.92755357777394803511)
    assert ae(fp.altzeta(1), 0.69314718055994530942)
    assert ae(fp.altzeta(2), 0.82246703342411321824)
    assert ae(fp.altzeta(0), 0.5)
    assert ae(fp.zeta(-2+3j, 1), (0.13297115587929864827 + 0.12305330040458776494j))
    assert ae(fp.zeta(-2+3j, 5), (18.384866151867576927 - 11.377015110597711009j))
    assert ae(fp.zeta(1.0000000001), 9999999173.1735741337)
    assert ae(fp.zeta(0.9999999999), -9999999172.0191428039)
    assert ae(fp.zeta(1+0.000000001j), (0.57721566490153286061 - 999999999.99999993765j))
    assert ae(fp.primezeta(2.5+4j), (-0.16922458243438033385 - 0.010847965298387727811j))
    assert ae(fp.primezeta(4), 0.076993139764246844943)
    assert ae(fp.riemannr(3.7), 2.3034079839110855717)
    assert ae(fp.riemannr(8), 3.9011860449341499474)
    assert ae(fp.riemannr(3+4j), (2.2369653314259991796 + 1.6339943856990281694j))

def test_fp_hyp2f1():
    assert ae(fp.hyp2f1(1, (3,2), 3.25, 5.0), (-0.46600275923108143059 - 0.74393667908854842325j))
    assert ae(fp.hyp2f1(1+1j, (3,2), 3.25, 5.0), (-5.9208875603806515987 - 2.3813557707889590686j))
    assert ae(fp.hyp2f1(1+1j, (3,2), 3.25, 2+3j), (0.17174552030925080445 + 0.19589781970539389999j))

def test_fp_erf():
    assert fp.erf(2) == fp.erf(2.0) == fp.erf(2.0+0.0j)
    assert fp.erf(fp.inf) == 1.0
    assert fp.erf(fp.ninf) == -1.0
    assert ae(fp.erf(0), 0.0)
    assert ae(fp.erf(-0), -0.0)
    assert ae(fp.erf(0.3), 0.32862675945912741619)
    assert ae(fp.erf(-0.3), -0.32862675945912741619)
    assert ae(fp.erf(0.9), 0.79690821242283213966)
    assert ae(fp.erf(-0.9), -0.79690821242283213966)
    assert ae(fp.erf(1.0), 0.84270079294971486934)
    assert ae(fp.erf(-1.0), -0.84270079294971486934)
    assert ae(fp.erf(1.1), 0.88020506957408172966)
    assert ae(fp.erf(-1.1), -0.88020506957408172966)
    assert ae(fp.erf(8.5), 1.0)
    assert ae(fp.erf(-8.5), -1.0)
    assert ae(fp.erf(9.1), 1.0)
    assert ae(fp.erf(-9.1), -1.0)
    assert ae(fp.erf(20.0), 1.0)
    assert ae(fp.erf(-20.0), -1.0)
    assert ae(fp.erf(10000.0), 1.0)
    assert ae(fp.erf(-10000.0), -1.0)
    assert ae(fp.erf(1e+50), 1.0)
    assert ae(fp.erf(-1e+50), -1.0)
    assert ae(fp.erf(1j), 1.650425758797542876j)
    assert ae(fp.erf(-1j), -1.650425758797542876j)
    assert ae(fp.erf((2+3j)), (-20.829461427614568389 + 8.6873182714701631444j))
    assert ae(fp.erf(-(2+3j)), -(-20.829461427614568389 + 8.6873182714701631444j))
    assert ae(fp.erf((8+9j)), (-1072004.2525062051158 + 364149.91954310255423j))
    assert ae(fp.erf(-(8+9j)), -(-1072004.2525062051158 + 364149.91954310255423j))
    assert fp.erfc(fp.inf) == 0.0
    assert fp.erfc(fp.ninf) == 2.0
    assert fp.erfc(0) == 1
    assert fp.erfc(-0.0) == 1
    assert fp.erfc(0+0j) == 1
    assert ae(fp.erfc(0.3), 0.67137324054087258381)
    assert ae(fp.erfc(-0.3), 1.3286267594591274162)
    assert ae(fp.erfc(0.9), 0.20309178757716786034)
    assert ae(fp.erfc(-0.9), 1.7969082124228321397)
    assert ae(fp.erfc(1.0), 0.15729920705028513066)
    assert ae(fp.erfc(-1.0), 1.8427007929497148693)
    assert ae(fp.erfc(1.1), 0.11979493042591827034)
    assert ae(fp.erfc(-1.1), 1.8802050695740817297)
    assert ae(fp.erfc(8.5), 2.7623240713337714461e-33)
    assert ae(fp.erfc(-8.5), 2.0)
    assert ae(fp.erfc(9.1), 6.6969004279886077452e-38)
    assert ae(fp.erfc(-9.1), 2.0)
    assert ae(fp.erfc(20.0), 5.3958656116079009289e-176)
    assert ae(fp.erfc(-20.0), 2.0)
    assert ae(fp.erfc(10000.0), 0.0)
    assert ae(fp.erfc(-10000.0), 2.0)
    assert ae(fp.erfc(1e+50), 0.0)
    assert ae(fp.erfc(-1e+50), 2.0)
    assert ae(fp.erfc(1j), (1.0 - 1.650425758797542876j))
    assert ae(fp.erfc(-1j), (1.0 + 1.650425758797542876j))
    assert ae(fp.erfc((2+3j)), (21.829461427614568389 - 8.6873182714701631444j), 1e-13)
    assert ae(fp.erfc(-(2+3j)), (-19.829461427614568389 + 8.6873182714701631444j), 1e-13)
    assert ae(fp.erfc((8+9j)), (1072005.2525062051158 - 364149.91954310255423j))
    assert ae(fp.erfc(-(8+9j)), (-1072003.2525062051158 + 364149.91954310255423j))
    assert ae(fp.erfc(20+0j), (5.3958656116079009289e-176 + 0.0j))

def test_fp_lambertw():
    assert ae(fp.lambertw(0.0), 0.0)
    assert ae(fp.lambertw(1.0), 0.567143290409783873)
    assert ae(fp.lambertw(7.5), 1.5662309537823875394)
    assert ae(fp.lambertw(-0.25), -0.35740295618138890307)
    assert ae(fp.lambertw(-10.0), (1.3699809685212708156 + 2.140194527074713196j))
    assert ae(fp.lambertw(0+0j), (0.0 + 0.0j))
    assert ae(fp.lambertw(4+0j), (1.2021678731970429392 + 0.0j))
    assert ae(fp.lambertw(1000.5), 5.2500227450408980127)
    assert ae(fp.lambertw(1e100), 224.84310644511850156)
    assert ae(fp.lambertw(-1000.0), (5.1501630246362515223 + 2.6641981432905204596j))
    assert ae(fp.lambertw(1e-10), 9.9999999990000003645e-11)
    assert ae(fp.lambertw(1e-10j), (1.0000000000000000728e-20 + 1.0000000000000000364e-10j))
    assert ae(fp.lambertw(3+4j), (1.2815618061237758782 + 0.53309522202097107131j))
    assert ae(fp.lambertw(-3-4j), (1.0750730665692549276 - 1.3251023817343588823j))
    assert ae(fp.lambertw(10000+1000j), (7.2361526563371602186 + 0.087567810943839352034j))
    assert ae(fp.lambertw(0.0, -1), -fp.inf)
    assert ae(fp.lambertw(1.0, -1), (-1.5339133197935745079 - 4.3751851530618983855j))
    assert ae(fp.lambertw(7.5, -1), (0.44125668415098614999 - 4.8039842008452390179j))
    assert ae(fp.lambertw(-0.25, -1), -2.1532923641103496492)
    assert ae(fp.lambertw(-10.0, -1), (1.3699809685212708156 - 2.140194527074713196j))
    assert ae(fp.lambertw(0+0j, -1), -fp.inf)
    assert ae(fp.lambertw(4+0j, -1), (-0.15730793189620765317 - 4.6787800704666656212j))
    assert ae(fp.lambertw(1000.5, -1), (4.9153765415404024736 - 5.4465682700815159569j))
    assert ae(fp.lambertw(1e100, -1), (224.84272130101601052 - 6.2553713838167244141j))
    assert ae(fp.lambertw(-1000.0, -1), (5.1501630246362515223 - 2.6641981432905204596j))
    assert ae(fp.lambertw(1e-10, -1), (-26.303186778379041521 - 3.2650939117038283975j))
    assert ae(fp.lambertw(1e-10j, -1), (-26.297238779529035028 - 1.6328071613455765135j))
    assert ae(fp.lambertw(3+4j, -1), (0.25856740686699741676 - 3.8521166861614355895j))
    assert ae(fp.lambertw(-3-4j, -1), (-0.32028750204310768396 - 6.8801677192091972343j))
    assert ae(fp.lambertw(10000+1000j, -1), (7.0255308742285435567 - 5.5177506835734067601j))
    assert ae(fp.lambertw(0.0, 2), -fp.inf)
    assert ae(fp.lambertw(1.0, 2), (-2.4015851048680028842 + 10.776299516115070898j))
    assert ae(fp.lambertw(7.5, 2), (-0.38003357962843791529 + 10.960916473368746184j))
    assert ae(fp.lambertw(-0.25, 2), (-4.0558735269061511898 + 13.852334658567271386j))
    assert ae(fp.lambertw(-10.0, 2), (-0.34479123764318858696 + 14.112740596763592363j))
    assert ae(fp.lambertw(0+0j, 2), -fp.inf)
    assert ae(fp.lambertw(4+0j, 2), (-1.0070343323804262788 + 10.903476551861683082j))
    assert ae(fp.lambertw(1000.5, 2), (4.4076185165459395295 + 11.365524591091402177j))
    assert ae(fp.lambertw(1e100, 2), (224.84156762724875878 + 12.510785262632255672j))
    assert ae(fp.lambertw(-1000.0, 2), (4.1984245610246530756 + 14.420478573754313845j))
    assert ae(fp.lambertw(1e-10, 2), (-26.362258095445866488 + 9.7800247407031482519j))
    assert ae(fp.lambertw(1e-10j, 2), (-26.384250801683084252 + 11.403535950607739763j))
    assert ae(fp.lambertw(3+4j, 2), (-0.86554679943333993562 + 11.849956798331992027j))
    assert ae(fp.lambertw(-3-4j, 2), (-0.55792273874679112639 + 8.7173627024159324811j))
    assert ae(fp.lambertw(10000+1000j, 2), (6.6223802254585662734 + 11.61348646825020766j))

def test_fp_stress_ei_e1():
    # Can be tightened on recent Pythons with more accurate math/cmath
    ATOL = 1e-13
    PTOL = 1e-12
    v = fp.e1(1.1641532182693481445e-10)
    assert ae(v, 22.296641293693077672, tol=ATOL)
    assert type(v) is float
    v = fp.e1(0.25)
    assert ae(v, 1.0442826344437381945, tol=ATOL)
    assert type(v) is float
    v = fp.e1(1.0)
    assert ae(v, 0.21938393439552027368, tol=ATOL)
    assert type(v) is float
    v = fp.e1(2.0)
    assert ae(v, 0.048900510708061119567, tol=ATOL)
    assert type(v) is float
    v = fp.e1(5.0)
    assert ae(v, 0.0011482955912753257973, tol=ATOL)
    assert type(v) is float
    v = fp.e1(20.0)
    assert ae(v, 9.8355252906498816904e-11, tol=ATOL)
    assert type(v) is float
    v = fp.e1(30.0)
    assert ae(v, 3.0215520106888125448e-15, tol=ATOL)
    assert type(v) is float
    v = fp.e1(40.0)
    assert ae(v, 1.0367732614516569722e-19, tol=ATOL)
    assert type(v) is float
    v = fp.e1(50.0)
    assert ae(v, 3.7832640295504590187e-24, tol=ATOL)
    assert type(v) is float
    v = fp.e1(80.0)
    assert ae(v, 2.2285432586884729112e-37, tol=ATOL)
    assert type(v) is float
    v = fp.e1((1.1641532182693481445e-10 + 0.0j))
    assert ae(v, (22.296641293693077672 + 0.0j), tol=ATOL)
    assert ae(v.real, 22.296641293693077672, tol=PTOL)
    assert v.imag == 0
    v = fp.e1((0.25 + 0.0j))
    assert ae(v, (1.0442826344437381945 + 0.0j), tol=ATOL)
    assert ae(v.real, 1.0442826344437381945, tol=PTOL)
    assert v.imag == 0
    v = fp.e1((1.0 + 0.0j))
    assert ae(v, (0.21938393439552027368 + 0.0j), tol=ATOL)
    assert ae(v.real, 0.21938393439552027368, tol=PTOL)
    assert v.imag == 0
    v = fp.e1((2.0 + 0.0j))
    assert ae(v, (0.048900510708061119567 + 0.0j), tol=ATOL)
    assert ae(v.real, 0.048900510708061119567, tol=PTOL)
    assert v.imag == 0
    v = fp.e1((5.0 + 0.0j))
    assert ae(v, (0.0011482955912753257973 + 0.0j), tol=ATOL)
    assert ae(v.real, 0.0011482955912753257973, tol=PTOL)
    assert v.imag == 0
    v = fp.e1((20.0 + 0.0j))
    assert ae(v, (9.8355252906498816904e-11 + 0.0j), tol=ATOL)
    assert ae(v.real, 9.8355252906498816904e-11, tol=PTOL)
    assert v.imag == 0
    v = fp.e1((30.0 + 0.0j))
    assert ae(v, (3.0215520106888125448e-15 + 0.0j), tol=ATOL)
    assert ae(v.real, 3.0215520106888125448e-15, tol=PTOL)
    assert v.imag == 0
    v = fp.e1((40.0 + 0.0j))
    assert ae(v, (1.0367732614516569722e-19 + 0.0j), tol=ATOL)
    assert ae(v.real, 1.0367732614516569722e-19, tol=PTOL)
    assert v.imag == 0
    v = fp.e1((50.0 + 0.0j))
    assert ae(v, (3.7832640295504590187e-24 + 0.0j), tol=ATOL)
    assert ae(v.real, 3.7832640295504590187e-24, tol=PTOL)
    assert v.imag == 0
    v = fp.e1((80.0 + 0.0j))
    assert ae(v, (2.2285432586884729112e-37 + 0.0j), tol=ATOL)
    assert ae(v.real, 2.2285432586884729112e-37, tol=PTOL)
    assert v.imag == 0
    v = fp.e1((4.6566128730773925781e-10 + 1.1641532182693481445e-10j))
    assert ae(v, (20.880034622014215597 - 0.24497866301044883237j), tol=ATOL)
    assert ae(v.real, 20.880034622014215597, tol=PTOL)
    assert ae(v.imag, -0.24497866301044883237, tol=PTOL)
    v = fp.e1((1.0 + 0.25j))
    assert ae(v, (0.19731063945004229095 - 0.087366045774299963672j), tol=ATOL)
    assert ae(v.real, 0.19731063945004229095, tol=PTOL)
    assert ae(v.imag, -0.087366045774299963672, tol=PTOL)
    v = fp.e1((4.0 + 1.0j))
    assert ae(v, (0.0013106173980145506944 - 0.0034542480199350626699j), tol=ATOL)
    assert ae(v.real, 0.0013106173980145506944, tol=PTOL)
    assert ae(v.imag, -0.0034542480199350626699, tol=PTOL)
    v = fp.e1((8.0 + 2.0j))
    assert ae(v, (-0.000022278049065270225945 - 0.000029191940456521555288j), tol=ATOL)
    assert ae(v.real, -0.000022278049065270225945, tol=PTOL)
    assert ae(v.imag, -0.000029191940456521555288, tol=PTOL)
    v = fp.e1((20.0 + 5.0j))
    assert ae(v, (4.7711374515765346894e-11 + 8.2902652405126947359e-11j), tol=ATOL)
    assert ae(v.real, 4.7711374515765346894e-11, tol=PTOL)
    assert ae(v.imag, 8.2902652405126947359e-11, tol=PTOL)
    v = fp.e1((80.0 + 20.0j))
    assert ae(v, (3.8353473865788235787e-38 - 2.129247592349605139e-37j), tol=ATOL)
    assert ae(v.real, 3.8353473865788235787e-38, tol=PTOL)
    assert ae(v.imag, -2.129247592349605139e-37, tol=PTOL)
    v = fp.e1((120.0 + 30.0j))
    assert ae(v, (2.3836002337480334716e-55 + 5.6704043587126198306e-55j), tol=ATOL)
    assert ae(v.real, 2.3836002337480334716e-55, tol=PTOL)
    assert ae(v.imag, 5.6704043587126198306e-55, tol=PTOL)
    v = fp.e1((160.0 + 40.0j))
    assert ae(v, (-1.6238022898654510661e-72 - 1.104172355572287367e-72j), tol=ATOL)
    assert ae(v.real, -1.6238022898654510661e-72, tol=PTOL)
    assert ae(v.imag, -1.104172355572287367e-72, tol=PTOL)
    v = fp.e1((200.0 + 50.0j))
    assert ae(v, (6.6800061461666228487e-90 + 1.4473816083541016115e-91j), tol=ATOL)
    assert ae(v.real, 6.6800061461666228487e-90, tol=PTOL)
    assert ae(v.imag, 1.4473816083541016115e-91, tol=PTOL)
    v = fp.e1((320.0 + 80.0j))
    assert ae(v, (4.2737871527778786157e-143 + 3.1789935525785660314e-142j), tol=ATOL)
    assert ae(v.real, 4.2737871527778786157e-143, tol=PTOL)
    assert ae(v.imag, 3.1789935525785660314e-142, tol=PTOL)
    v = fp.e1((1.1641532182693481445e-10 + 1.1641532182693481445e-10j))
    assert ae(v, (21.950067703413105017 - 0.7853981632810329878j), tol=ATOL)
    assert ae(v.real, 21.950067703413105017, tol=PTOL)
    assert ae(v.imag, -0.7853981632810329878, tol=PTOL)
    v = fp.e1((0.25 + 0.25j))
    assert ae(v, (0.71092525792923287894 - 0.56491812441304194711j), tol=ATOL)
    assert ae(v.real, 0.71092525792923287894, tol=PTOL)
    assert ae(v.imag, -0.56491812441304194711, tol=PTOL)
    v = fp.e1((1.0 + 1.0j))
    assert ae(v, (0.00028162445198141832551 - 0.17932453503935894015j), tol=ATOL)
    assert ae(v.real, 0.00028162445198141832551, tol=PTOL)
    assert ae(v.imag, -0.17932453503935894015, tol=PTOL)
    v = fp.e1((2.0 + 2.0j))
    assert ae(v, (-0.033767089606562004246 - 0.018599414169750541925j), tol=ATOL)
    assert ae(v.real, -0.033767089606562004246, tol=PTOL)
    assert ae(v.imag, -0.018599414169750541925, tol=PTOL)
    v = fp.e1((5.0 + 5.0j))
    assert ae(v, (0.0007266506660356393891 + 0.00047102780163522245054j), tol=ATOL)
    assert ae(v.real, 0.0007266506660356393891, tol=PTOL)
    assert ae(v.imag, 0.00047102780163522245054, tol=PTOL)
    v = fp.e1((20.0 + 20.0j))
    assert ae(v, (-2.3824537449367396579e-11 - 6.6969873156525615158e-11j), tol=ATOL)
    assert ae(v.real, -2.3824537449367396579e-11, tol=PTOL)
    assert ae(v.imag, -6.6969873156525615158e-11, tol=PTOL)
    v = fp.e1((30.0 + 30.0j))
    assert ae(v, (1.7316045841744061617e-15 + 1.3065678019487308689e-15j), tol=ATOL)
    assert ae(v.real, 1.7316045841744061617e-15, tol=PTOL)
    assert ae(v.imag, 1.3065678019487308689e-15, tol=PTOL)
    v = fp.e1((40.0 + 40.0j))
    assert ae(v, (-7.4001043002899232182e-20 - 4.991847855336816304e-21j), tol=ATOL)
    assert ae(v.real, -7.4001043002899232182e-20, tol=PTOL)
    assert ae(v.imag, -4.991847855336816304e-21, tol=PTOL)
    v = fp.e1((50.0 + 50.0j))
    assert ae(v, (2.3566128324644641219e-24 - 1.3188326726201614778e-24j), tol=ATOL)
    assert ae(v.real, 2.3566128324644641219e-24, tol=PTOL)
    assert ae(v.imag, -1.3188326726201614778e-24, tol=PTOL)
    v = fp.e1((80.0 + 80.0j))
    assert ae(v, (9.8279750572186526673e-38 + 1.243952841288868831e-37j), tol=ATOL)
    assert ae(v.real, 9.8279750572186526673e-38, tol=PTOL)
    assert ae(v.imag, 1.243952841288868831e-37, tol=PTOL)
    v = fp.e1((1.1641532182693481445e-10 + 4.6566128730773925781e-10j))
    assert ae(v, (20.880034621664969632 - 1.3258176632023711778j), tol=ATOL)
    assert ae(v.real, 20.880034621664969632, tol=PTOL)
    assert ae(v.imag, -1.3258176632023711778, tol=PTOL)
    v = fp.e1((0.25 + 1.0j))
    assert ae(v, (-0.16868306393667788761 - 0.4858011885947426971j), tol=ATOL)
    assert ae(v.real, -0.16868306393667788761, tol=PTOL)
    assert ae(v.imag, -0.4858011885947426971, tol=PTOL)
    v = fp.e1((1.0 + 4.0j))
    assert ae(v, (0.03373591813926547318 + 0.073523452241083821877j), tol=ATOL)
    assert ae(v.real, 0.03373591813926547318, tol=PTOL)
    assert ae(v.imag, 0.073523452241083821877, tol=PTOL)
    v = fp.e1((2.0 + 8.0j))
    assert ae(v, (-0.015392833434733785143 - 0.0031747121557605415914j), tol=ATOL)
    assert ae(v.real, -0.015392833434733785143, tol=PTOL)
    assert ae(v.imag, -0.0031747121557605415914, tol=PTOL)
    v = fp.e1((5.0 + 20.0j))
    assert ae(v, (-0.00024419662286542966525 - 0.00021008322966152755674j), tol=ATOL)
    assert ae(v.real, -0.00024419662286542966525, tol=PTOL)
    assert ae(v.imag, -0.00021008322966152755674, tol=PTOL)
    v = fp.e1((20.0 + 80.0j))
    assert ae(v, (2.3255552781051330088e-11 + 8.9463918891349438007e-12j), tol=ATOL)
    assert ae(v.real, 2.3255552781051330088e-11, tol=PTOL)
    assert ae(v.imag, 8.9463918891349438007e-12, tol=PTOL)
    v = fp.e1((30.0 + 120.0j))
    assert ae(v, (-2.7068919097124652332e-16 - 7.0477762411705130239e-16j), tol=ATOL)
    assert ae(v.real, -2.7068919097124652332e-16, tol=PTOL)
    assert ae(v.imag, -7.0477762411705130239e-16, tol=PTOL)
    v = fp.e1((40.0 + 160.0j))
    assert ae(v, (-1.1695597827678024687e-20 + 2.2907401455645736661e-20j), tol=ATOL)
    assert ae(v.real, -1.1695597827678024687e-20, tol=PTOL)
    assert ae(v.imag, 2.2907401455645736661e-20, tol=PTOL)
    v = fp.e1((50.0 + 200.0j))
    assert ae(v, (9.0323746914410162531e-25 - 2.3950601790033530935e-25j), tol=ATOL)
    assert ae(v.real, 9.0323746914410162531e-25, tol=PTOL)
    assert ae(v.imag, -2.3950601790033530935e-25, tol=PTOL)
    v = fp.e1((80.0 + 320.0j))
    assert ae(v, (3.4819106748728063576e-38 - 4.215653005615772724e-38j), tol=ATOL)
    assert ae(v.real, 3.4819106748728063576e-38, tol=PTOL)
    assert ae(v.imag, -4.215653005615772724e-38, tol=PTOL)
    v = fp.e1((0.0 + 1.1641532182693481445e-10j))
    assert ae(v, (22.29664129357666235 - 1.5707963266784812974j), tol=ATOL)
    assert ae(v.real, 22.29664129357666235, tol=PTOL)
    assert ae(v.imag, -1.5707963266784812974, tol=PTOL)
    v = fp.e1((0.0 + 0.25j))
    assert ae(v, (0.82466306258094565309 - 1.3216627564751394551j), tol=ATOL)
    assert ae(v.real, 0.82466306258094565309, tol=PTOL)
    assert ae(v.imag, -1.3216627564751394551, tol=PTOL)
    v = fp.e1((0.0 + 1.0j))
    assert ae(v, (-0.33740392290096813466 - 0.62471325642771360429j), tol=ATOL)
    assert ae(v.real, -0.33740392290096813466, tol=PTOL)
    assert ae(v.imag, -0.62471325642771360429, tol=PTOL)
    v = fp.e1((0.0 + 2.0j))
    assert ae(v, (-0.4229808287748649957 + 0.034616650007798229345j), tol=ATOL)
    assert ae(v.real, -0.4229808287748649957, tol=PTOL)
    assert ae(v.imag, 0.034616650007798229345, tol=PTOL)
    v = fp.e1((0.0 + 5.0j))
    assert ae(v, (0.19002974965664387862 - 0.020865081850222481957j), tol=ATOL)
    assert ae(v.real, 0.19002974965664387862, tol=PTOL)
    assert ae(v.imag, -0.020865081850222481957, tol=PTOL)
    v = fp.e1((0.0 + 20.0j))
    assert ae(v, (-0.04441982084535331654 - 0.022554625751456779068j), tol=ATOL)
    assert ae(v.real, -0.04441982084535331654, tol=PTOL)
    assert ae(v.imag, -0.022554625751456779068, tol=PTOL)
    v = fp.e1((0.0 + 30.0j))
    assert ae(v, (0.033032417282071143779 - 0.0040397867645455082476j), tol=ATOL)
    assert ae(v.real, 0.033032417282071143779, tol=PTOL)
    assert ae(v.imag, -0.0040397867645455082476, tol=PTOL)
    v = fp.e1((0.0 + 40.0j))
    assert ae(v, (-0.019020007896208766962 + 0.016188792559887887544j), tol=ATOL)
    assert ae(v.real, -0.019020007896208766962, tol=PTOL)
    assert ae(v.imag, 0.016188792559887887544, tol=PTOL)
    v = fp.e1((0.0 + 50.0j))
    assert ae(v, (0.0056283863241163054402 - 0.019179254308960724503j), tol=ATOL)
    assert ae(v.real, 0.0056283863241163054402, tol=PTOL)
    assert ae(v.imag, -0.019179254308960724503, tol=PTOL)
    v = fp.e1((0.0 + 80.0j))
    assert ae(v, (0.012402501155070958192 + 0.0015345601175906961199j), tol=ATOL)
    assert ae(v.real, 0.012402501155070958192, tol=PTOL)
    assert ae(v.imag, 0.0015345601175906961199, tol=PTOL)
    v = fp.e1((-1.1641532182693481445e-10 + 4.6566128730773925781e-10j))
    assert ae(v, (20.880034621432138988 - 1.8157749894560994861j), tol=ATOL)
    assert ae(v.real, 20.880034621432138988, tol=PTOL)
    assert ae(v.imag, -1.8157749894560994861, tol=PTOL)
    v = fp.e1((-0.25 + 1.0j))
    assert ae(v, (-0.59066621214766308594 - 0.74474454765205036972j), tol=ATOL)
    assert ae(v.real, -0.59066621214766308594, tol=PTOL)
    assert ae(v.imag, -0.74474454765205036972, tol=PTOL)
    v = fp.e1((-1.0 + 4.0j))
    assert ae(v, (0.49739047283060471093 + 0.41543605404038863174j), tol=ATOL)
    assert ae(v.real, 0.49739047283060471093, tol=PTOL)
    assert ae(v.imag, 0.41543605404038863174, tol=PTOL)
    v = fp.e1((-2.0 + 8.0j))
    assert ae(v, (-0.8705211147733730969 + 0.24099328498605539667j), tol=ATOL)
    assert ae(v.real, -0.8705211147733730969, tol=PTOL)
    assert ae(v.imag, 0.24099328498605539667, tol=PTOL)
    v = fp.e1((-5.0 + 20.0j))
    assert ae(v, (-7.0789514293925893007 - 1.6102177171960790536j), tol=ATOL)
    assert ae(v.real, -7.0789514293925893007, tol=PTOL)
    assert ae(v.imag, -1.6102177171960790536, tol=PTOL)
    v = fp.e1((-20.0 + 80.0j))
    assert ae(v, (5855431.4907298084434 - 720920.93315409165707j), tol=ATOL)
    assert ae(v.real, 5855431.4907298084434, tol=PTOL)
    assert ae(v.imag, -720920.93315409165707, tol=PTOL)
    v = fp.e1((-30.0 + 120.0j))
    assert ae(v, (-65402491644.703470747 - 56697658399.657460294j), tol=ATOL)
    assert ae(v.real, -65402491644.703470747, tol=PTOL)
    assert ae(v.imag, -56697658399.657460294, tol=PTOL)
    v = fp.e1((-40.0 + 160.0j))
    assert ae(v, (25504929379604.776769 + 1429035198630573.2463j), tol=ATOL)
    assert ae(v.real, 25504929379604.776769, tol=PTOL)
    assert ae(v.imag, 1429035198630573.2463, tol=PTOL)
    v = fp.e1((-50.0 + 200.0j))
    assert ae(v, (18437746526988116954.0 - 17146362239046152345.0j), tol=ATOL)
    assert ae(v.real, 18437746526988116954.0, tol=PTOL)
    assert ae(v.imag, -17146362239046152345.0, tol=PTOL)
    v = fp.e1((-80.0 + 320.0j))
    assert ae(v, (3.3464697299634526706e+31 - 1.6473152633843023919e+32j), tol=ATOL)
    assert ae(v.real, 3.3464697299634526706e+31, tol=PTOL)
    assert ae(v.imag, -1.6473152633843023919e+32, tol=PTOL)
    v = fp.e1((-4.6566128730773925781e-10 + 1.1641532182693481445e-10j))
    assert ae(v, (20.880034621082893023 - 2.8966139903465137624j), tol=ATOL)
    assert ae(v.real, 20.880034621082893023, tol=PTOL)
    assert ae(v.imag, -2.8966139903465137624, tol=PTOL)
    v = fp.e1((-1.0 + 0.25j))
    assert ae(v, (-1.8942716983721074932 - 2.4689102827070540799j), tol=ATOL)
    assert ae(v.real, -1.8942716983721074932, tol=PTOL)
    assert ae(v.imag, -2.4689102827070540799, tol=PTOL)
    v = fp.e1((-4.0 + 1.0j))
    assert ae(v, (-14.806699492675420438 + 9.1384225230837893776j), tol=ATOL)
    assert ae(v.real, -14.806699492675420438, tol=PTOL)
    assert ae(v.imag, 9.1384225230837893776, tol=PTOL)
    v = fp.e1((-8.0 + 2.0j))
    assert ae(v, (54.633252667426386294 + 413.20318163814670688j), tol=ATOL)
    assert ae(v.real, 54.633252667426386294, tol=PTOL)
    assert ae(v.imag, 413.20318163814670688, tol=PTOL)
    v = fp.e1((-20.0 + 5.0j))
    assert ae(v, (-711836.97165402624643 - 24745250.939695900956j), tol=ATOL)
    assert ae(v.real, -711836.97165402624643, tol=PTOL)
    assert ae(v.imag, -24745250.939695900956, tol=PTOL)
    v = fp.e1((-80.0 + 20.0j))
    assert ae(v, (-4.2139911108612653091e+32 + 5.3367124741918251637e+32j), tol=ATOL)
    assert ae(v.real, -4.2139911108612653091e+32, tol=PTOL)
    assert ae(v.imag, 5.3367124741918251637e+32, tol=PTOL)
    v = fp.e1((-120.0 + 30.0j))
    assert ae(v, (9.7760616203707508892e+48 - 1.058257682317195792e+50j), tol=ATOL)
    assert ae(v.real, 9.7760616203707508892e+48, tol=PTOL)
    assert ae(v.imag, -1.058257682317195792e+50, tol=PTOL)
    v = fp.e1((-160.0 + 40.0j))
    assert ae(v, (8.7065541466623638861e+66 + 1.6577106725141739889e+67j), tol=ATOL)
    assert ae(v.real, 8.7065541466623638861e+66, tol=PTOL)
    assert ae(v.imag, 1.6577106725141739889e+67, tol=PTOL)
    v = fp.e1((-200.0 + 50.0j))
    assert ae(v, (-3.070744996327018106e+84 - 1.7243244846769415903e+84j), tol=ATOL)
    assert ae(v.real, -3.070744996327018106e+84, tol=PTOL)
    assert ae(v.imag, -1.7243244846769415903e+84, tol=PTOL)
    v = fp.e1((-320.0 + 80.0j))
    assert ae(v, (9.9960598637998647276e+135 - 2.6855081527595608863e+136j), tol=ATOL)
    assert ae(v.real, 9.9960598637998647276e+135, tol=PTOL)
    assert ae(v.imag, -2.6855081527595608863e+136, tol=PTOL)
    v = fp.e1(-1.1641532182693481445e-10)
    assert ae(v, (22.296641293460247028 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, 22.296641293460247028, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1(-0.25)
    assert ae(v, (0.54254326466191372953 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, 0.54254326466191372953, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1(-1.0)
    assert ae(v, (-1.8951178163559367555 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -1.8951178163559367555, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1(-2.0)
    assert ae(v, (-4.9542343560018901634 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -4.9542343560018901634, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1(-5.0)
    assert ae(v, (-40.185275355803177455 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -40.185275355803177455, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1(-20.0)
    assert ae(v, (-25615652.66405658882 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -25615652.66405658882, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1(-30.0)
    assert ae(v, (-368973209407.27419706 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -368973209407.27419706, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1(-40.0)
    assert ae(v, (-6039718263611241.5784 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -6039718263611241.5784, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1(-50.0)
    assert ae(v, (-1.0585636897131690963e+20 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -1.0585636897131690963e+20, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1(-80.0)
    assert ae(v, (-7.0146000049047999696e+32 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -7.0146000049047999696e+32, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1((-1.1641532182693481445e-10 + 0.0j))
    assert ae(v, (22.296641293460247028 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, 22.296641293460247028, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1((-0.25 + 0.0j))
    assert ae(v, (0.54254326466191372953 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, 0.54254326466191372953, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1((-1.0 + 0.0j))
    assert ae(v, (-1.8951178163559367555 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -1.8951178163559367555, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1((-2.0 + 0.0j))
    assert ae(v, (-4.9542343560018901634 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -4.9542343560018901634, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1((-5.0 + 0.0j))
    assert ae(v, (-40.185275355803177455 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -40.185275355803177455, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1((-20.0 + 0.0j))
    assert ae(v, (-25615652.66405658882 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -25615652.66405658882, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1((-30.0 + 0.0j))
    assert ae(v, (-368973209407.27419706 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -368973209407.27419706, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1((-40.0 + 0.0j))
    assert ae(v, (-6039718263611241.5784 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -6039718263611241.5784, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1((-50.0 + 0.0j))
    assert ae(v, (-1.0585636897131690963e+20 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -1.0585636897131690963e+20, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1((-80.0 + 0.0j))
    assert ae(v, (-7.0146000049047999696e+32 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -7.0146000049047999696e+32, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.e1((-4.6566128730773925781e-10 - 1.1641532182693481445e-10j))
    assert ae(v, (20.880034621082893023 + 2.8966139903465137624j), tol=ATOL)
    assert ae(v.real, 20.880034621082893023, tol=PTOL)
    assert ae(v.imag, 2.8966139903465137624, tol=PTOL)
    v = fp.e1((-1.0 - 0.25j))
    assert ae(v, (-1.8942716983721074932 + 2.4689102827070540799j), tol=ATOL)
    assert ae(v.real, -1.8942716983721074932, tol=PTOL)
    assert ae(v.imag, 2.4689102827070540799, tol=PTOL)
    v = fp.e1((-4.0 - 1.0j))
    assert ae(v, (-14.806699492675420438 - 9.1384225230837893776j), tol=ATOL)
    assert ae(v.real, -14.806699492675420438, tol=PTOL)
    assert ae(v.imag, -9.1384225230837893776, tol=PTOL)
    v = fp.e1((-8.0 - 2.0j))
    assert ae(v, (54.633252667426386294 - 413.20318163814670688j), tol=ATOL)
    assert ae(v.real, 54.633252667426386294, tol=PTOL)
    assert ae(v.imag, -413.20318163814670688, tol=PTOL)
    v = fp.e1((-20.0 - 5.0j))
    assert ae(v, (-711836.97165402624643 + 24745250.939695900956j), tol=ATOL)
    assert ae(v.real, -711836.97165402624643, tol=PTOL)
    assert ae(v.imag, 24745250.939695900956, tol=PTOL)
    v = fp.e1((-80.0 - 20.0j))
    assert ae(v, (-4.2139911108612653091e+32 - 5.3367124741918251637e+32j), tol=ATOL)
    assert ae(v.real, -4.2139911108612653091e+32, tol=PTOL)
    assert ae(v.imag, -5.3367124741918251637e+32, tol=PTOL)
    v = fp.e1((-120.0 - 30.0j))
    assert ae(v, (9.7760616203707508892e+48 + 1.058257682317195792e+50j), tol=ATOL)
    assert ae(v.real, 9.7760616203707508892e+48, tol=PTOL)
    assert ae(v.imag, 1.058257682317195792e+50, tol=PTOL)
    v = fp.e1((-160.0 - 40.0j))
    assert ae(v, (8.7065541466623638861e+66 - 1.6577106725141739889e+67j), tol=ATOL)
    assert ae(v.real, 8.7065541466623638861e+66, tol=PTOL)
    assert ae(v.imag, -1.6577106725141739889e+67, tol=PTOL)
    v = fp.e1((-200.0 - 50.0j))
    assert ae(v, (-3.070744996327018106e+84 + 1.7243244846769415903e+84j), tol=ATOL)
    assert ae(v.real, -3.070744996327018106e+84, tol=PTOL)
    assert ae(v.imag, 1.7243244846769415903e+84, tol=PTOL)
    v = fp.e1((-320.0 - 80.0j))
    assert ae(v, (9.9960598637998647276e+135 + 2.6855081527595608863e+136j), tol=ATOL)
    assert ae(v.real, 9.9960598637998647276e+135, tol=PTOL)
    assert ae(v.imag, 2.6855081527595608863e+136, tol=PTOL)
    v = fp.e1((-1.1641532182693481445e-10 - 1.1641532182693481445e-10j))
    assert ae(v, (21.950067703180274374 + 2.356194490075929607j), tol=ATOL)
    assert ae(v.real, 21.950067703180274374, tol=PTOL)
    assert ae(v.imag, 2.356194490075929607, tol=PTOL)
    v = fp.e1((-0.25 - 0.25j))
    assert ae(v, (0.21441047326710323254 + 2.0732153554307936389j), tol=ATOL)
    assert ae(v.real, 0.21441047326710323254, tol=PTOL)
    assert ae(v.imag, 2.0732153554307936389, tol=PTOL)
    v = fp.e1((-1.0 - 1.0j))
    assert ae(v, (-1.7646259855638540684 + 0.7538228020792708192j), tol=ATOL)
    assert ae(v.real, -1.7646259855638540684, tol=PTOL)
    assert ae(v.imag, 0.7538228020792708192, tol=PTOL)
    v = fp.e1((-2.0 - 2.0j))
    assert ae(v, (-1.8920781621855474089 - 2.1753697842428647236j), tol=ATOL)
    assert ae(v.real, -1.8920781621855474089, tol=PTOL)
    assert ae(v.imag, -2.1753697842428647236, tol=PTOL)
    v = fp.e1((-5.0 - 5.0j))
    assert ae(v, (13.470936071475245856 + 18.464085049321024206j), tol=ATOL)
    assert ae(v.real, 13.470936071475245856, tol=PTOL)
    assert ae(v.imag, 18.464085049321024206, tol=PTOL)
    v = fp.e1((-20.0 - 20.0j))
    assert ae(v, (-16589317.398788971896 - 5831702.3296441771206j), tol=ATOL)
    assert ae(v.real, -16589317.398788971896, tol=PTOL)
    assert ae(v.imag, -5831702.3296441771206, tol=PTOL)
    v = fp.e1((-30.0 - 30.0j))
    assert ae(v, (154596484273.69322527 + 204179357837.41389696j), tol=ATOL)
    assert ae(v.real, 154596484273.69322527, tol=PTOL)
    assert ae(v.imag, 204179357837.41389696, tol=PTOL)
    v = fp.e1((-40.0 - 40.0j))
    assert ae(v, (-287512180321448.45408 - 4203502407932314.974j), tol=ATOL)
    assert ae(v.real, -287512180321448.45408, tol=PTOL)
    assert ae(v.imag, -4203502407932314.974, tol=PTOL)
    v = fp.e1((-50.0 - 50.0j))
    assert ae(v, (-36128528616649268826.0 + 64648801861338741963.0j), tol=ATOL)
    assert ae(v.real, -36128528616649268826.0, tol=PTOL)
    assert ae(v.imag, 64648801861338741963.0, tol=PTOL)
    v = fp.e1((-80.0 - 80.0j))
    assert ae(v, (3.8674816337930010217e+32 + 3.0540709639658071041e+32j), tol=ATOL)
    assert ae(v.real, 3.8674816337930010217e+32, tol=PTOL)
    assert ae(v.imag, 3.0540709639658071041e+32, tol=PTOL)
    v = fp.e1((-1.1641532182693481445e-10 - 4.6566128730773925781e-10j))
    assert ae(v, (20.880034621432138988 + 1.8157749894560994861j), tol=ATOL)
    assert ae(v.real, 20.880034621432138988, tol=PTOL)
    assert ae(v.imag, 1.8157749894560994861, tol=PTOL)
    v = fp.e1((-0.25 - 1.0j))
    assert ae(v, (-0.59066621214766308594 + 0.74474454765205036972j), tol=ATOL)
    assert ae(v.real, -0.59066621214766308594, tol=PTOL)
    assert ae(v.imag, 0.74474454765205036972, tol=PTOL)
    v = fp.e1((-1.0 - 4.0j))
    assert ae(v, (0.49739047283060471093 - 0.41543605404038863174j), tol=ATOL)
    assert ae(v.real, 0.49739047283060471093, tol=PTOL)
    assert ae(v.imag, -0.41543605404038863174, tol=PTOL)
    v = fp.e1((-2.0 - 8.0j))
    assert ae(v, (-0.8705211147733730969 - 0.24099328498605539667j), tol=ATOL)
    assert ae(v.real, -0.8705211147733730969, tol=PTOL)
    assert ae(v.imag, -0.24099328498605539667, tol=PTOL)
    v = fp.e1((-5.0 - 20.0j))
    assert ae(v, (-7.0789514293925893007 + 1.6102177171960790536j), tol=ATOL)
    assert ae(v.real, -7.0789514293925893007, tol=PTOL)
    assert ae(v.imag, 1.6102177171960790536, tol=PTOL)
    v = fp.e1((-20.0 - 80.0j))
    assert ae(v, (5855431.4907298084434 + 720920.93315409165707j), tol=ATOL)
    assert ae(v.real, 5855431.4907298084434, tol=PTOL)
    assert ae(v.imag, 720920.93315409165707, tol=PTOL)
    v = fp.e1((-30.0 - 120.0j))
    assert ae(v, (-65402491644.703470747 + 56697658399.657460294j), tol=ATOL)
    assert ae(v.real, -65402491644.703470747, tol=PTOL)
    assert ae(v.imag, 56697658399.657460294, tol=PTOL)
    v = fp.e1((-40.0 - 160.0j))
    assert ae(v, (25504929379604.776769 - 1429035198630573.2463j), tol=ATOL)
    assert ae(v.real, 25504929379604.776769, tol=PTOL)
    assert ae(v.imag, -1429035198630573.2463, tol=PTOL)
    v = fp.e1((-50.0 - 200.0j))
    assert ae(v, (18437746526988116954.0 + 17146362239046152345.0j), tol=ATOL)
    assert ae(v.real, 18437746526988116954.0, tol=PTOL)
    assert ae(v.imag, 17146362239046152345.0, tol=PTOL)
    v = fp.e1((-80.0 - 320.0j))
    assert ae(v, (3.3464697299634526706e+31 + 1.6473152633843023919e+32j), tol=ATOL)
    assert ae(v.real, 3.3464697299634526706e+31, tol=PTOL)
    assert ae(v.imag, 1.6473152633843023919e+32, tol=PTOL)
    v = fp.e1((0.0 - 1.1641532182693481445e-10j))
    assert ae(v, (22.29664129357666235 + 1.5707963266784812974j), tol=ATOL)
    assert ae(v.real, 22.29664129357666235, tol=PTOL)
    assert ae(v.imag, 1.5707963266784812974, tol=PTOL)
    v = fp.e1((0.0 - 0.25j))
    assert ae(v, (0.82466306258094565309 + 1.3216627564751394551j), tol=ATOL)
    assert ae(v.real, 0.82466306258094565309, tol=PTOL)
    assert ae(v.imag, 1.3216627564751394551, tol=PTOL)
    v = fp.e1((0.0 - 1.0j))
    assert ae(v, (-0.33740392290096813466 + 0.62471325642771360429j), tol=ATOL)
    assert ae(v.real, -0.33740392290096813466, tol=PTOL)
    assert ae(v.imag, 0.62471325642771360429, tol=PTOL)
    v = fp.e1((0.0 - 2.0j))
    assert ae(v, (-0.4229808287748649957 - 0.034616650007798229345j), tol=ATOL)
    assert ae(v.real, -0.4229808287748649957, tol=PTOL)
    assert ae(v.imag, -0.034616650007798229345, tol=PTOL)
    v = fp.e1((0.0 - 5.0j))
    assert ae(v, (0.19002974965664387862 + 0.020865081850222481957j), tol=ATOL)
    assert ae(v.real, 0.19002974965664387862, tol=PTOL)
    assert ae(v.imag, 0.020865081850222481957, tol=PTOL)
    v = fp.e1((0.0 - 20.0j))
    assert ae(v, (-0.04441982084535331654 + 0.022554625751456779068j), tol=ATOL)
    assert ae(v.real, -0.04441982084535331654, tol=PTOL)
    assert ae(v.imag, 0.022554625751456779068, tol=PTOL)
    v = fp.e1((0.0 - 30.0j))
    assert ae(v, (0.033032417282071143779 + 0.0040397867645455082476j), tol=ATOL)
    assert ae(v.real, 0.033032417282071143779, tol=PTOL)
    assert ae(v.imag, 0.0040397867645455082476, tol=PTOL)
    v = fp.e1((0.0 - 40.0j))
    assert ae(v, (-0.019020007896208766962 - 0.016188792559887887544j), tol=ATOL)
    assert ae(v.real, -0.019020007896208766962, tol=PTOL)
    assert ae(v.imag, -0.016188792559887887544, tol=PTOL)
    v = fp.e1((0.0 - 50.0j))
    assert ae(v, (0.0056283863241163054402 + 0.019179254308960724503j), tol=ATOL)
    assert ae(v.real, 0.0056283863241163054402, tol=PTOL)
    assert ae(v.imag, 0.019179254308960724503, tol=PTOL)
    v = fp.e1((0.0 - 80.0j))
    assert ae(v, (0.012402501155070958192 - 0.0015345601175906961199j), tol=ATOL)
    assert ae(v.real, 0.012402501155070958192, tol=PTOL)
    assert ae(v.imag, -0.0015345601175906961199, tol=PTOL)
    v = fp.e1((1.1641532182693481445e-10 - 4.6566128730773925781e-10j))
    assert ae(v, (20.880034621664969632 + 1.3258176632023711778j), tol=ATOL)
    assert ae(v.real, 20.880034621664969632, tol=PTOL)
    assert ae(v.imag, 1.3258176632023711778, tol=PTOL)
    v = fp.e1((0.25 - 1.0j))
    assert ae(v, (-0.16868306393667788761 + 0.4858011885947426971j), tol=ATOL)
    assert ae(v.real, -0.16868306393667788761, tol=PTOL)
    assert ae(v.imag, 0.4858011885947426971, tol=PTOL)
    v = fp.e1((1.0 - 4.0j))
    assert ae(v, (0.03373591813926547318 - 0.073523452241083821877j), tol=ATOL)
    assert ae(v.real, 0.03373591813926547318, tol=PTOL)
    assert ae(v.imag, -0.073523452241083821877, tol=PTOL)
    v = fp.e1((2.0 - 8.0j))
    assert ae(v, (-0.015392833434733785143 + 0.0031747121557605415914j), tol=ATOL)
    assert ae(v.real, -0.015392833434733785143, tol=PTOL)
    assert ae(v.imag, 0.0031747121557605415914, tol=PTOL)
    v = fp.e1((5.0 - 20.0j))
    assert ae(v, (-0.00024419662286542966525 + 0.00021008322966152755674j), tol=ATOL)
    assert ae(v.real, -0.00024419662286542966525, tol=PTOL)
    assert ae(v.imag, 0.00021008322966152755674, tol=PTOL)
    v = fp.e1((20.0 - 80.0j))
    assert ae(v, (2.3255552781051330088e-11 - 8.9463918891349438007e-12j), tol=ATOL)
    assert ae(v.real, 2.3255552781051330088e-11, tol=PTOL)
    assert ae(v.imag, -8.9463918891349438007e-12, tol=PTOL)
    v = fp.e1((30.0 - 120.0j))
    assert ae(v, (-2.7068919097124652332e-16 + 7.0477762411705130239e-16j), tol=ATOL)
    assert ae(v.real, -2.7068919097124652332e-16, tol=PTOL)
    assert ae(v.imag, 7.0477762411705130239e-16, tol=PTOL)
    v = fp.e1((40.0 - 160.0j))
    assert ae(v, (-1.1695597827678024687e-20 - 2.2907401455645736661e-20j), tol=ATOL)
    assert ae(v.real, -1.1695597827678024687e-20, tol=PTOL)
    assert ae(v.imag, -2.2907401455645736661e-20, tol=PTOL)
    v = fp.e1((50.0 - 200.0j))
    assert ae(v, (9.0323746914410162531e-25 + 2.3950601790033530935e-25j), tol=ATOL)
    assert ae(v.real, 9.0323746914410162531e-25, tol=PTOL)
    assert ae(v.imag, 2.3950601790033530935e-25, tol=PTOL)
    v = fp.e1((80.0 - 320.0j))
    assert ae(v, (3.4819106748728063576e-38 + 4.215653005615772724e-38j), tol=ATOL)
    assert ae(v.real, 3.4819106748728063576e-38, tol=PTOL)
    assert ae(v.imag, 4.215653005615772724e-38, tol=PTOL)
    v = fp.e1((1.1641532182693481445e-10 - 1.1641532182693481445e-10j))
    assert ae(v, (21.950067703413105017 + 0.7853981632810329878j), tol=ATOL)
    assert ae(v.real, 21.950067703413105017, tol=PTOL)
    assert ae(v.imag, 0.7853981632810329878, tol=PTOL)
    v = fp.e1((0.25 - 0.25j))
    assert ae(v, (0.71092525792923287894 + 0.56491812441304194711j), tol=ATOL)
    assert ae(v.real, 0.71092525792923287894, tol=PTOL)
    assert ae(v.imag, 0.56491812441304194711, tol=PTOL)
    v = fp.e1((1.0 - 1.0j))
    assert ae(v, (0.00028162445198141832551 + 0.17932453503935894015j), tol=ATOL)
    assert ae(v.real, 0.00028162445198141832551, tol=PTOL)
    assert ae(v.imag, 0.17932453503935894015, tol=PTOL)
    v = fp.e1((2.0 - 2.0j))
    assert ae(v, (-0.033767089606562004246 + 0.018599414169750541925j), tol=ATOL)
    assert ae(v.real, -0.033767089606562004246, tol=PTOL)
    assert ae(v.imag, 0.018599414169750541925, tol=PTOL)
    v = fp.e1((5.0 - 5.0j))
    assert ae(v, (0.0007266506660356393891 - 0.00047102780163522245054j), tol=ATOL)
    assert ae(v.real, 0.0007266506660356393891, tol=PTOL)
    assert ae(v.imag, -0.00047102780163522245054, tol=PTOL)
    v = fp.e1((20.0 - 20.0j))
    assert ae(v, (-2.3824537449367396579e-11 + 6.6969873156525615158e-11j), tol=ATOL)
    assert ae(v.real, -2.3824537449367396579e-11, tol=PTOL)
    assert ae(v.imag, 6.6969873156525615158e-11, tol=PTOL)
    v = fp.e1((30.0 - 30.0j))
    assert ae(v, (1.7316045841744061617e-15 - 1.3065678019487308689e-15j), tol=ATOL)
    assert ae(v.real, 1.7316045841744061617e-15, tol=PTOL)
    assert ae(v.imag, -1.3065678019487308689e-15, tol=PTOL)
    v = fp.e1((40.0 - 40.0j))
    assert ae(v, (-7.4001043002899232182e-20 + 4.991847855336816304e-21j), tol=ATOL)
    assert ae(v.real, -7.4001043002899232182e-20, tol=PTOL)
    assert ae(v.imag, 4.991847855336816304e-21, tol=PTOL)
    v = fp.e1((50.0 - 50.0j))
    assert ae(v, (2.3566128324644641219e-24 + 1.3188326726201614778e-24j), tol=ATOL)
    assert ae(v.real, 2.3566128324644641219e-24, tol=PTOL)
    assert ae(v.imag, 1.3188326726201614778e-24, tol=PTOL)
    v = fp.e1((80.0 - 80.0j))
    assert ae(v, (9.8279750572186526673e-38 - 1.243952841288868831e-37j), tol=ATOL)
    assert ae(v.real, 9.8279750572186526673e-38, tol=PTOL)
    assert ae(v.imag, -1.243952841288868831e-37, tol=PTOL)
    v = fp.e1((4.6566128730773925781e-10 - 1.1641532182693481445e-10j))
    assert ae(v, (20.880034622014215597 + 0.24497866301044883237j), tol=ATOL)
    assert ae(v.real, 20.880034622014215597, tol=PTOL)
    assert ae(v.imag, 0.24497866301044883237, tol=PTOL)
    v = fp.e1((1.0 - 0.25j))
    assert ae(v, (0.19731063945004229095 + 0.087366045774299963672j), tol=ATOL)
    assert ae(v.real, 0.19731063945004229095, tol=PTOL)
    assert ae(v.imag, 0.087366045774299963672, tol=PTOL)
    v = fp.e1((4.0 - 1.0j))
    assert ae(v, (0.0013106173980145506944 + 0.0034542480199350626699j), tol=ATOL)
    assert ae(v.real, 0.0013106173980145506944, tol=PTOL)
    assert ae(v.imag, 0.0034542480199350626699, tol=PTOL)
    v = fp.e1((8.0 - 2.0j))
    assert ae(v, (-0.000022278049065270225945 + 0.000029191940456521555288j), tol=ATOL)
    assert ae(v.real, -0.000022278049065270225945, tol=PTOL)
    assert ae(v.imag, 0.000029191940456521555288, tol=PTOL)
    v = fp.e1((20.0 - 5.0j))
    assert ae(v, (4.7711374515765346894e-11 - 8.2902652405126947359e-11j), tol=ATOL)
    assert ae(v.real, 4.7711374515765346894e-11, tol=PTOL)
    assert ae(v.imag, -8.2902652405126947359e-11, tol=PTOL)
    v = fp.e1((80.0 - 20.0j))
    assert ae(v, (3.8353473865788235787e-38 + 2.129247592349605139e-37j), tol=ATOL)
    assert ae(v.real, 3.8353473865788235787e-38, tol=PTOL)
    assert ae(v.imag, 2.129247592349605139e-37, tol=PTOL)
    v = fp.e1((120.0 - 30.0j))
    assert ae(v, (2.3836002337480334716e-55 - 5.6704043587126198306e-55j), tol=ATOL)
    assert ae(v.real, 2.3836002337480334716e-55, tol=PTOL)
    assert ae(v.imag, -5.6704043587126198306e-55, tol=PTOL)
    v = fp.e1((160.0 - 40.0j))
    assert ae(v, (-1.6238022898654510661e-72 + 1.104172355572287367e-72j), tol=ATOL)
    assert ae(v.real, -1.6238022898654510661e-72, tol=PTOL)
    assert ae(v.imag, 1.104172355572287367e-72, tol=PTOL)
    v = fp.e1((200.0 - 50.0j))
    assert ae(v, (6.6800061461666228487e-90 - 1.4473816083541016115e-91j), tol=ATOL)
    assert ae(v.real, 6.6800061461666228487e-90, tol=PTOL)
    assert ae(v.imag, -1.4473816083541016115e-91, tol=PTOL)
    v = fp.e1((320.0 - 80.0j))
    assert ae(v, (4.2737871527778786157e-143 - 3.1789935525785660314e-142j), tol=ATOL)
    assert ae(v.real, 4.2737871527778786157e-143, tol=PTOL)
    assert ae(v.imag, -3.1789935525785660314e-142, tol=PTOL)
    v = fp.ei(1.1641532182693481445e-10)
    assert ae(v, -22.296641293460247028, tol=ATOL)
    assert type(v) is float
    v = fp.ei(0.25)
    assert ae(v, -0.54254326466191372953, tol=ATOL)
    assert type(v) is float
    v = fp.ei(1.0)
    assert ae(v, 1.8951178163559367555, tol=ATOL)
    assert type(v) is float
    v = fp.ei(2.0)
    assert ae(v, 4.9542343560018901634, tol=ATOL)
    assert type(v) is float
    v = fp.ei(5.0)
    assert ae(v, 40.185275355803177455, tol=ATOL)
    assert type(v) is float
    v = fp.ei(20.0)
    assert ae(v, 25615652.66405658882, tol=ATOL)
    assert type(v) is float
    v = fp.ei(30.0)
    assert ae(v, 368973209407.27419706, tol=ATOL)
    assert type(v) is float
    v = fp.ei(40.0)
    assert ae(v, 6039718263611241.5784, tol=ATOL)
    assert type(v) is float
    v = fp.ei(50.0)
    assert ae(v, 1.0585636897131690963e+20, tol=ATOL)
    assert type(v) is float
    v = fp.ei(80.0)
    assert ae(v, 7.0146000049047999696e+32, tol=ATOL)
    assert type(v) is float
    v = fp.ei((1.1641532182693481445e-10 + 0.0j))
    assert ae(v, (-22.296641293460247028 + 0.0j), tol=ATOL)
    assert ae(v.real, -22.296641293460247028, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((0.25 + 0.0j))
    assert ae(v, (-0.54254326466191372953 + 0.0j), tol=ATOL)
    assert ae(v.real, -0.54254326466191372953, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((1.0 + 0.0j))
    assert ae(v, (1.8951178163559367555 + 0.0j), tol=ATOL)
    assert ae(v.real, 1.8951178163559367555, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((2.0 + 0.0j))
    assert ae(v, (4.9542343560018901634 + 0.0j), tol=ATOL)
    assert ae(v.real, 4.9542343560018901634, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((5.0 + 0.0j))
    assert ae(v, (40.185275355803177455 + 0.0j), tol=ATOL)
    assert ae(v.real, 40.185275355803177455, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((20.0 + 0.0j))
    assert ae(v, (25615652.66405658882 + 0.0j), tol=ATOL)
    assert ae(v.real, 25615652.66405658882, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((30.0 + 0.0j))
    assert ae(v, (368973209407.27419706 + 0.0j), tol=ATOL)
    assert ae(v.real, 368973209407.27419706, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((40.0 + 0.0j))
    assert ae(v, (6039718263611241.5784 + 0.0j), tol=ATOL)
    assert ae(v.real, 6039718263611241.5784, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((50.0 + 0.0j))
    assert ae(v, (1.0585636897131690963e+20 + 0.0j), tol=ATOL)
    assert ae(v.real, 1.0585636897131690963e+20, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((80.0 + 0.0j))
    assert ae(v, (7.0146000049047999696e+32 + 0.0j), tol=ATOL)
    assert ae(v.real, 7.0146000049047999696e+32, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((4.6566128730773925781e-10 + 1.1641532182693481445e-10j))
    assert ae(v, (-20.880034621082893023 + 0.24497866324327947603j), tol=ATOL)
    assert ae(v.real, -20.880034621082893023, tol=PTOL)
    assert ae(v.imag, 0.24497866324327947603, tol=PTOL)
    v = fp.ei((1.0 + 0.25j))
    assert ae(v, (1.8942716983721074932 + 0.67268237088273915854j), tol=ATOL)
    assert ae(v.real, 1.8942716983721074932, tol=PTOL)
    assert ae(v.imag, 0.67268237088273915854, tol=PTOL)
    v = fp.ei((4.0 + 1.0j))
    assert ae(v, (14.806699492675420438 + 12.280015176673582616j), tol=ATOL)
    assert ae(v.real, 14.806699492675420438, tol=PTOL)
    assert ae(v.imag, 12.280015176673582616, tol=PTOL)
    v = fp.ei((8.0 + 2.0j))
    assert ae(v, (-54.633252667426386294 + 416.34477429173650012j), tol=ATOL)
    assert ae(v.real, -54.633252667426386294, tol=PTOL)
    assert ae(v.imag, 416.34477429173650012, tol=PTOL)
    v = fp.ei((20.0 + 5.0j))
    assert ae(v, (711836.97165402624643 - 24745247.798103247366j), tol=ATOL)
    assert ae(v.real, 711836.97165402624643, tol=PTOL)
    assert ae(v.imag, -24745247.798103247366, tol=PTOL)
    v = fp.ei((80.0 + 20.0j))
    assert ae(v, (4.2139911108612653091e+32 + 5.3367124741918251637e+32j), tol=ATOL)
    assert ae(v.real, 4.2139911108612653091e+32, tol=PTOL)
    assert ae(v.imag, 5.3367124741918251637e+32, tol=PTOL)
    v = fp.ei((120.0 + 30.0j))
    assert ae(v, (-9.7760616203707508892e+48 - 1.058257682317195792e+50j), tol=ATOL)
    assert ae(v.real, -9.7760616203707508892e+48, tol=PTOL)
    assert ae(v.imag, -1.058257682317195792e+50, tol=PTOL)
    v = fp.ei((160.0 + 40.0j))
    assert ae(v, (-8.7065541466623638861e+66 + 1.6577106725141739889e+67j), tol=ATOL)
    assert ae(v.real, -8.7065541466623638861e+66, tol=PTOL)
    assert ae(v.imag, 1.6577106725141739889e+67, tol=PTOL)
    v = fp.ei((200.0 + 50.0j))
    assert ae(v, (3.070744996327018106e+84 - 1.7243244846769415903e+84j), tol=ATOL)
    assert ae(v.real, 3.070744996327018106e+84, tol=PTOL)
    assert ae(v.imag, -1.7243244846769415903e+84, tol=PTOL)
    v = fp.ei((320.0 + 80.0j))
    assert ae(v, (-9.9960598637998647276e+135 - 2.6855081527595608863e+136j), tol=ATOL)
    assert ae(v.real, -9.9960598637998647276e+135, tol=PTOL)
    assert ae(v.imag, -2.6855081527595608863e+136, tol=PTOL)
    v = fp.ei((1.1641532182693481445e-10 + 1.1641532182693481445e-10j))
    assert ae(v, (-21.950067703180274374 + 0.78539816351386363145j), tol=ATOL)
    assert ae(v.real, -21.950067703180274374, tol=PTOL)
    assert ae(v.imag, 0.78539816351386363145, tol=PTOL)
    v = fp.ei((0.25 + 0.25j))
    assert ae(v, (-0.21441047326710323254 + 1.0683772981589995996j), tol=ATOL)
    assert ae(v.real, -0.21441047326710323254, tol=PTOL)
    assert ae(v.imag, 1.0683772981589995996, tol=PTOL)
    v = fp.ei((1.0 + 1.0j))
    assert ae(v, (1.7646259855638540684 + 2.3877698515105224193j), tol=ATOL)
    assert ae(v.real, 1.7646259855638540684, tol=PTOL)
    assert ae(v.imag, 2.3877698515105224193, tol=PTOL)
    v = fp.ei((2.0 + 2.0j))
    assert ae(v, (1.8920781621855474089 + 5.3169624378326579621j), tol=ATOL)
    assert ae(v.real, 1.8920781621855474089, tol=PTOL)
    assert ae(v.imag, 5.3169624378326579621, tol=PTOL)
    v = fp.ei((5.0 + 5.0j))
    assert ae(v, (-13.470936071475245856 - 15.322492395731230968j), tol=ATOL)
    assert ae(v.real, -13.470936071475245856, tol=PTOL)
    assert ae(v.imag, -15.322492395731230968, tol=PTOL)
    v = fp.ei((20.0 + 20.0j))
    assert ae(v, (16589317.398788971896 + 5831705.4712368307104j), tol=ATOL)
    assert ae(v.real, 16589317.398788971896, tol=PTOL)
    assert ae(v.imag, 5831705.4712368307104, tol=PTOL)
    v = fp.ei((30.0 + 30.0j))
    assert ae(v, (-154596484273.69322527 - 204179357834.2723043j), tol=ATOL)
    assert ae(v.real, -154596484273.69322527, tol=PTOL)
    assert ae(v.imag, -204179357834.2723043, tol=PTOL)
    v = fp.ei((40.0 + 40.0j))
    assert ae(v, (287512180321448.45408 + 4203502407932318.1156j), tol=ATOL)
    assert ae(v.real, 287512180321448.45408, tol=PTOL)
    assert ae(v.imag, 4203502407932318.1156, tol=PTOL)
    v = fp.ei((50.0 + 50.0j))
    assert ae(v, (36128528616649268826.0 - 64648801861338741960.0j), tol=ATOL)
    assert ae(v.real, 36128528616649268826.0, tol=PTOL)
    assert ae(v.imag, -64648801861338741960.0, tol=PTOL)
    v = fp.ei((80.0 + 80.0j))
    assert ae(v, (-3.8674816337930010217e+32 - 3.0540709639658071041e+32j), tol=ATOL)
    assert ae(v.real, -3.8674816337930010217e+32, tol=PTOL)
    assert ae(v.imag, -3.0540709639658071041e+32, tol=PTOL)
    v = fp.ei((1.1641532182693481445e-10 + 4.6566128730773925781e-10j))
    assert ae(v, (-20.880034621432138988 + 1.3258176641336937524j), tol=ATOL)
    assert ae(v.real, -20.880034621432138988, tol=PTOL)
    assert ae(v.imag, 1.3258176641336937524, tol=PTOL)
    v = fp.ei((0.25 + 1.0j))
    assert ae(v, (0.59066621214766308594 + 2.3968481059377428687j), tol=ATOL)
    assert ae(v.real, 0.59066621214766308594, tol=PTOL)
    assert ae(v.imag, 2.3968481059377428687, tol=PTOL)
    v = fp.ei((1.0 + 4.0j))
    assert ae(v, (-0.49739047283060471093 + 3.5570287076301818702j), tol=ATOL)
    assert ae(v.real, -0.49739047283060471093, tol=PTOL)
    assert ae(v.imag, 3.5570287076301818702, tol=PTOL)
    v = fp.ei((2.0 + 8.0j))
    assert ae(v, (0.8705211147733730969 + 3.3825859385758486351j), tol=ATOL)
    assert ae(v.real, 0.8705211147733730969, tol=PTOL)
    assert ae(v.imag, 3.3825859385758486351, tol=PTOL)
    v = fp.ei((5.0 + 20.0j))
    assert ae(v, (7.0789514293925893007 + 1.5313749363937141849j), tol=ATOL)
    assert ae(v.real, 7.0789514293925893007, tol=PTOL)
    assert ae(v.imag, 1.5313749363937141849, tol=PTOL)
    v = fp.ei((20.0 + 80.0j))
    assert ae(v, (-5855431.4907298084434 - 720917.79156143806727j), tol=ATOL)
    assert ae(v.real, -5855431.4907298084434, tol=PTOL)
    assert ae(v.imag, -720917.79156143806727, tol=PTOL)
    v = fp.ei((30.0 + 120.0j))
    assert ae(v, (65402491644.703470747 - 56697658396.51586764j), tol=ATOL)
    assert ae(v.real, 65402491644.703470747, tol=PTOL)
    assert ae(v.imag, -56697658396.51586764, tol=PTOL)
    v = fp.ei((40.0 + 160.0j))
    assert ae(v, (-25504929379604.776769 + 1429035198630576.3879j), tol=ATOL)
    assert ae(v.real, -25504929379604.776769, tol=PTOL)
    assert ae(v.imag, 1429035198630576.3879, tol=PTOL)
    v = fp.ei((50.0 + 200.0j))
    assert ae(v, (-18437746526988116954.0 - 17146362239046152342.0j), tol=ATOL)
    assert ae(v.real, -18437746526988116954.0, tol=PTOL)
    assert ae(v.imag, -17146362239046152342.0, tol=PTOL)
    v = fp.ei((80.0 + 320.0j))
    assert ae(v, (-3.3464697299634526706e+31 - 1.6473152633843023919e+32j), tol=ATOL)
    assert ae(v.real, -3.3464697299634526706e+31, tol=PTOL)
    assert ae(v.imag, -1.6473152633843023919e+32, tol=PTOL)
    v = fp.ei((0.0 + 1.1641532182693481445e-10j))
    assert ae(v, (-22.29664129357666235 + 1.5707963269113119411j), tol=ATOL)
    assert ae(v.real, -22.29664129357666235, tol=PTOL)
    assert ae(v.imag, 1.5707963269113119411, tol=PTOL)
    v = fp.ei((0.0 + 0.25j))
    assert ae(v, (-0.82466306258094565309 + 1.8199298971146537833j), tol=ATOL)
    assert ae(v.real, -0.82466306258094565309, tol=PTOL)
    assert ae(v.imag, 1.8199298971146537833, tol=PTOL)
    v = fp.ei((0.0 + 1.0j))
    assert ae(v, (0.33740392290096813466 + 2.5168793971620796342j), tol=ATOL)
    assert ae(v.real, 0.33740392290096813466, tol=PTOL)
    assert ae(v.imag, 2.5168793971620796342, tol=PTOL)
    v = fp.ei((0.0 + 2.0j))
    assert ae(v, (0.4229808287748649957 + 3.1762093035975914678j), tol=ATOL)
    assert ae(v.real, 0.4229808287748649957, tol=PTOL)
    assert ae(v.imag, 3.1762093035975914678, tol=PTOL)
    v = fp.ei((0.0 + 5.0j))
    assert ae(v, (-0.19002974965664387862 + 3.1207275717395707565j), tol=ATOL)
    assert ae(v.real, -0.19002974965664387862, tol=PTOL)
    assert ae(v.imag, 3.1207275717395707565, tol=PTOL)
    v = fp.ei((0.0 + 20.0j))
    assert ae(v, (0.04441982084535331654 + 3.1190380278383364594j), tol=ATOL)
    assert ae(v.real, 0.04441982084535331654, tol=PTOL)
    assert ae(v.imag, 3.1190380278383364594, tol=PTOL)
    v = fp.ei((0.0 + 30.0j))
    assert ae(v, (-0.033032417282071143779 + 3.1375528668252477302j), tol=ATOL)
    assert ae(v.real, -0.033032417282071143779, tol=PTOL)
    assert ae(v.imag, 3.1375528668252477302, tol=PTOL)
    v = fp.ei((0.0 + 40.0j))
    assert ae(v, (0.019020007896208766962 + 3.157781446149681126j), tol=ATOL)
    assert ae(v.real, 0.019020007896208766962, tol=PTOL)
    assert ae(v.imag, 3.157781446149681126, tol=PTOL)
    v = fp.ei((0.0 + 50.0j))
    assert ae(v, (-0.0056283863241163054402 + 3.122413399280832514j), tol=ATOL)
    assert ae(v.real, -0.0056283863241163054402, tol=PTOL)
    assert ae(v.imag, 3.122413399280832514, tol=PTOL)
    v = fp.ei((0.0 + 80.0j))
    assert ae(v, (-0.012402501155070958192 + 3.1431272137073839346j), tol=ATOL)
    assert ae(v.real, -0.012402501155070958192, tol=PTOL)
    assert ae(v.imag, 3.1431272137073839346, tol=PTOL)
    v = fp.ei((-1.1641532182693481445e-10 + 4.6566128730773925781e-10j))
    assert ae(v, (-20.880034621664969632 + 1.8157749903874220607j), tol=ATOL)
    assert ae(v.real, -20.880034621664969632, tol=PTOL)
    assert ae(v.imag, 1.8157749903874220607, tol=PTOL)
    v = fp.ei((-0.25 + 1.0j))
    assert ae(v, (0.16868306393667788761 + 2.6557914649950505414j), tol=ATOL)
    assert ae(v.real, 0.16868306393667788761, tol=PTOL)
    assert ae(v.imag, 2.6557914649950505414, tol=PTOL)
    v = fp.ei((-1.0 + 4.0j))
    assert ae(v, (-0.03373591813926547318 + 3.2151161058308770603j), tol=ATOL)
    assert ae(v.real, -0.03373591813926547318, tol=PTOL)
    assert ae(v.imag, 3.2151161058308770603, tol=PTOL)
    v = fp.ei((-2.0 + 8.0j))
    assert ae(v, (0.015392833434733785143 + 3.1384179414340326969j), tol=ATOL)
    assert ae(v.real, 0.015392833434733785143, tol=PTOL)
    assert ae(v.imag, 3.1384179414340326969, tol=PTOL)
    v = fp.ei((-5.0 + 20.0j))
    assert ae(v, (0.00024419662286542966525 + 3.1413825703601317109j), tol=ATOL)
    assert ae(v.real, 0.00024419662286542966525, tol=PTOL)
    assert ae(v.imag, 3.1413825703601317109, tol=PTOL)
    v = fp.ei((-20.0 + 80.0j))
    assert ae(v, (-2.3255552781051330088e-11 + 3.1415926535987396304j), tol=ATOL)
    assert ae(v.real, -2.3255552781051330088e-11, tol=PTOL)
    assert ae(v.imag, 3.1415926535987396304, tol=PTOL)
    v = fp.ei((-30.0 + 120.0j))
    assert ae(v, (2.7068919097124652332e-16 + 3.1415926535897925337j), tol=ATOL)
    assert ae(v.real, 2.7068919097124652332e-16, tol=PTOL)
    assert ae(v.imag, 3.1415926535897925337, tol=PTOL)
    v = fp.ei((-40.0 + 160.0j))
    assert ae(v, (1.1695597827678024687e-20 + 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, 1.1695597827678024687e-20, tol=PTOL)
    assert ae(v.imag, 3.1415926535897932385, tol=PTOL)
    v = fp.ei((-50.0 + 200.0j))
    assert ae(v, (-9.0323746914410162531e-25 + 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -9.0323746914410162531e-25, tol=PTOL)
    assert ae(v.imag, 3.1415926535897932385, tol=PTOL)
    v = fp.ei((-80.0 + 320.0j))
    assert ae(v, (-3.4819106748728063576e-38 + 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -3.4819106748728063576e-38, tol=PTOL)
    assert ae(v.imag, 3.1415926535897932385, tol=PTOL)
    v = fp.ei((-4.6566128730773925781e-10 + 1.1641532182693481445e-10j))
    assert ae(v, (-20.880034622014215597 + 2.8966139905793444061j), tol=ATOL)
    assert ae(v.real, -20.880034622014215597, tol=PTOL)
    assert ae(v.imag, 2.8966139905793444061, tol=PTOL)
    v = fp.ei((-1.0 + 0.25j))
    assert ae(v, (-0.19731063945004229095 + 3.0542266078154932748j), tol=ATOL)
    assert ae(v.real, -0.19731063945004229095, tol=PTOL)
    assert ae(v.imag, 3.0542266078154932748, tol=PTOL)
    v = fp.ei((-4.0 + 1.0j))
    assert ae(v, (-0.0013106173980145506944 + 3.1381384055698581758j), tol=ATOL)
    assert ae(v.real, -0.0013106173980145506944, tol=PTOL)
    assert ae(v.imag, 3.1381384055698581758, tol=PTOL)
    v = fp.ei((-8.0 + 2.0j))
    assert ae(v, (0.000022278049065270225945 + 3.1415634616493367169j), tol=ATOL)
    assert ae(v.real, 0.000022278049065270225945, tol=PTOL)
    assert ae(v.imag, 3.1415634616493367169, tol=PTOL)
    v = fp.ei((-20.0 + 5.0j))
    assert ae(v, (-4.7711374515765346894e-11 + 3.1415926536726958909j), tol=ATOL)
    assert ae(v.real, -4.7711374515765346894e-11, tol=PTOL)
    assert ae(v.imag, 3.1415926536726958909, tol=PTOL)
    v = fp.ei((-80.0 + 20.0j))
    assert ae(v, (-3.8353473865788235787e-38 + 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -3.8353473865788235787e-38, tol=PTOL)
    assert ae(v.imag, 3.1415926535897932385, tol=PTOL)
    v = fp.ei((-120.0 + 30.0j))
    assert ae(v, (-2.3836002337480334716e-55 + 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -2.3836002337480334716e-55, tol=PTOL)
    assert ae(v.imag, 3.1415926535897932385, tol=PTOL)
    v = fp.ei((-160.0 + 40.0j))
    assert ae(v, (1.6238022898654510661e-72 + 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, 1.6238022898654510661e-72, tol=PTOL)
    assert ae(v.imag, 3.1415926535897932385, tol=PTOL)
    v = fp.ei((-200.0 + 50.0j))
    assert ae(v, (-6.6800061461666228487e-90 + 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -6.6800061461666228487e-90, tol=PTOL)
    assert ae(v.imag, 3.1415926535897932385, tol=PTOL)
    v = fp.ei((-320.0 + 80.0j))
    assert ae(v, (-4.2737871527778786157e-143 + 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -4.2737871527778786157e-143, tol=PTOL)
    assert ae(v.imag, 3.1415926535897932385, tol=PTOL)
    v = fp.ei(-1.1641532182693481445e-10)
    assert ae(v, -22.296641293693077672, tol=ATOL)
    assert type(v) is float
    v = fp.ei(-0.25)
    assert ae(v, -1.0442826344437381945, tol=ATOL)
    assert type(v) is float
    v = fp.ei(-1.0)
    assert ae(v, -0.21938393439552027368, tol=ATOL)
    assert type(v) is float
    v = fp.ei(-2.0)
    assert ae(v, -0.048900510708061119567, tol=ATOL)
    assert type(v) is float
    v = fp.ei(-5.0)
    assert ae(v, -0.0011482955912753257973, tol=ATOL)
    assert type(v) is float
    v = fp.ei(-20.0)
    assert ae(v, -9.8355252906498816904e-11, tol=ATOL)
    assert type(v) is float
    v = fp.ei(-30.0)
    assert ae(v, -3.0215520106888125448e-15, tol=ATOL)
    assert type(v) is float
    v = fp.ei(-40.0)
    assert ae(v, -1.0367732614516569722e-19, tol=ATOL)
    assert type(v) is float
    v = fp.ei(-50.0)
    assert ae(v, -3.7832640295504590187e-24, tol=ATOL)
    assert type(v) is float
    v = fp.ei(-80.0)
    assert ae(v, -2.2285432586884729112e-37, tol=ATOL)
    assert type(v) is float
    v = fp.ei((-1.1641532182693481445e-10 + 0.0j))
    assert ae(v, (-22.296641293693077672 + 0.0j), tol=ATOL)
    assert ae(v.real, -22.296641293693077672, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((-0.25 + 0.0j))
    assert ae(v, (-1.0442826344437381945 + 0.0j), tol=ATOL)
    assert ae(v.real, -1.0442826344437381945, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((-1.0 + 0.0j))
    assert ae(v, (-0.21938393439552027368 + 0.0j), tol=ATOL)
    assert ae(v.real, -0.21938393439552027368, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((-2.0 + 0.0j))
    assert ae(v, (-0.048900510708061119567 + 0.0j), tol=ATOL)
    assert ae(v.real, -0.048900510708061119567, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((-5.0 + 0.0j))
    assert ae(v, (-0.0011482955912753257973 + 0.0j), tol=ATOL)
    assert ae(v.real, -0.0011482955912753257973, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((-20.0 + 0.0j))
    assert ae(v, (-9.8355252906498816904e-11 + 0.0j), tol=ATOL)
    assert ae(v.real, -9.8355252906498816904e-11, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((-30.0 + 0.0j))
    assert ae(v, (-3.0215520106888125448e-15 + 0.0j), tol=ATOL)
    assert ae(v.real, -3.0215520106888125448e-15, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((-40.0 + 0.0j))
    assert ae(v, (-1.0367732614516569722e-19 + 0.0j), tol=ATOL)
    assert ae(v.real, -1.0367732614516569722e-19, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((-50.0 + 0.0j))
    assert ae(v, (-3.7832640295504590187e-24 + 0.0j), tol=ATOL)
    assert ae(v.real, -3.7832640295504590187e-24, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((-80.0 + 0.0j))
    assert ae(v, (-2.2285432586884729112e-37 + 0.0j), tol=ATOL)
    assert ae(v.real, -2.2285432586884729112e-37, tol=PTOL)
    assert v.imag == 0
    v = fp.ei((-4.6566128730773925781e-10 - 1.1641532182693481445e-10j))
    assert ae(v, (-20.880034622014215597 - 2.8966139905793444061j), tol=ATOL)
    assert ae(v.real, -20.880034622014215597, tol=PTOL)
    assert ae(v.imag, -2.8966139905793444061, tol=PTOL)
    v = fp.ei((-1.0 - 0.25j))
    assert ae(v, (-0.19731063945004229095 - 3.0542266078154932748j), tol=ATOL)
    assert ae(v.real, -0.19731063945004229095, tol=PTOL)
    assert ae(v.imag, -3.0542266078154932748, tol=PTOL)
    v = fp.ei((-4.0 - 1.0j))
    assert ae(v, (-0.0013106173980145506944 - 3.1381384055698581758j), tol=ATOL)
    assert ae(v.real, -0.0013106173980145506944, tol=PTOL)
    assert ae(v.imag, -3.1381384055698581758, tol=PTOL)
    v = fp.ei((-8.0 - 2.0j))
    assert ae(v, (0.000022278049065270225945 - 3.1415634616493367169j), tol=ATOL)
    assert ae(v.real, 0.000022278049065270225945, tol=PTOL)
    assert ae(v.imag, -3.1415634616493367169, tol=PTOL)
    v = fp.ei((-20.0 - 5.0j))
    assert ae(v, (-4.7711374515765346894e-11 - 3.1415926536726958909j), tol=ATOL)
    assert ae(v.real, -4.7711374515765346894e-11, tol=PTOL)
    assert ae(v.imag, -3.1415926536726958909, tol=PTOL)
    v = fp.ei((-80.0 - 20.0j))
    assert ae(v, (-3.8353473865788235787e-38 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -3.8353473865788235787e-38, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.ei((-120.0 - 30.0j))
    assert ae(v, (-2.3836002337480334716e-55 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -2.3836002337480334716e-55, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.ei((-160.0 - 40.0j))
    assert ae(v, (1.6238022898654510661e-72 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, 1.6238022898654510661e-72, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.ei((-200.0 - 50.0j))
    assert ae(v, (-6.6800061461666228487e-90 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -6.6800061461666228487e-90, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.ei((-320.0 - 80.0j))
    assert ae(v, (-4.2737871527778786157e-143 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -4.2737871527778786157e-143, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.ei((-1.1641532182693481445e-10 - 1.1641532182693481445e-10j))
    assert ae(v, (-21.950067703413105017 - 2.3561944903087602507j), tol=ATOL)
    assert ae(v.real, -21.950067703413105017, tol=PTOL)
    assert ae(v.imag, -2.3561944903087602507, tol=PTOL)
    v = fp.ei((-0.25 - 0.25j))
    assert ae(v, (-0.71092525792923287894 - 2.5766745291767512913j), tol=ATOL)
    assert ae(v.real, -0.71092525792923287894, tol=PTOL)
    assert ae(v.imag, -2.5766745291767512913, tol=PTOL)
    v = fp.ei((-1.0 - 1.0j))
    assert ae(v, (-0.00028162445198141832551 - 2.9622681185504342983j), tol=ATOL)
    assert ae(v.real, -0.00028162445198141832551, tol=PTOL)
    assert ae(v.imag, -2.9622681185504342983, tol=PTOL)
    v = fp.ei((-2.0 - 2.0j))
    assert ae(v, (0.033767089606562004246 - 3.1229932394200426965j), tol=ATOL)
    assert ae(v.real, 0.033767089606562004246, tol=PTOL)
    assert ae(v.imag, -3.1229932394200426965, tol=PTOL)
    v = fp.ei((-5.0 - 5.0j))
    assert ae(v, (-0.0007266506660356393891 - 3.1420636813914284609j), tol=ATOL)
    assert ae(v.real, -0.0007266506660356393891, tol=PTOL)
    assert ae(v.imag, -3.1420636813914284609, tol=PTOL)
    v = fp.ei((-20.0 - 20.0j))
    assert ae(v, (2.3824537449367396579e-11 - 3.1415926535228233653j), tol=ATOL)
    assert ae(v.real, 2.3824537449367396579e-11, tol=PTOL)
    assert ae(v.imag, -3.1415926535228233653, tol=PTOL)
    v = fp.ei((-30.0 - 30.0j))
    assert ae(v, (-1.7316045841744061617e-15 - 3.141592653589794545j), tol=ATOL)
    assert ae(v.real, -1.7316045841744061617e-15, tol=PTOL)
    assert ae(v.imag, -3.141592653589794545, tol=PTOL)
    v = fp.ei((-40.0 - 40.0j))
    assert ae(v, (7.4001043002899232182e-20 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, 7.4001043002899232182e-20, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.ei((-50.0 - 50.0j))
    assert ae(v, (-2.3566128324644641219e-24 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -2.3566128324644641219e-24, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.ei((-80.0 - 80.0j))
    assert ae(v, (-9.8279750572186526673e-38 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -9.8279750572186526673e-38, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.ei((-1.1641532182693481445e-10 - 4.6566128730773925781e-10j))
    assert ae(v, (-20.880034621664969632 - 1.8157749903874220607j), tol=ATOL)
    assert ae(v.real, -20.880034621664969632, tol=PTOL)
    assert ae(v.imag, -1.8157749903874220607, tol=PTOL)
    v = fp.ei((-0.25 - 1.0j))
    assert ae(v, (0.16868306393667788761 - 2.6557914649950505414j), tol=ATOL)
    assert ae(v.real, 0.16868306393667788761, tol=PTOL)
    assert ae(v.imag, -2.6557914649950505414, tol=PTOL)
    v = fp.ei((-1.0 - 4.0j))
    assert ae(v, (-0.03373591813926547318 - 3.2151161058308770603j), tol=ATOL)
    assert ae(v.real, -0.03373591813926547318, tol=PTOL)
    assert ae(v.imag, -3.2151161058308770603, tol=PTOL)
    v = fp.ei((-2.0 - 8.0j))
    assert ae(v, (0.015392833434733785143 - 3.1384179414340326969j), tol=ATOL)
    assert ae(v.real, 0.015392833434733785143, tol=PTOL)
    assert ae(v.imag, -3.1384179414340326969, tol=PTOL)
    v = fp.ei((-5.0 - 20.0j))
    assert ae(v, (0.00024419662286542966525 - 3.1413825703601317109j), tol=ATOL)
    assert ae(v.real, 0.00024419662286542966525, tol=PTOL)
    assert ae(v.imag, -3.1413825703601317109, tol=PTOL)
    v = fp.ei((-20.0 - 80.0j))
    assert ae(v, (-2.3255552781051330088e-11 - 3.1415926535987396304j), tol=ATOL)
    assert ae(v.real, -2.3255552781051330088e-11, tol=PTOL)
    assert ae(v.imag, -3.1415926535987396304, tol=PTOL)
    v = fp.ei((-30.0 - 120.0j))
    assert ae(v, (2.7068919097124652332e-16 - 3.1415926535897925337j), tol=ATOL)
    assert ae(v.real, 2.7068919097124652332e-16, tol=PTOL)
    assert ae(v.imag, -3.1415926535897925337, tol=PTOL)
    v = fp.ei((-40.0 - 160.0j))
    assert ae(v, (1.1695597827678024687e-20 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, 1.1695597827678024687e-20, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.ei((-50.0 - 200.0j))
    assert ae(v, (-9.0323746914410162531e-25 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -9.0323746914410162531e-25, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.ei((-80.0 - 320.0j))
    assert ae(v, (-3.4819106748728063576e-38 - 3.1415926535897932385j), tol=ATOL)
    assert ae(v.real, -3.4819106748728063576e-38, tol=PTOL)
    assert ae(v.imag, -3.1415926535897932385, tol=PTOL)
    v = fp.ei((0.0 - 1.1641532182693481445e-10j))
    assert ae(v, (-22.29664129357666235 - 1.5707963269113119411j), tol=ATOL)
    assert ae(v.real, -22.29664129357666235, tol=PTOL)
    assert ae(v.imag, -1.5707963269113119411, tol=PTOL)
    v = fp.ei((0.0 - 0.25j))
    assert ae(v, (-0.82466306258094565309 - 1.8199298971146537833j), tol=ATOL)
    assert ae(v.real, -0.82466306258094565309, tol=PTOL)
    assert ae(v.imag, -1.8199298971146537833, tol=PTOL)
    v = fp.ei((0.0 - 1.0j))
    assert ae(v, (0.33740392290096813466 - 2.5168793971620796342j), tol=ATOL)
    assert ae(v.real, 0.33740392290096813466, tol=PTOL)
    assert ae(v.imag, -2.5168793971620796342, tol=PTOL)
    v = fp.ei((0.0 - 2.0j))
    assert ae(v, (0.4229808287748649957 - 3.1762093035975914678j), tol=ATOL)
    assert ae(v.real, 0.4229808287748649957, tol=PTOL)
    assert ae(v.imag, -3.1762093035975914678, tol=PTOL)
    v = fp.ei((0.0 - 5.0j))
    assert ae(v, (-0.19002974965664387862 - 3.1207275717395707565j), tol=ATOL)
    assert ae(v.real, -0.19002974965664387862, tol=PTOL)
    assert ae(v.imag, -3.1207275717395707565, tol=PTOL)
    v = fp.ei((0.0 - 20.0j))
    assert ae(v, (0.04441982084535331654 - 3.1190380278383364594j), tol=ATOL)
    assert ae(v.real, 0.04441982084535331654, tol=PTOL)
    assert ae(v.imag, -3.1190380278383364594, tol=PTOL)
    v = fp.ei((0.0 - 30.0j))
    assert ae(v, (-0.033032417282071143779 - 3.1375528668252477302j), tol=ATOL)
    assert ae(v.real, -0.033032417282071143779, tol=PTOL)
    assert ae(v.imag, -3.1375528668252477302, tol=PTOL)
    v = fp.ei((0.0 - 40.0j))
    assert ae(v, (0.019020007896208766962 - 3.157781446149681126j), tol=ATOL)
    assert ae(v.real, 0.019020007896208766962, tol=PTOL)
    assert ae(v.imag, -3.157781446149681126, tol=PTOL)
    v = fp.ei((0.0 - 50.0j))
    assert ae(v, (-0.0056283863241163054402 - 3.122413399280832514j), tol=ATOL)
    assert ae(v.real, -0.0056283863241163054402, tol=PTOL)
    assert ae(v.imag, -3.122413399280832514, tol=PTOL)
    v = fp.ei((0.0 - 80.0j))
    assert ae(v, (-0.012402501155070958192 - 3.1431272137073839346j), tol=ATOL)
    assert ae(v.real, -0.012402501155070958192, tol=PTOL)
    assert ae(v.imag, -3.1431272137073839346, tol=PTOL)
    v = fp.ei((1.1641532182693481445e-10 - 4.6566128730773925781e-10j))
    assert ae(v, (-20.880034621432138988 - 1.3258176641336937524j), tol=ATOL)
    assert ae(v.real, -20.880034621432138988, tol=PTOL)
    assert ae(v.imag, -1.3258176641336937524, tol=PTOL)
    v = fp.ei((0.25 - 1.0j))
    assert ae(v, (0.59066621214766308594 - 2.3968481059377428687j), tol=ATOL)
    assert ae(v.real, 0.59066621214766308594, tol=PTOL)
    assert ae(v.imag, -2.3968481059377428687, tol=PTOL)
    v = fp.ei((1.0 - 4.0j))
    assert ae(v, (-0.49739047283060471093 - 3.5570287076301818702j), tol=ATOL)
    assert ae(v.real, -0.49739047283060471093, tol=PTOL)
    assert ae(v.imag, -3.5570287076301818702, tol=PTOL)
    v = fp.ei((2.0 - 8.0j))
    assert ae(v, (0.8705211147733730969 - 3.3825859385758486351j), tol=ATOL)
    assert ae(v.real, 0.8705211147733730969, tol=PTOL)
    assert ae(v.imag, -3.3825859385758486351, tol=PTOL)
    v = fp.ei((5.0 - 20.0j))
    assert ae(v, (7.0789514293925893007 - 1.5313749363937141849j), tol=ATOL)
    assert ae(v.real, 7.0789514293925893007, tol=PTOL)
    assert ae(v.imag, -1.5313749363937141849, tol=PTOL)
    v = fp.ei((20.0 - 80.0j))
    assert ae(v, (-5855431.4907298084434 + 720917.79156143806727j), tol=ATOL)
    assert ae(v.real, -5855431.4907298084434, tol=PTOL)
    assert ae(v.imag, 720917.79156143806727, tol=PTOL)
    v = fp.ei((30.0 - 120.0j))
    assert ae(v, (65402491644.703470747 + 56697658396.51586764j), tol=ATOL)
    assert ae(v.real, 65402491644.703470747, tol=PTOL)
    assert ae(v.imag, 56697658396.51586764, tol=PTOL)
    v = fp.ei((40.0 - 160.0j))
    assert ae(v, (-25504929379604.776769 - 1429035198630576.3879j), tol=ATOL)
    assert ae(v.real, -25504929379604.776769, tol=PTOL)
    assert ae(v.imag, -1429035198630576.3879, tol=PTOL)
    v = fp.ei((50.0 - 200.0j))
    assert ae(v, (-18437746526988116954.0 + 17146362239046152342.0j), tol=ATOL)
    assert ae(v.real, -18437746526988116954.0, tol=PTOL)
    assert ae(v.imag, 17146362239046152342.0, tol=PTOL)
    v = fp.ei((80.0 - 320.0j))
    assert ae(v, (-3.3464697299634526706e+31 + 1.6473152633843023919e+32j), tol=ATOL)
    assert ae(v.real, -3.3464697299634526706e+31, tol=PTOL)
    assert ae(v.imag, 1.6473152633843023919e+32, tol=PTOL)
    v = fp.ei((1.1641532182693481445e-10 - 1.1641532182693481445e-10j))
    assert ae(v, (-21.950067703180274374 - 0.78539816351386363145j), tol=ATOL)
    assert ae(v.real, -21.950067703180274374, tol=PTOL)
    assert ae(v.imag, -0.78539816351386363145, tol=PTOL)
    v = fp.ei((0.25 - 0.25j))
    assert ae(v, (-0.21441047326710323254 - 1.0683772981589995996j), tol=ATOL)
    assert ae(v.real, -0.21441047326710323254, tol=PTOL)
    assert ae(v.imag, -1.0683772981589995996, tol=PTOL)
    v = fp.ei((1.0 - 1.0j))
    assert ae(v, (1.7646259855638540684 - 2.3877698515105224193j), tol=ATOL)
    assert ae(v.real, 1.7646259855638540684, tol=PTOL)
    assert ae(v.imag, -2.3877698515105224193, tol=PTOL)
    v = fp.ei((2.0 - 2.0j))
    assert ae(v, (1.8920781621855474089 - 5.3169624378326579621j), tol=ATOL)
    assert ae(v.real, 1.8920781621855474089, tol=PTOL)
    assert ae(v.imag, -5.3169624378326579621, tol=PTOL)
    v = fp.ei((5.0 - 5.0j))
    assert ae(v, (-13.470936071475245856 + 15.322492395731230968j), tol=ATOL)
    assert ae(v.real, -13.470936071475245856, tol=PTOL)
    assert ae(v.imag, 15.322492395731230968, tol=PTOL)
    v = fp.ei((20.0 - 20.0j))
    assert ae(v, (16589317.398788971896 - 5831705.4712368307104j), tol=ATOL)
    assert ae(v.real, 16589317.398788971896, tol=PTOL)
    assert ae(v.imag, -5831705.4712368307104, tol=PTOL)
    v = fp.ei((30.0 - 30.0j))
    assert ae(v, (-154596484273.69322527 + 204179357834.2723043j), tol=ATOL)
    assert ae(v.real, -154596484273.69322527, tol=PTOL)
    assert ae(v.imag, 204179357834.2723043, tol=PTOL)
    v = fp.ei((40.0 - 40.0j))
    assert ae(v, (287512180321448.45408 - 4203502407932318.1156j), tol=ATOL)
    assert ae(v.real, 287512180321448.45408, tol=PTOL)
    assert ae(v.imag, -4203502407932318.1156, tol=PTOL)
    v = fp.ei((50.0 - 50.0j))
    assert ae(v, (36128528616649268826.0 + 64648801861338741960.0j), tol=ATOL)
    assert ae(v.real, 36128528616649268826.0, tol=PTOL)
    assert ae(v.imag, 64648801861338741960.0, tol=PTOL)
    v = fp.ei((80.0 - 80.0j))
    assert ae(v, (-3.8674816337930010217e+32 + 3.0540709639658071041e+32j), tol=ATOL)
    assert ae(v.real, -3.8674816337930010217e+32, tol=PTOL)
    assert ae(v.imag, 3.0540709639658071041e+32, tol=PTOL)
    v = fp.ei((4.6566128730773925781e-10 - 1.1641532182693481445e-10j))
    assert ae(v, (-20.880034621082893023 - 0.24497866324327947603j), tol=ATOL)
    assert ae(v.real, -20.880034621082893023, tol=PTOL)
    assert ae(v.imag, -0.24497866324327947603, tol=PTOL)
    v = fp.ei((1.0 - 0.25j))
    assert ae(v, (1.8942716983721074932 - 0.67268237088273915854j), tol=ATOL)
    assert ae(v.real, 1.8942716983721074932, tol=PTOL)
    assert ae(v.imag, -0.67268237088273915854, tol=PTOL)
    v = fp.ei((4.0 - 1.0j))
    assert ae(v, (14.806699492675420438 - 12.280015176673582616j), tol=ATOL)
    assert ae(v.real, 14.806699492675420438, tol=PTOL)
    assert ae(v.imag, -12.280015176673582616, tol=PTOL)
    v = fp.ei((8.0 - 2.0j))
    assert ae(v, (-54.633252667426386294 - 416.34477429173650012j), tol=ATOL)
    assert ae(v.real, -54.633252667426386294, tol=PTOL)
    assert ae(v.imag, -416.34477429173650012, tol=PTOL)
    v = fp.ei((20.0 - 5.0j))
    assert ae(v, (711836.97165402624643 + 24745247.798103247366j), tol=ATOL)
    assert ae(v.real, 711836.97165402624643, tol=PTOL)
    assert ae(v.imag, 24745247.798103247366, tol=PTOL)
    v = fp.ei((80.0 - 20.0j))
    assert ae(v, (4.2139911108612653091e+32 - 5.3367124741918251637e+32j), tol=ATOL)
    assert ae(v.real, 4.2139911108612653091e+32, tol=PTOL)
    assert ae(v.imag, -5.3367124741918251637e+32, tol=PTOL)
    v = fp.ei((120.0 - 30.0j))
    assert ae(v, (-9.7760616203707508892e+48 + 1.058257682317195792e+50j), tol=ATOL)
    assert ae(v.real, -9.7760616203707508892e+48, tol=PTOL)
    assert ae(v.imag, 1.058257682317195792e+50, tol=PTOL)
    v = fp.ei((160.0 - 40.0j))
    assert ae(v, (-8.7065541466623638861e+66 - 1.6577106725141739889e+67j), tol=ATOL)
    assert ae(v.real, -8.7065541466623638861e+66, tol=PTOL)
    assert ae(v.imag, -1.6577106725141739889e+67, tol=PTOL)
    v = fp.ei((200.0 - 50.0j))
    assert ae(v, (3.070744996327018106e+84 + 1.7243244846769415903e+84j), tol=ATOL)
    assert ae(v.real, 3.070744996327018106e+84, tol=PTOL)
    assert ae(v.imag, 1.7243244846769415903e+84, tol=PTOL)
    v = fp.ei((320.0 - 80.0j))
    assert ae(v, (-9.9960598637998647276e+135 + 2.6855081527595608863e+136j), tol=ATOL)
    assert ae(v.real, -9.9960598637998647276e+135, tol=PTOL)
    assert ae(v.imag, 2.6855081527595608863e+136, tol=PTOL)
