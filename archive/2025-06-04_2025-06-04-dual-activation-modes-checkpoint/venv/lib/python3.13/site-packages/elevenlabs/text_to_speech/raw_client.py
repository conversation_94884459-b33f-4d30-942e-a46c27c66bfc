# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import contextlib
import json
import typing
from json.decoder import <PERSON><PERSON>NDecode<PERSON>rror

from ..core.api_error import ApiError
from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.http_response import Async<PERSON>ttpR<PERSON>ponse, HttpResponse
from ..core.jsonable_encoder import jsonable_encoder
from ..core.request_options import RequestOptions
from ..core.serialization import convert_and_respect_annotation_metadata
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.audio_with_timestamps_response import AudioWithTimestampsResponse
from ..types.http_validation_error import HttpValidationError
from ..types.pronunciation_dictionary_version_locator import PronunciationDictionaryVersionLocator
from ..types.streaming_audio_chunk_with_timestamps_response import StreamingAudioChunkWithTimestampsResponse
from ..types.voice_settings import VoiceSettings
from .types.body_text_to_speech_streaming_v_1_text_to_speech_voice_id_stream_post_apply_text_normalization import (
    BodyTextToSpeechStreamingV1TextToSpeechVoiceIdStreamPostApplyTextNormalization,
)
from .types.body_text_to_speech_streaming_with_timestamps_v_1_text_to_speech_voice_id_stream_with_timestamps_post_apply_text_normalization import (
    BodyTextToSpeechStreamingWithTimestampsV1TextToSpeechVoiceIdStreamWithTimestampsPostApplyTextNormalization,
)
from .types.body_text_to_speech_v_1_text_to_speech_voice_id_post_apply_text_normalization import (
    BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization,
)
from .types.body_text_to_speech_with_timestamps_v_1_text_to_speech_voice_id_with_timestamps_post_apply_text_normalization import (
    BodyTextToSpeechWithTimestampsV1TextToSpeechVoiceIdWithTimestampsPostApplyTextNormalization,
)
from .types.text_to_speech_convert_request_output_format import TextToSpeechConvertRequestOutputFormat
from .types.text_to_speech_convert_with_timestamps_request_output_format import (
    TextToSpeechConvertWithTimestampsRequestOutputFormat,
)
from .types.text_to_speech_stream_request_output_format import TextToSpeechStreamRequestOutputFormat
from .types.text_to_speech_stream_with_timestamps_request_output_format import (
    TextToSpeechStreamWithTimestampsRequestOutputFormat,
)

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawTextToSpeechClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    @contextlib.contextmanager
    def convert(
        self,
        voice_id: str,
        *,
        text: str,
        enable_logging: typing.Optional[bool] = None,
        optimize_streaming_latency: typing.Optional[int] = None,
        output_format: typing.Optional[TextToSpeechConvertRequestOutputFormat] = None,
        model_id: typing.Optional[str] = OMIT,
        language_code: typing.Optional[str] = OMIT,
        voice_settings: typing.Optional[VoiceSettings] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[
            typing.Sequence[PronunciationDictionaryVersionLocator]
        ] = OMIT,
        seed: typing.Optional[int] = OMIT,
        previous_text: typing.Optional[str] = OMIT,
        next_text: typing.Optional[str] = OMIT,
        previous_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        next_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        use_pvc_as_ivc: typing.Optional[bool] = OMIT,
        apply_text_normalization: typing.Optional[
            BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization
        ] = OMIT,
        apply_language_text_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[HttpResponse[typing.Iterator[bytes]]]:
        """
        Converts text into speech using a voice of your choice and returns audio.

        Parameters
        ----------
        voice_id : str
            ID of the voice to be used. Use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        text : str
            The text that will get converted into speech.

        enable_logging : typing.Optional[bool]
            When enable_logging is set to false zero retention mode will be used for the request. This will mean history features are unavailable for this request, including request stitching. Zero retention mode may only be used by enterprise customers.

        optimize_streaming_latency : typing.Optional[int]
            You can turn on latency optimizations at some cost of quality. The best possible final latency varies by model. Possible values:
            0 - default mode (no latency optimizations)
            1 - normal latency optimizations (about 50% of possible latency improvement of option 3)
            2 - strong latency optimizations (about 75% of possible latency improvement of option 3)
            3 - max latency optimizations
            4 - max latency optimizations, but also with text normalizer turned off for even more latency savings (best latency, but can mispronounce eg numbers and dates).

            Defaults to None.

        output_format : typing.Optional[TextToSpeechConvertRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        model_id : typing.Optional[str]
            Identifier of the model that will be used, you can query them using GET /v1/models. The model needs to have support for text to speech, you can check this using the can_do_text_to_speech property.

        language_code : typing.Optional[str]
            Language code (ISO 639-1) used to enforce a language for the model. Currently only Turbo v2.5 and Flash v2.5 support language enforcement. For other models, an error will be returned if language code is provided.

        voice_settings : typing.Optional[VoiceSettings]
            Voice settings overriding stored settings for the given voice. They are applied only on the given request.

        pronunciation_dictionary_locators : typing.Optional[typing.Sequence[PronunciationDictionaryVersionLocator]]
            A list of pronunciation dictionary locators (id, version_id) to be applied to the text. They will be applied in order. You may have up to 3 locators per request

        seed : typing.Optional[int]
            If specified, our system will make a best effort to sample deterministically, such that repeated requests with the same seed and parameters should return the same result. Determinism is not guaranteed. Must be integer between 0 and 4294967295.

        previous_text : typing.Optional[str]
            The text that came before the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        next_text : typing.Optional[str]
            The text that comes after the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        previous_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that were generated before this generation. Can be used to improve the speech's continuity when splitting up a large task into multiple requests. The results will be best when the same model is used across the generations. In case both previous_text and previous_request_ids is send, previous_text will be ignored. A maximum of 3 request_ids can be send.

        next_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that come after this generation. next_request_ids is especially useful for maintaining the speech's continuity when regenerating a sample that has had some audio quality issues. For example, if you have generated 3 speech clips, and you want to improve clip 2, passing the request id of clip 3 as a next_request_id (and that of clip 1 as a previous_request_id) will help maintain natural flow in the combined speech. The results will be best when the same model is used across the generations. In case both next_text and next_request_ids is send, next_text will be ignored. A maximum of 3 request_ids can be send.

        use_pvc_as_ivc : typing.Optional[bool]
            If true, we won't use PVC version of the voice for the generation but the IVC version. This is a temporary workaround for higher latency in PVC versions.

        apply_text_normalization : typing.Optional[BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization]
            This parameter controls text normalization with three modes: 'auto', 'on', and 'off'. When set to 'auto', the system will automatically decide whether to apply text normalization (e.g., spelling out numbers). With 'on', text normalization will always be applied, while with 'off', it will be skipped. Cannot be turned on for 'eleven_turbo_v2_5' or 'eleven_flash_v2_5' models.

        apply_language_text_normalization : typing.Optional[bool]
            This parameter controls language text normalization. This helps with proper pronunciation of text in some supported languages. WARNING: This parameter can heavily increase the latency of the request. Currently only supported for Japanese.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[HttpResponse[typing.Iterator[bytes]]]
            The generated audio file
        """
        with self._client_wrapper.httpx_client.stream(
            f"v1/text-to-speech/{jsonable_encoder(voice_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            params={
                "enable_logging": enable_logging,
                "optimize_streaming_latency": optimize_streaming_latency,
                "output_format": output_format,
            },
            json={
                "text": text,
                "model_id": model_id,
                "language_code": language_code,
                "voice_settings": convert_and_respect_annotation_metadata(
                    object_=voice_settings, annotation=VoiceSettings, direction="write"
                ),
                "pronunciation_dictionary_locators": convert_and_respect_annotation_metadata(
                    object_=pronunciation_dictionary_locators,
                    annotation=typing.Sequence[PronunciationDictionaryVersionLocator],
                    direction="write",
                ),
                "seed": seed,
                "previous_text": previous_text,
                "next_text": next_text,
                "previous_request_ids": previous_request_ids,
                "next_request_ids": next_request_ids,
                "use_pvc_as_ivc": use_pvc_as_ivc,
                "apply_text_normalization": apply_text_normalization,
                "apply_language_text_normalization": apply_language_text_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:

            def _stream() -> HttpResponse[typing.Iterator[bytes]]:
                try:
                    if 200 <= _response.status_code < 300:
                        _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                        return HttpResponse(
                            response=_response, data=(_chunk for _chunk in _response.iter_bytes(chunk_size=_chunk_size))
                        )
                    _response.read()
                    if _response.status_code == 422:
                        raise UnprocessableEntityError(
                            headers=dict(_response.headers),
                            body=typing.cast(
                                HttpValidationError,
                                construct_type(
                                    type_=HttpValidationError,  # type: ignore
                                    object_=_response.json(),
                                ),
                            ),
                        )
                    _response_json = _response.json()
                except JSONDecodeError:
                    raise ApiError(
                        status_code=_response.status_code, headers=dict(_response.headers), body=_response.text
                    )
                raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

            yield _stream()

    def convert_with_timestamps(
        self,
        voice_id: str,
        *,
        text: str,
        enable_logging: typing.Optional[bool] = None,
        optimize_streaming_latency: typing.Optional[int] = None,
        output_format: typing.Optional[TextToSpeechConvertWithTimestampsRequestOutputFormat] = None,
        model_id: typing.Optional[str] = OMIT,
        language_code: typing.Optional[str] = OMIT,
        voice_settings: typing.Optional[VoiceSettings] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[
            typing.Sequence[PronunciationDictionaryVersionLocator]
        ] = OMIT,
        seed: typing.Optional[int] = OMIT,
        previous_text: typing.Optional[str] = OMIT,
        next_text: typing.Optional[str] = OMIT,
        previous_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        next_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        use_pvc_as_ivc: typing.Optional[bool] = OMIT,
        apply_text_normalization: typing.Optional[
            BodyTextToSpeechWithTimestampsV1TextToSpeechVoiceIdWithTimestampsPostApplyTextNormalization
        ] = OMIT,
        apply_language_text_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[AudioWithTimestampsResponse]:
        """
        Generate speech from text with precise character-level timing information for audio-text synchronization.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        text : str
            The text that will get converted into speech.

        enable_logging : typing.Optional[bool]
            When enable_logging is set to false zero retention mode will be used for the request. This will mean history features are unavailable for this request, including request stitching. Zero retention mode may only be used by enterprise customers.

        optimize_streaming_latency : typing.Optional[int]
            You can turn on latency optimizations at some cost of quality. The best possible final latency varies by model. Possible values:
            0 - default mode (no latency optimizations)
            1 - normal latency optimizations (about 50% of possible latency improvement of option 3)
            2 - strong latency optimizations (about 75% of possible latency improvement of option 3)
            3 - max latency optimizations
            4 - max latency optimizations, but also with text normalizer turned off for even more latency savings (best latency, but can mispronounce eg numbers and dates).

            Defaults to None.

        output_format : typing.Optional[TextToSpeechConvertWithTimestampsRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        model_id : typing.Optional[str]
            Identifier of the model that will be used, you can query them using GET /v1/models. The model needs to have support for text to speech, you can check this using the can_do_text_to_speech property.

        language_code : typing.Optional[str]
            Language code (ISO 639-1) used to enforce a language for the model. Currently only Turbo v2.5 and Flash v2.5 support language enforcement. For other models, an error will be returned if language code is provided.

        voice_settings : typing.Optional[VoiceSettings]
            Voice settings overriding stored settings for the given voice. They are applied only on the given request.

        pronunciation_dictionary_locators : typing.Optional[typing.Sequence[PronunciationDictionaryVersionLocator]]
            A list of pronunciation dictionary locators (id, version_id) to be applied to the text. They will be applied in order. You may have up to 3 locators per request

        seed : typing.Optional[int]
            If specified, our system will make a best effort to sample deterministically, such that repeated requests with the same seed and parameters should return the same result. Determinism is not guaranteed. Must be integer between 0 and 4294967295.

        previous_text : typing.Optional[str]
            The text that came before the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        next_text : typing.Optional[str]
            The text that comes after the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        previous_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that were generated before this generation. Can be used to improve the speech's continuity when splitting up a large task into multiple requests. The results will be best when the same model is used across the generations. In case both previous_text and previous_request_ids is send, previous_text will be ignored. A maximum of 3 request_ids can be send.

        next_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that come after this generation. next_request_ids is especially useful for maintaining the speech's continuity when regenerating a sample that has had some audio quality issues. For example, if you have generated 3 speech clips, and you want to improve clip 2, passing the request id of clip 3 as a next_request_id (and that of clip 1 as a previous_request_id) will help maintain natural flow in the combined speech. The results will be best when the same model is used across the generations. In case both next_text and next_request_ids is send, next_text will be ignored. A maximum of 3 request_ids can be send.

        use_pvc_as_ivc : typing.Optional[bool]
            If true, we won't use PVC version of the voice for the generation but the IVC version. This is a temporary workaround for higher latency in PVC versions.

        apply_text_normalization : typing.Optional[BodyTextToSpeechWithTimestampsV1TextToSpeechVoiceIdWithTimestampsPostApplyTextNormalization]
            This parameter controls text normalization with three modes: 'auto', 'on', and 'off'. When set to 'auto', the system will automatically decide whether to apply text normalization (e.g., spelling out numbers). With 'on', text normalization will always be applied, while with 'off', it will be skipped. Cannot be turned on for 'eleven_turbo_v2_5' or 'eleven_flash_v2_5' models.

        apply_language_text_normalization : typing.Optional[bool]
            This parameter controls language text normalization. This helps with proper pronunciation of text in some supported languages. WARNING: This parameter can heavily increase the latency of the request. Currently only supported for Japanese.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[AudioWithTimestampsResponse]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/text-to-speech/{jsonable_encoder(voice_id)}/with-timestamps",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            params={
                "enable_logging": enable_logging,
                "optimize_streaming_latency": optimize_streaming_latency,
                "output_format": output_format,
            },
            json={
                "text": text,
                "model_id": model_id,
                "language_code": language_code,
                "voice_settings": convert_and_respect_annotation_metadata(
                    object_=voice_settings, annotation=VoiceSettings, direction="write"
                ),
                "pronunciation_dictionary_locators": convert_and_respect_annotation_metadata(
                    object_=pronunciation_dictionary_locators,
                    annotation=typing.Sequence[PronunciationDictionaryVersionLocator],
                    direction="write",
                ),
                "seed": seed,
                "previous_text": previous_text,
                "next_text": next_text,
                "previous_request_ids": previous_request_ids,
                "next_request_ids": next_request_ids,
                "use_pvc_as_ivc": use_pvc_as_ivc,
                "apply_text_normalization": apply_text_normalization,
                "apply_language_text_normalization": apply_language_text_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AudioWithTimestampsResponse,
                    construct_type(
                        type_=AudioWithTimestampsResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    @contextlib.contextmanager
    def stream(
        self,
        voice_id: str,
        *,
        text: str,
        enable_logging: typing.Optional[bool] = None,
        optimize_streaming_latency: typing.Optional[int] = None,
        output_format: typing.Optional[TextToSpeechStreamRequestOutputFormat] = None,
        model_id: typing.Optional[str] = OMIT,
        language_code: typing.Optional[str] = OMIT,
        voice_settings: typing.Optional[VoiceSettings] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[
            typing.Sequence[PronunciationDictionaryVersionLocator]
        ] = OMIT,
        seed: typing.Optional[int] = OMIT,
        previous_text: typing.Optional[str] = OMIT,
        next_text: typing.Optional[str] = OMIT,
        previous_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        next_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        use_pvc_as_ivc: typing.Optional[bool] = OMIT,
        apply_text_normalization: typing.Optional[
            BodyTextToSpeechStreamingV1TextToSpeechVoiceIdStreamPostApplyTextNormalization
        ] = OMIT,
        apply_language_text_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[HttpResponse[typing.Iterator[bytes]]]:
        """
        Converts text into speech using a voice of your choice and returns audio as an audio stream.

        Parameters
        ----------
        voice_id : str
            ID of the voice to be used. Use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        text : str
            The text that will get converted into speech.

        enable_logging : typing.Optional[bool]
            When enable_logging is set to false zero retention mode will be used for the request. This will mean history features are unavailable for this request, including request stitching. Zero retention mode may only be used by enterprise customers.

        optimize_streaming_latency : typing.Optional[int]
            You can turn on latency optimizations at some cost of quality. The best possible final latency varies by model. Possible values:
            0 - default mode (no latency optimizations)
            1 - normal latency optimizations (about 50% of possible latency improvement of option 3)
            2 - strong latency optimizations (about 75% of possible latency improvement of option 3)
            3 - max latency optimizations
            4 - max latency optimizations, but also with text normalizer turned off for even more latency savings (best latency, but can mispronounce eg numbers and dates).

            Defaults to None.

        output_format : typing.Optional[TextToSpeechStreamRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        model_id : typing.Optional[str]
            Identifier of the model that will be used, you can query them using GET /v1/models. The model needs to have support for text to speech, you can check this using the can_do_text_to_speech property.

        language_code : typing.Optional[str]
            Language code (ISO 639-1) used to enforce a language for the model. Currently only Turbo v2.5 and Flash v2.5 support language enforcement. For other models, an error will be returned if language code is provided.

        voice_settings : typing.Optional[VoiceSettings]
            Voice settings overriding stored settings for the given voice. They are applied only on the given request.

        pronunciation_dictionary_locators : typing.Optional[typing.Sequence[PronunciationDictionaryVersionLocator]]
            A list of pronunciation dictionary locators (id, version_id) to be applied to the text. They will be applied in order. You may have up to 3 locators per request

        seed : typing.Optional[int]
            If specified, our system will make a best effort to sample deterministically, such that repeated requests with the same seed and parameters should return the same result. Determinism is not guaranteed. Must be integer between 0 and 4294967295.

        previous_text : typing.Optional[str]
            The text that came before the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        next_text : typing.Optional[str]
            The text that comes after the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        previous_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that were generated before this generation. Can be used to improve the speech's continuity when splitting up a large task into multiple requests. The results will be best when the same model is used across the generations. In case both previous_text and previous_request_ids is send, previous_text will be ignored. A maximum of 3 request_ids can be send.

        next_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that come after this generation. next_request_ids is especially useful for maintaining the speech's continuity when regenerating a sample that has had some audio quality issues. For example, if you have generated 3 speech clips, and you want to improve clip 2, passing the request id of clip 3 as a next_request_id (and that of clip 1 as a previous_request_id) will help maintain natural flow in the combined speech. The results will be best when the same model is used across the generations. In case both next_text and next_request_ids is send, next_text will be ignored. A maximum of 3 request_ids can be send.

        use_pvc_as_ivc : typing.Optional[bool]
            If true, we won't use PVC version of the voice for the generation but the IVC version. This is a temporary workaround for higher latency in PVC versions.

        apply_text_normalization : typing.Optional[BodyTextToSpeechStreamingV1TextToSpeechVoiceIdStreamPostApplyTextNormalization]
            This parameter controls text normalization with three modes: 'auto', 'on', and 'off'. When set to 'auto', the system will automatically decide whether to apply text normalization (e.g., spelling out numbers). With 'on', text normalization will always be applied, while with 'off', it will be skipped. Cannot be turned on for 'eleven_turbo_v2_5' or 'eleven_flash_v2_5' models.

        apply_language_text_normalization : typing.Optional[bool]
            This parameter controls language text normalization. This helps with proper pronunciation of text in some supported languages. WARNING: This parameter can heavily increase the latency of the request. Currently only supported for Japanese.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[HttpResponse[typing.Iterator[bytes]]]
            Streaming audio data
        """
        with self._client_wrapper.httpx_client.stream(
            f"v1/text-to-speech/{jsonable_encoder(voice_id)}/stream",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            params={
                "enable_logging": enable_logging,
                "optimize_streaming_latency": optimize_streaming_latency,
                "output_format": output_format,
            },
            json={
                "text": text,
                "model_id": model_id,
                "language_code": language_code,
                "voice_settings": convert_and_respect_annotation_metadata(
                    object_=voice_settings, annotation=VoiceSettings, direction="write"
                ),
                "pronunciation_dictionary_locators": convert_and_respect_annotation_metadata(
                    object_=pronunciation_dictionary_locators,
                    annotation=typing.Sequence[PronunciationDictionaryVersionLocator],
                    direction="write",
                ),
                "seed": seed,
                "previous_text": previous_text,
                "next_text": next_text,
                "previous_request_ids": previous_request_ids,
                "next_request_ids": next_request_ids,
                "use_pvc_as_ivc": use_pvc_as_ivc,
                "apply_text_normalization": apply_text_normalization,
                "apply_language_text_normalization": apply_language_text_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:

            def _stream() -> HttpResponse[typing.Iterator[bytes]]:
                try:
                    if 200 <= _response.status_code < 300:
                        _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                        return HttpResponse(
                            response=_response, data=(_chunk for _chunk in _response.iter_bytes(chunk_size=_chunk_size))
                        )
                    _response.read()
                    if _response.status_code == 422:
                        raise UnprocessableEntityError(
                            headers=dict(_response.headers),
                            body=typing.cast(
                                HttpValidationError,
                                construct_type(
                                    type_=HttpValidationError,  # type: ignore
                                    object_=_response.json(),
                                ),
                            ),
                        )
                    _response_json = _response.json()
                except JSONDecodeError:
                    raise ApiError(
                        status_code=_response.status_code, headers=dict(_response.headers), body=_response.text
                    )
                raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

            yield _stream()

    @contextlib.contextmanager
    def stream_with_timestamps(
        self,
        voice_id: str,
        *,
        text: str,
        enable_logging: typing.Optional[bool] = None,
        optimize_streaming_latency: typing.Optional[int] = None,
        output_format: typing.Optional[TextToSpeechStreamWithTimestampsRequestOutputFormat] = None,
        model_id: typing.Optional[str] = OMIT,
        language_code: typing.Optional[str] = OMIT,
        voice_settings: typing.Optional[VoiceSettings] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[
            typing.Sequence[PronunciationDictionaryVersionLocator]
        ] = OMIT,
        seed: typing.Optional[int] = OMIT,
        previous_text: typing.Optional[str] = OMIT,
        next_text: typing.Optional[str] = OMIT,
        previous_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        next_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        use_pvc_as_ivc: typing.Optional[bool] = OMIT,
        apply_text_normalization: typing.Optional[
            BodyTextToSpeechStreamingWithTimestampsV1TextToSpeechVoiceIdStreamWithTimestampsPostApplyTextNormalization
        ] = OMIT,
        apply_language_text_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[HttpResponse[typing.Iterator[StreamingAudioChunkWithTimestampsResponse]]]:
        """
        Converts text into speech using a voice of your choice and returns a stream of JSONs containing audio as a base64 encoded string together with information on when which character was spoken.

        Parameters
        ----------
        voice_id : str
            ID of the voice to be used. Use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        text : str
            The text that will get converted into speech.

        enable_logging : typing.Optional[bool]
            When enable_logging is set to false zero retention mode will be used for the request. This will mean history features are unavailable for this request, including request stitching. Zero retention mode may only be used by enterprise customers.

        optimize_streaming_latency : typing.Optional[int]
            You can turn on latency optimizations at some cost of quality. The best possible final latency varies by model. Possible values:
            0 - default mode (no latency optimizations)
            1 - normal latency optimizations (about 50% of possible latency improvement of option 3)
            2 - strong latency optimizations (about 75% of possible latency improvement of option 3)
            3 - max latency optimizations
            4 - max latency optimizations, but also with text normalizer turned off for even more latency savings (best latency, but can mispronounce eg numbers and dates).

            Defaults to None.

        output_format : typing.Optional[TextToSpeechStreamWithTimestampsRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        model_id : typing.Optional[str]
            Identifier of the model that will be used, you can query them using GET /v1/models. The model needs to have support for text to speech, you can check this using the can_do_text_to_speech property.

        language_code : typing.Optional[str]
            Language code (ISO 639-1) used to enforce a language for the model. Currently only Turbo v2.5 and Flash v2.5 support language enforcement. For other models, an error will be returned if language code is provided.

        voice_settings : typing.Optional[VoiceSettings]
            Voice settings overriding stored settings for the given voice. They are applied only on the given request.

        pronunciation_dictionary_locators : typing.Optional[typing.Sequence[PronunciationDictionaryVersionLocator]]
            A list of pronunciation dictionary locators (id, version_id) to be applied to the text. They will be applied in order. You may have up to 3 locators per request

        seed : typing.Optional[int]
            If specified, our system will make a best effort to sample deterministically, such that repeated requests with the same seed and parameters should return the same result. Determinism is not guaranteed. Must be integer between 0 and 4294967295.

        previous_text : typing.Optional[str]
            The text that came before the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        next_text : typing.Optional[str]
            The text that comes after the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        previous_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that were generated before this generation. Can be used to improve the speech's continuity when splitting up a large task into multiple requests. The results will be best when the same model is used across the generations. In case both previous_text and previous_request_ids is send, previous_text will be ignored. A maximum of 3 request_ids can be send.

        next_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that come after this generation. next_request_ids is especially useful for maintaining the speech's continuity when regenerating a sample that has had some audio quality issues. For example, if you have generated 3 speech clips, and you want to improve clip 2, passing the request id of clip 3 as a next_request_id (and that of clip 1 as a previous_request_id) will help maintain natural flow in the combined speech. The results will be best when the same model is used across the generations. In case both next_text and next_request_ids is send, next_text will be ignored. A maximum of 3 request_ids can be send.

        use_pvc_as_ivc : typing.Optional[bool]
            If true, we won't use PVC version of the voice for the generation but the IVC version. This is a temporary workaround for higher latency in PVC versions.

        apply_text_normalization : typing.Optional[BodyTextToSpeechStreamingWithTimestampsV1TextToSpeechVoiceIdStreamWithTimestampsPostApplyTextNormalization]
            This parameter controls text normalization with three modes: 'auto', 'on', and 'off'. When set to 'auto', the system will automatically decide whether to apply text normalization (e.g., spelling out numbers). With 'on', text normalization will always be applied, while with 'off', it will be skipped. Cannot be turned on for 'eleven_turbo_v2_5' or 'eleven_flash_v2_5' models.

        apply_language_text_normalization : typing.Optional[bool]
            This parameter controls language text normalization. This helps with proper pronunciation of text in some supported languages. WARNING: This parameter can heavily increase the latency of the request. Currently only supported for Japanese.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Yields
        ------
        typing.Iterator[HttpResponse[typing.Iterator[StreamingAudioChunkWithTimestampsResponse]]]
            Stream of transcription chunks
        """
        with self._client_wrapper.httpx_client.stream(
            f"v1/text-to-speech/{jsonable_encoder(voice_id)}/stream/with-timestamps",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            params={
                "enable_logging": enable_logging,
                "optimize_streaming_latency": optimize_streaming_latency,
                "output_format": output_format,
            },
            json={
                "text": text,
                "model_id": model_id,
                "language_code": language_code,
                "voice_settings": convert_and_respect_annotation_metadata(
                    object_=voice_settings, annotation=VoiceSettings, direction="write"
                ),
                "pronunciation_dictionary_locators": convert_and_respect_annotation_metadata(
                    object_=pronunciation_dictionary_locators,
                    annotation=typing.Sequence[PronunciationDictionaryVersionLocator],
                    direction="write",
                ),
                "seed": seed,
                "previous_text": previous_text,
                "next_text": next_text,
                "previous_request_ids": previous_request_ids,
                "next_request_ids": next_request_ids,
                "use_pvc_as_ivc": use_pvc_as_ivc,
                "apply_text_normalization": apply_text_normalization,
                "apply_language_text_normalization": apply_language_text_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:

            def _stream() -> HttpResponse[typing.Iterator[StreamingAudioChunkWithTimestampsResponse]]:
                try:
                    if 200 <= _response.status_code < 300:

                        def _iter():
                            for _text in _response.iter_lines():
                                try:
                                    if len(_text) == 0:
                                        continue
                                    yield typing.cast(
                                        StreamingAudioChunkWithTimestampsResponse,
                                        construct_type(
                                            type_=StreamingAudioChunkWithTimestampsResponse,  # type: ignore
                                            object_=json.loads(_text),
                                        ),
                                    )
                                except Exception:
                                    pass
                            return

                        return HttpResponse(response=_response, data=_iter())
                    _response.read()
                    if _response.status_code == 422:
                        raise UnprocessableEntityError(
                            headers=dict(_response.headers),
                            body=typing.cast(
                                HttpValidationError,
                                construct_type(
                                    type_=HttpValidationError,  # type: ignore
                                    object_=_response.json(),
                                ),
                            ),
                        )
                    _response_json = _response.json()
                except JSONDecodeError:
                    raise ApiError(
                        status_code=_response.status_code, headers=dict(_response.headers), body=_response.text
                    )
                raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

            yield _stream()


class AsyncRawTextToSpeechClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    @contextlib.asynccontextmanager
    async def convert(
        self,
        voice_id: str,
        *,
        text: str,
        enable_logging: typing.Optional[bool] = None,
        optimize_streaming_latency: typing.Optional[int] = None,
        output_format: typing.Optional[TextToSpeechConvertRequestOutputFormat] = None,
        model_id: typing.Optional[str] = OMIT,
        language_code: typing.Optional[str] = OMIT,
        voice_settings: typing.Optional[VoiceSettings] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[
            typing.Sequence[PronunciationDictionaryVersionLocator]
        ] = OMIT,
        seed: typing.Optional[int] = OMIT,
        previous_text: typing.Optional[str] = OMIT,
        next_text: typing.Optional[str] = OMIT,
        previous_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        next_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        use_pvc_as_ivc: typing.Optional[bool] = OMIT,
        apply_text_normalization: typing.Optional[
            BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization
        ] = OMIT,
        apply_language_text_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[AsyncHttpResponse[typing.AsyncIterator[bytes]]]:
        """
        Converts text into speech using a voice of your choice and returns audio.

        Parameters
        ----------
        voice_id : str
            ID of the voice to be used. Use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        text : str
            The text that will get converted into speech.

        enable_logging : typing.Optional[bool]
            When enable_logging is set to false zero retention mode will be used for the request. This will mean history features are unavailable for this request, including request stitching. Zero retention mode may only be used by enterprise customers.

        optimize_streaming_latency : typing.Optional[int]
            You can turn on latency optimizations at some cost of quality. The best possible final latency varies by model. Possible values:
            0 - default mode (no latency optimizations)
            1 - normal latency optimizations (about 50% of possible latency improvement of option 3)
            2 - strong latency optimizations (about 75% of possible latency improvement of option 3)
            3 - max latency optimizations
            4 - max latency optimizations, but also with text normalizer turned off for even more latency savings (best latency, but can mispronounce eg numbers and dates).

            Defaults to None.

        output_format : typing.Optional[TextToSpeechConvertRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        model_id : typing.Optional[str]
            Identifier of the model that will be used, you can query them using GET /v1/models. The model needs to have support for text to speech, you can check this using the can_do_text_to_speech property.

        language_code : typing.Optional[str]
            Language code (ISO 639-1) used to enforce a language for the model. Currently only Turbo v2.5 and Flash v2.5 support language enforcement. For other models, an error will be returned if language code is provided.

        voice_settings : typing.Optional[VoiceSettings]
            Voice settings overriding stored settings for the given voice. They are applied only on the given request.

        pronunciation_dictionary_locators : typing.Optional[typing.Sequence[PronunciationDictionaryVersionLocator]]
            A list of pronunciation dictionary locators (id, version_id) to be applied to the text. They will be applied in order. You may have up to 3 locators per request

        seed : typing.Optional[int]
            If specified, our system will make a best effort to sample deterministically, such that repeated requests with the same seed and parameters should return the same result. Determinism is not guaranteed. Must be integer between 0 and 4294967295.

        previous_text : typing.Optional[str]
            The text that came before the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        next_text : typing.Optional[str]
            The text that comes after the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        previous_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that were generated before this generation. Can be used to improve the speech's continuity when splitting up a large task into multiple requests. The results will be best when the same model is used across the generations. In case both previous_text and previous_request_ids is send, previous_text will be ignored. A maximum of 3 request_ids can be send.

        next_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that come after this generation. next_request_ids is especially useful for maintaining the speech's continuity when regenerating a sample that has had some audio quality issues. For example, if you have generated 3 speech clips, and you want to improve clip 2, passing the request id of clip 3 as a next_request_id (and that of clip 1 as a previous_request_id) will help maintain natural flow in the combined speech. The results will be best when the same model is used across the generations. In case both next_text and next_request_ids is send, next_text will be ignored. A maximum of 3 request_ids can be send.

        use_pvc_as_ivc : typing.Optional[bool]
            If true, we won't use PVC version of the voice for the generation but the IVC version. This is a temporary workaround for higher latency in PVC versions.

        apply_text_normalization : typing.Optional[BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization]
            This parameter controls text normalization with three modes: 'auto', 'on', and 'off'. When set to 'auto', the system will automatically decide whether to apply text normalization (e.g., spelling out numbers). With 'on', text normalization will always be applied, while with 'off', it will be skipped. Cannot be turned on for 'eleven_turbo_v2_5' or 'eleven_flash_v2_5' models.

        apply_language_text_normalization : typing.Optional[bool]
            This parameter controls language text normalization. This helps with proper pronunciation of text in some supported languages. WARNING: This parameter can heavily increase the latency of the request. Currently only supported for Japanese.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[AsyncHttpResponse[typing.AsyncIterator[bytes]]]
            The generated audio file
        """
        async with self._client_wrapper.httpx_client.stream(
            f"v1/text-to-speech/{jsonable_encoder(voice_id)}",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            params={
                "enable_logging": enable_logging,
                "optimize_streaming_latency": optimize_streaming_latency,
                "output_format": output_format,
            },
            json={
                "text": text,
                "model_id": model_id,
                "language_code": language_code,
                "voice_settings": convert_and_respect_annotation_metadata(
                    object_=voice_settings, annotation=VoiceSettings, direction="write"
                ),
                "pronunciation_dictionary_locators": convert_and_respect_annotation_metadata(
                    object_=pronunciation_dictionary_locators,
                    annotation=typing.Sequence[PronunciationDictionaryVersionLocator],
                    direction="write",
                ),
                "seed": seed,
                "previous_text": previous_text,
                "next_text": next_text,
                "previous_request_ids": previous_request_ids,
                "next_request_ids": next_request_ids,
                "use_pvc_as_ivc": use_pvc_as_ivc,
                "apply_text_normalization": apply_text_normalization,
                "apply_language_text_normalization": apply_language_text_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:

            async def _stream() -> AsyncHttpResponse[typing.AsyncIterator[bytes]]:
                try:
                    if 200 <= _response.status_code < 300:
                        _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                        return AsyncHttpResponse(
                            response=_response,
                            data=(_chunk async for _chunk in _response.aiter_bytes(chunk_size=_chunk_size)),
                        )
                    await _response.aread()
                    if _response.status_code == 422:
                        raise UnprocessableEntityError(
                            headers=dict(_response.headers),
                            body=typing.cast(
                                HttpValidationError,
                                construct_type(
                                    type_=HttpValidationError,  # type: ignore
                                    object_=_response.json(),
                                ),
                            ),
                        )
                    _response_json = _response.json()
                except JSONDecodeError:
                    raise ApiError(
                        status_code=_response.status_code, headers=dict(_response.headers), body=_response.text
                    )
                raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

            yield await _stream()

    async def convert_with_timestamps(
        self,
        voice_id: str,
        *,
        text: str,
        enable_logging: typing.Optional[bool] = None,
        optimize_streaming_latency: typing.Optional[int] = None,
        output_format: typing.Optional[TextToSpeechConvertWithTimestampsRequestOutputFormat] = None,
        model_id: typing.Optional[str] = OMIT,
        language_code: typing.Optional[str] = OMIT,
        voice_settings: typing.Optional[VoiceSettings] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[
            typing.Sequence[PronunciationDictionaryVersionLocator]
        ] = OMIT,
        seed: typing.Optional[int] = OMIT,
        previous_text: typing.Optional[str] = OMIT,
        next_text: typing.Optional[str] = OMIT,
        previous_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        next_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        use_pvc_as_ivc: typing.Optional[bool] = OMIT,
        apply_text_normalization: typing.Optional[
            BodyTextToSpeechWithTimestampsV1TextToSpeechVoiceIdWithTimestampsPostApplyTextNormalization
        ] = OMIT,
        apply_language_text_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[AudioWithTimestampsResponse]:
        """
        Generate speech from text with precise character-level timing information for audio-text synchronization.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        text : str
            The text that will get converted into speech.

        enable_logging : typing.Optional[bool]
            When enable_logging is set to false zero retention mode will be used for the request. This will mean history features are unavailable for this request, including request stitching. Zero retention mode may only be used by enterprise customers.

        optimize_streaming_latency : typing.Optional[int]
            You can turn on latency optimizations at some cost of quality. The best possible final latency varies by model. Possible values:
            0 - default mode (no latency optimizations)
            1 - normal latency optimizations (about 50% of possible latency improvement of option 3)
            2 - strong latency optimizations (about 75% of possible latency improvement of option 3)
            3 - max latency optimizations
            4 - max latency optimizations, but also with text normalizer turned off for even more latency savings (best latency, but can mispronounce eg numbers and dates).

            Defaults to None.

        output_format : typing.Optional[TextToSpeechConvertWithTimestampsRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        model_id : typing.Optional[str]
            Identifier of the model that will be used, you can query them using GET /v1/models. The model needs to have support for text to speech, you can check this using the can_do_text_to_speech property.

        language_code : typing.Optional[str]
            Language code (ISO 639-1) used to enforce a language for the model. Currently only Turbo v2.5 and Flash v2.5 support language enforcement. For other models, an error will be returned if language code is provided.

        voice_settings : typing.Optional[VoiceSettings]
            Voice settings overriding stored settings for the given voice. They are applied only on the given request.

        pronunciation_dictionary_locators : typing.Optional[typing.Sequence[PronunciationDictionaryVersionLocator]]
            A list of pronunciation dictionary locators (id, version_id) to be applied to the text. They will be applied in order. You may have up to 3 locators per request

        seed : typing.Optional[int]
            If specified, our system will make a best effort to sample deterministically, such that repeated requests with the same seed and parameters should return the same result. Determinism is not guaranteed. Must be integer between 0 and 4294967295.

        previous_text : typing.Optional[str]
            The text that came before the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        next_text : typing.Optional[str]
            The text that comes after the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        previous_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that were generated before this generation. Can be used to improve the speech's continuity when splitting up a large task into multiple requests. The results will be best when the same model is used across the generations. In case both previous_text and previous_request_ids is send, previous_text will be ignored. A maximum of 3 request_ids can be send.

        next_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that come after this generation. next_request_ids is especially useful for maintaining the speech's continuity when regenerating a sample that has had some audio quality issues. For example, if you have generated 3 speech clips, and you want to improve clip 2, passing the request id of clip 3 as a next_request_id (and that of clip 1 as a previous_request_id) will help maintain natural flow in the combined speech. The results will be best when the same model is used across the generations. In case both next_text and next_request_ids is send, next_text will be ignored. A maximum of 3 request_ids can be send.

        use_pvc_as_ivc : typing.Optional[bool]
            If true, we won't use PVC version of the voice for the generation but the IVC version. This is a temporary workaround for higher latency in PVC versions.

        apply_text_normalization : typing.Optional[BodyTextToSpeechWithTimestampsV1TextToSpeechVoiceIdWithTimestampsPostApplyTextNormalization]
            This parameter controls text normalization with three modes: 'auto', 'on', and 'off'. When set to 'auto', the system will automatically decide whether to apply text normalization (e.g., spelling out numbers). With 'on', text normalization will always be applied, while with 'off', it will be skipped. Cannot be turned on for 'eleven_turbo_v2_5' or 'eleven_flash_v2_5' models.

        apply_language_text_normalization : typing.Optional[bool]
            This parameter controls language text normalization. This helps with proper pronunciation of text in some supported languages. WARNING: This parameter can heavily increase the latency of the request. Currently only supported for Japanese.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[AudioWithTimestampsResponse]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/text-to-speech/{jsonable_encoder(voice_id)}/with-timestamps",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            params={
                "enable_logging": enable_logging,
                "optimize_streaming_latency": optimize_streaming_latency,
                "output_format": output_format,
            },
            json={
                "text": text,
                "model_id": model_id,
                "language_code": language_code,
                "voice_settings": convert_and_respect_annotation_metadata(
                    object_=voice_settings, annotation=VoiceSettings, direction="write"
                ),
                "pronunciation_dictionary_locators": convert_and_respect_annotation_metadata(
                    object_=pronunciation_dictionary_locators,
                    annotation=typing.Sequence[PronunciationDictionaryVersionLocator],
                    direction="write",
                ),
                "seed": seed,
                "previous_text": previous_text,
                "next_text": next_text,
                "previous_request_ids": previous_request_ids,
                "next_request_ids": next_request_ids,
                "use_pvc_as_ivc": use_pvc_as_ivc,
                "apply_text_normalization": apply_text_normalization,
                "apply_language_text_normalization": apply_language_text_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AudioWithTimestampsResponse,
                    construct_type(
                        type_=AudioWithTimestampsResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    @contextlib.asynccontextmanager
    async def stream(
        self,
        voice_id: str,
        *,
        text: str,
        enable_logging: typing.Optional[bool] = None,
        optimize_streaming_latency: typing.Optional[int] = None,
        output_format: typing.Optional[TextToSpeechStreamRequestOutputFormat] = None,
        model_id: typing.Optional[str] = OMIT,
        language_code: typing.Optional[str] = OMIT,
        voice_settings: typing.Optional[VoiceSettings] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[
            typing.Sequence[PronunciationDictionaryVersionLocator]
        ] = OMIT,
        seed: typing.Optional[int] = OMIT,
        previous_text: typing.Optional[str] = OMIT,
        next_text: typing.Optional[str] = OMIT,
        previous_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        next_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        use_pvc_as_ivc: typing.Optional[bool] = OMIT,
        apply_text_normalization: typing.Optional[
            BodyTextToSpeechStreamingV1TextToSpeechVoiceIdStreamPostApplyTextNormalization
        ] = OMIT,
        apply_language_text_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[AsyncHttpResponse[typing.AsyncIterator[bytes]]]:
        """
        Converts text into speech using a voice of your choice and returns audio as an audio stream.

        Parameters
        ----------
        voice_id : str
            ID of the voice to be used. Use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        text : str
            The text that will get converted into speech.

        enable_logging : typing.Optional[bool]
            When enable_logging is set to false zero retention mode will be used for the request. This will mean history features are unavailable for this request, including request stitching. Zero retention mode may only be used by enterprise customers.

        optimize_streaming_latency : typing.Optional[int]
            You can turn on latency optimizations at some cost of quality. The best possible final latency varies by model. Possible values:
            0 - default mode (no latency optimizations)
            1 - normal latency optimizations (about 50% of possible latency improvement of option 3)
            2 - strong latency optimizations (about 75% of possible latency improvement of option 3)
            3 - max latency optimizations
            4 - max latency optimizations, but also with text normalizer turned off for even more latency savings (best latency, but can mispronounce eg numbers and dates).

            Defaults to None.

        output_format : typing.Optional[TextToSpeechStreamRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        model_id : typing.Optional[str]
            Identifier of the model that will be used, you can query them using GET /v1/models. The model needs to have support for text to speech, you can check this using the can_do_text_to_speech property.

        language_code : typing.Optional[str]
            Language code (ISO 639-1) used to enforce a language for the model. Currently only Turbo v2.5 and Flash v2.5 support language enforcement. For other models, an error will be returned if language code is provided.

        voice_settings : typing.Optional[VoiceSettings]
            Voice settings overriding stored settings for the given voice. They are applied only on the given request.

        pronunciation_dictionary_locators : typing.Optional[typing.Sequence[PronunciationDictionaryVersionLocator]]
            A list of pronunciation dictionary locators (id, version_id) to be applied to the text. They will be applied in order. You may have up to 3 locators per request

        seed : typing.Optional[int]
            If specified, our system will make a best effort to sample deterministically, such that repeated requests with the same seed and parameters should return the same result. Determinism is not guaranteed. Must be integer between 0 and 4294967295.

        previous_text : typing.Optional[str]
            The text that came before the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        next_text : typing.Optional[str]
            The text that comes after the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        previous_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that were generated before this generation. Can be used to improve the speech's continuity when splitting up a large task into multiple requests. The results will be best when the same model is used across the generations. In case both previous_text and previous_request_ids is send, previous_text will be ignored. A maximum of 3 request_ids can be send.

        next_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that come after this generation. next_request_ids is especially useful for maintaining the speech's continuity when regenerating a sample that has had some audio quality issues. For example, if you have generated 3 speech clips, and you want to improve clip 2, passing the request id of clip 3 as a next_request_id (and that of clip 1 as a previous_request_id) will help maintain natural flow in the combined speech. The results will be best when the same model is used across the generations. In case both next_text and next_request_ids is send, next_text will be ignored. A maximum of 3 request_ids can be send.

        use_pvc_as_ivc : typing.Optional[bool]
            If true, we won't use PVC version of the voice for the generation but the IVC version. This is a temporary workaround for higher latency in PVC versions.

        apply_text_normalization : typing.Optional[BodyTextToSpeechStreamingV1TextToSpeechVoiceIdStreamPostApplyTextNormalization]
            This parameter controls text normalization with three modes: 'auto', 'on', and 'off'. When set to 'auto', the system will automatically decide whether to apply text normalization (e.g., spelling out numbers). With 'on', text normalization will always be applied, while with 'off', it will be skipped. Cannot be turned on for 'eleven_turbo_v2_5' or 'eleven_flash_v2_5' models.

        apply_language_text_normalization : typing.Optional[bool]
            This parameter controls language text normalization. This helps with proper pronunciation of text in some supported languages. WARNING: This parameter can heavily increase the latency of the request. Currently only supported for Japanese.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[AsyncHttpResponse[typing.AsyncIterator[bytes]]]
            Streaming audio data
        """
        async with self._client_wrapper.httpx_client.stream(
            f"v1/text-to-speech/{jsonable_encoder(voice_id)}/stream",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            params={
                "enable_logging": enable_logging,
                "optimize_streaming_latency": optimize_streaming_latency,
                "output_format": output_format,
            },
            json={
                "text": text,
                "model_id": model_id,
                "language_code": language_code,
                "voice_settings": convert_and_respect_annotation_metadata(
                    object_=voice_settings, annotation=VoiceSettings, direction="write"
                ),
                "pronunciation_dictionary_locators": convert_and_respect_annotation_metadata(
                    object_=pronunciation_dictionary_locators,
                    annotation=typing.Sequence[PronunciationDictionaryVersionLocator],
                    direction="write",
                ),
                "seed": seed,
                "previous_text": previous_text,
                "next_text": next_text,
                "previous_request_ids": previous_request_ids,
                "next_request_ids": next_request_ids,
                "use_pvc_as_ivc": use_pvc_as_ivc,
                "apply_text_normalization": apply_text_normalization,
                "apply_language_text_normalization": apply_language_text_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:

            async def _stream() -> AsyncHttpResponse[typing.AsyncIterator[bytes]]:
                try:
                    if 200 <= _response.status_code < 300:
                        _chunk_size = request_options.get("chunk_size", 1024) if request_options is not None else 1024
                        return AsyncHttpResponse(
                            response=_response,
                            data=(_chunk async for _chunk in _response.aiter_bytes(chunk_size=_chunk_size)),
                        )
                    await _response.aread()
                    if _response.status_code == 422:
                        raise UnprocessableEntityError(
                            headers=dict(_response.headers),
                            body=typing.cast(
                                HttpValidationError,
                                construct_type(
                                    type_=HttpValidationError,  # type: ignore
                                    object_=_response.json(),
                                ),
                            ),
                        )
                    _response_json = _response.json()
                except JSONDecodeError:
                    raise ApiError(
                        status_code=_response.status_code, headers=dict(_response.headers), body=_response.text
                    )
                raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

            yield await _stream()

    @contextlib.asynccontextmanager
    async def stream_with_timestamps(
        self,
        voice_id: str,
        *,
        text: str,
        enable_logging: typing.Optional[bool] = None,
        optimize_streaming_latency: typing.Optional[int] = None,
        output_format: typing.Optional[TextToSpeechStreamWithTimestampsRequestOutputFormat] = None,
        model_id: typing.Optional[str] = OMIT,
        language_code: typing.Optional[str] = OMIT,
        voice_settings: typing.Optional[VoiceSettings] = OMIT,
        pronunciation_dictionary_locators: typing.Optional[
            typing.Sequence[PronunciationDictionaryVersionLocator]
        ] = OMIT,
        seed: typing.Optional[int] = OMIT,
        previous_text: typing.Optional[str] = OMIT,
        next_text: typing.Optional[str] = OMIT,
        previous_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        next_request_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        use_pvc_as_ivc: typing.Optional[bool] = OMIT,
        apply_text_normalization: typing.Optional[
            BodyTextToSpeechStreamingWithTimestampsV1TextToSpeechVoiceIdStreamWithTimestampsPostApplyTextNormalization
        ] = OMIT,
        apply_language_text_normalization: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[AsyncHttpResponse[typing.AsyncIterator[StreamingAudioChunkWithTimestampsResponse]]]:
        """
        Converts text into speech using a voice of your choice and returns a stream of JSONs containing audio as a base64 encoded string together with information on when which character was spoken.

        Parameters
        ----------
        voice_id : str
            ID of the voice to be used. Use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        text : str
            The text that will get converted into speech.

        enable_logging : typing.Optional[bool]
            When enable_logging is set to false zero retention mode will be used for the request. This will mean history features are unavailable for this request, including request stitching. Zero retention mode may only be used by enterprise customers.

        optimize_streaming_latency : typing.Optional[int]
            You can turn on latency optimizations at some cost of quality. The best possible final latency varies by model. Possible values:
            0 - default mode (no latency optimizations)
            1 - normal latency optimizations (about 50% of possible latency improvement of option 3)
            2 - strong latency optimizations (about 75% of possible latency improvement of option 3)
            3 - max latency optimizations
            4 - max latency optimizations, but also with text normalizer turned off for even more latency savings (best latency, but can mispronounce eg numbers and dates).

            Defaults to None.

        output_format : typing.Optional[TextToSpeechStreamWithTimestampsRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        model_id : typing.Optional[str]
            Identifier of the model that will be used, you can query them using GET /v1/models. The model needs to have support for text to speech, you can check this using the can_do_text_to_speech property.

        language_code : typing.Optional[str]
            Language code (ISO 639-1) used to enforce a language for the model. Currently only Turbo v2.5 and Flash v2.5 support language enforcement. For other models, an error will be returned if language code is provided.

        voice_settings : typing.Optional[VoiceSettings]
            Voice settings overriding stored settings for the given voice. They are applied only on the given request.

        pronunciation_dictionary_locators : typing.Optional[typing.Sequence[PronunciationDictionaryVersionLocator]]
            A list of pronunciation dictionary locators (id, version_id) to be applied to the text. They will be applied in order. You may have up to 3 locators per request

        seed : typing.Optional[int]
            If specified, our system will make a best effort to sample deterministically, such that repeated requests with the same seed and parameters should return the same result. Determinism is not guaranteed. Must be integer between 0 and 4294967295.

        previous_text : typing.Optional[str]
            The text that came before the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        next_text : typing.Optional[str]
            The text that comes after the text of the current request. Can be used to improve the speech's continuity when concatenating together multiple generations or to influence the speech's continuity in the current generation.

        previous_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that were generated before this generation. Can be used to improve the speech's continuity when splitting up a large task into multiple requests. The results will be best when the same model is used across the generations. In case both previous_text and previous_request_ids is send, previous_text will be ignored. A maximum of 3 request_ids can be send.

        next_request_ids : typing.Optional[typing.Sequence[str]]
            A list of request_id of the samples that come after this generation. next_request_ids is especially useful for maintaining the speech's continuity when regenerating a sample that has had some audio quality issues. For example, if you have generated 3 speech clips, and you want to improve clip 2, passing the request id of clip 3 as a next_request_id (and that of clip 1 as a previous_request_id) will help maintain natural flow in the combined speech. The results will be best when the same model is used across the generations. In case both next_text and next_request_ids is send, next_text will be ignored. A maximum of 3 request_ids can be send.

        use_pvc_as_ivc : typing.Optional[bool]
            If true, we won't use PVC version of the voice for the generation but the IVC version. This is a temporary workaround for higher latency in PVC versions.

        apply_text_normalization : typing.Optional[BodyTextToSpeechStreamingWithTimestampsV1TextToSpeechVoiceIdStreamWithTimestampsPostApplyTextNormalization]
            This parameter controls text normalization with three modes: 'auto', 'on', and 'off'. When set to 'auto', the system will automatically decide whether to apply text normalization (e.g., spelling out numbers). With 'on', text normalization will always be applied, while with 'off', it will be skipped. Cannot be turned on for 'eleven_turbo_v2_5' or 'eleven_flash_v2_5' models.

        apply_language_text_normalization : typing.Optional[bool]
            This parameter controls language text normalization. This helps with proper pronunciation of text in some supported languages. WARNING: This parameter can heavily increase the latency of the request. Currently only supported for Japanese.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Yields
        ------
        typing.AsyncIterator[AsyncHttpResponse[typing.AsyncIterator[StreamingAudioChunkWithTimestampsResponse]]]
            Stream of transcription chunks
        """
        async with self._client_wrapper.httpx_client.stream(
            f"v1/text-to-speech/{jsonable_encoder(voice_id)}/stream/with-timestamps",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            params={
                "enable_logging": enable_logging,
                "optimize_streaming_latency": optimize_streaming_latency,
                "output_format": output_format,
            },
            json={
                "text": text,
                "model_id": model_id,
                "language_code": language_code,
                "voice_settings": convert_and_respect_annotation_metadata(
                    object_=voice_settings, annotation=VoiceSettings, direction="write"
                ),
                "pronunciation_dictionary_locators": convert_and_respect_annotation_metadata(
                    object_=pronunciation_dictionary_locators,
                    annotation=typing.Sequence[PronunciationDictionaryVersionLocator],
                    direction="write",
                ),
                "seed": seed,
                "previous_text": previous_text,
                "next_text": next_text,
                "previous_request_ids": previous_request_ids,
                "next_request_ids": next_request_ids,
                "use_pvc_as_ivc": use_pvc_as_ivc,
                "apply_text_normalization": apply_text_normalization,
                "apply_language_text_normalization": apply_language_text_normalization,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:

            async def _stream() -> AsyncHttpResponse[typing.AsyncIterator[StreamingAudioChunkWithTimestampsResponse]]:
                try:
                    if 200 <= _response.status_code < 300:

                        async def _iter():
                            async for _text in _response.aiter_lines():
                                try:
                                    if len(_text) == 0:
                                        continue
                                    yield typing.cast(
                                        StreamingAudioChunkWithTimestampsResponse,
                                        construct_type(
                                            type_=StreamingAudioChunkWithTimestampsResponse,  # type: ignore
                                            object_=json.loads(_text),
                                        ),
                                    )
                                except Exception:
                                    pass
                            return

                        return AsyncHttpResponse(response=_response, data=_iter())
                    await _response.aread()
                    if _response.status_code == 422:
                        raise UnprocessableEntityError(
                            headers=dict(_response.headers),
                            body=typing.cast(
                                HttpValidationError,
                                construct_type(
                                    type_=HttpValidationError,  # type: ignore
                                    object_=_response.json(),
                                ),
                            ),
                        )
                    _response_json = _response.json()
                except JSONDecodeError:
                    raise ApiError(
                        status_code=_response.status_code, headers=dict(_response.headers), body=_response.text
                    )
                raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

            yield await _stream()
