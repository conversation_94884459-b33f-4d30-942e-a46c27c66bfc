#!/usr/bin/env python3
"""Juno FINAL: ElevenLabs ONLY Agent - Button Activation Mode

This is the DEFINITIVE agent that uses ONLY ElevenLabs TTS.
No fallbacks, no Cartesia, no confusion.

Features:
- OpenAI Whisper STT with Arabic-English language detection
- Eleven<PERSON>abs TTS ONLY with specific voice ID
- Button-based activation (no wake word)
- Two modes: LISTENING (passive) and RESPONSIVE (active)
- Clear logging to verify TTS provider
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, Agent, AgentSession
from livekit.plugins import openai, silero, elevenlabs
from livekit import rtc

# Import our custom Whisper STT with language detection (WORKING CONFIGURATION)
from whisper_language_stt import create_whisper_language_stt

# Import button server for activation control
from button_server import start_button_server, register_mode_change_callback, get_current_mode

# Global state for activation mode
activation_mode = "LISTENING"  # LISTENING or RESPONSIVE

class <PERSON><PERSON>uttonAgent(Agent):
    """Custom agent that respects button activation mode."""
    
    async def say(self, message: str, **kwargs):
        """Override say method to check activation mode."""
        current_mode = get_current_mode()
        if current_mode == "RESPONSIVE":
            logger.info(f"🔊 RESPONDING (mode: {current_mode}): {message[:50]}...")
            return await super().say(message, **kwargs)
        else:
            logger.info(f"🤫 SILENCED (mode: {current_mode}): Would have said: {message[:50]}...")
            return None
    
    async def _handle_user_speech(self, user_speech: str):
        """Handle user speech based on activation mode."""
        current_mode = get_current_mode()
        logger.info(f"📝 TRANSCRIBED ({current_mode}): {user_speech}")
        
        if current_mode == "RESPONSIVE":
            # Process normally when in responsive mode
            return await super()._handle_user_speech(user_speech)
        else:
            # Just log in listening mode, don't respond
            logger.info(f"🤫 LISTENING MODE: Transcribed but not responding")
            return None

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def prewarm(proc: JobContext):
    """
    Prewarm function to initialize models and connections.
    """
    logger.info("🔥 SYSTEM REBOOT: Prewarming Juno with ElevenLabs ONLY...")
    logger.info("- Custom Whisper STT (WORKING Arabic-English detection)")
    logger.info("- OpenAI GPT-4o-mini LLM")
    logger.info("- ElevenLabs TTS ONLY (NO CARTESIA)")
    logger.info("- Silero VAD")
    proc.wait_for_participant = False
    logger.info("🔥 SYSTEM REBOOT: Prewarm completed successfully")

async def entrypoint(ctx: JobContext):
    """
    Main entrypoint for Juno FINAL with ElevenLabs ONLY.
    """
    logger.info("🔥 SYSTEM REBOOT: Starting Juno FINAL with ElevenLabs ONLY...")
    logger.info("🚫 NO CARTESIA - ElevenLabs ONLY MODE")

    # Connect to the room first
    await ctx.connect()
    logger.info("✅ Connected to LiveKit room")

    # Create the Juno agent with button activation behavior
    agent = JunoButtonAgent(
        instructions="""You are Juno, a multilingual meeting assistant AI with Arabic-English language detection capabilities.

أنت جونو، مساعد اجتماعات ذكي متعدد اللغات مع قدرات كشف اللغة العربية والإنجليزية.

BUTTON ACTIVATION BEHAVIOR:
- You have two modes: LISTENING (passive) and RESPONSIVE (active)
- In LISTENING mode: You transcribe conversations but do NOT respond
- In RESPONSIVE mode: You can respond to questions and participate
- Mode is controlled by a button press (not voice activation)
- You act as a silent meeting participant until button activates RESPONSIVE mode

LANGUAGE HANDLING:
- You receive transcripts with language markers like [EN]text[/EN] and [AR]text[/AR]
- Process both English and Arabic content naturally
- ALWAYS respond in the same language as the user's question
- If user speaks Arabic, respond in Arabic
- If user speaks English, respond in English
- Handle code-switching (mixing languages) gracefully

VOICE RULES:
- Keep responses brief and conversational
- Be friendly and helpful in both languages
- Don't use markdown or special characters in responses
- Don't read out language markers like [EN] or [AR]
- Use natural pronunciation for each language

You are using ElevenLabs TTS ONLY for consistent, natural voice synthesis."""
    )

    # Initialize components with STRICT ElevenLabs-only configuration
    try:
        # Initialize WORKING custom Whisper STT with Arabic-English detection
        # Note: This is the EXACT configuration that was working for Arabic detection
        whisper_stt = create_whisper_language_stt(
            model="whisper-1",
            detect_language=True,
            supported_languages=["en", "ar"],
            add_language_markers=True
        )
        logger.info("✅ CUSTOM WHISPER STT: Initialized with WORKING Arabic-English detection")
        logger.info("🔄 RESTORED: Using proven working configuration for Arabic")

        # Initialize ElevenLabs TTS with specific voice ID - NO ALTERNATIVES
        elevenlabs_tts = elevenlabs.TTS(
            model="eleven_turbo_v2_5",
            voice_id="QRq5hPRAKf5ZhSlTBH6r",  # Your specific voice ID
            language="en",
            enable_ssml_parsing=True,
        )
        logger.info("✅ ELEVENLABS TTS: Initialized with voice ID QRq5hPRAKf5ZhSlTBH6r")
        logger.info("🚫 NO CARTESIA: ElevenLabs is the ONLY TTS provider")

        # Create agent session with WORKING STT + ElevenLabs TTS
        session = AgentSession(
            vad=silero.VAD.load(),
            stt=whisper_stt,  # WORKING: Custom Whisper STT with Arabic-English detection
            llm=openai.LLM(model="gpt-4o-mini", temperature=0.1),
            tts=elevenlabs_tts,  # ONLY ElevenLabs - no list, no fallbacks
        )
        logger.info("✅ AGENT SESSION: Created with ElevenLabs ONLY")

    except Exception as e:
        logger.error(f"❌ CRITICAL ERROR: Cannot initialize ElevenLabs TTS: {e}")
        logger.error("❌ SYSTEM HALT: No fallbacks allowed - fix ElevenLabs configuration")
        raise e  # Fail completely rather than use Cartesia

    # Start the button server for activation control
    start_button_server(port=8080)
    
    # Set up mode change callback
    def on_mode_change(new_mode):
        logger.info(f"🔄 Agent notified of mode change: {new_mode}")
    
    register_mode_change_callback(on_mode_change)
    
    # Start the session
    await session.start(agent=agent, room=ctx.room)
    logger.info("✅ AGENT SESSION: Started successfully with ElevenLabs ONLY")

    logger.info("🎉 JUNO FINAL: Ready with BUTTON ACTIVATION + ElevenLabs TTS!")
    logger.info("🎯 ACTIVATION: Button-based mode switching")
    logger.info("🔄 STT PROVIDER: OpenAI Whisper (optimized for Arabic-English)")
    logger.info("🔊 TTS PROVIDER: ElevenLabs ONLY (voice: QRq5hPRAKf5ZhSlTBH6r)")
    logger.info("🚫 NO CARTESIA: System configured to prevent any fallbacks")
    logger.info("📝 Current mode: LISTENING (passive)")
    logger.info("🌐 Button Interface: http://localhost:8080")
    logger.info("📝 Test with Arabic: 'مرحبا كيف حالك اليوم؟'")
    logger.info("📝 Test with English: 'Hello how are you today?'")
    logger.info("🌍 MULTILINGUAL: Perfect Arabic-English code-switching support!")
    logger.info("🤫 LISTENING MODE: Juno transcribes but doesn't respond until activated")
    logger.info("🎯 CONTROL: Open http://localhost:8080 to toggle between LISTENING/RESPONSIVE modes")

if __name__ == "__main__":
    # Verify environment variables
    required_env_vars = [
        "OPENAI_API_KEY",
        "ELEVEN_API_KEY",
        # "DEEPGRAM_API_KEY",  # Not needed for OpenAI Whisper STT
        "LIVEKIT_URL",
        "LIVEKIT_API_KEY",
        "LIVEKIT_API_SECRET"
    ]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        logger.error(f"❌ MISSING ENV VARS: {missing_vars}")
        logger.error("❌ SYSTEM HALT: Fix environment configuration")
        exit(1)

    logger.info("✅ ENV VARS: All required variables found")
    logger.info(f"🔑 ElevenLabs API Key: {os.getenv('ELEVEN_API_KEY')[:10]}...")
    logger.info(f"🔄 OpenAI API Key: {os.getenv('OPENAI_API_KEY')[:10]}...")
    logger.info(f"🎤 Voice ID: QRq5hPRAKf5ZhSlTBH6r")
    logger.info("🔥 SYSTEM REBOOT: OPTIMIZED STT + ElevenLabs ONLY mode")

    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=prewarm,
        ),
    )
