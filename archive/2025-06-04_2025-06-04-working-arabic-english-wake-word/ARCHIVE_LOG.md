# Project Archived: Juno Arabic-English Wake Word Voice Agent

- **Archive Date**: June 4, 2025
- **Project**: Working Arabic-English Voice Agent with Wake Word Detection
- **Status**: ✅ FULLY FUNCTIONAL - Ready for production use

## 🎯 What's Working
- ✅ **Arabic-English Speech Recognition** - Perfect language detection
- ✅ **Wake Word Detection** - "Juno" activation system
- ✅ **ElevenLabs TTS** - High-quality voice output
- ✅ **LiveKit Integration** - Real-time voice processing
- ✅ **Mode Switching** - Passive/Active mode functionality
- ✅ **Language Markers** - [EN]/[AR] code-switching detection

## 📁 Key Files
- `src/juno_final_elevenlabs_only.py` - Main working agent
- `src/whisper_language_stt.py` - Custom Arabic-English STT
- `src/juno_wake_word_detector.py` - Wake word detection
- `src/activation_button.html` - Web interface for mode switching

## 🔧 Technical Stack
- **STT**: OpenAI Whisper with custom language detection
- **TTS**: ElevenLabs (Voice ID: QRq5hPRAKf5ZhSlTBH6r)
- **Platform**: LiveKit Agents Framework
- **Languages**: Arabic-English code-switching support

## 📋 Contents
This archive contains the complete working state of the Arabic-English voice agent with wake word detection, ready for production deployment or further development.
