#!/usr/bin/env python3
"""
Simple Test Suite for Arabic-English Language Detection (No API Keys Required)

This module contains tests that don't require OpenAI API keys,
focusing on configuration and utility functions.
"""

import unittest
import logging
from language_detection_config import (
    get_language_config,
    format_transcript_with_markers,
    detect_code_switching_context,
    create_test_scenarios,
    validate_language_code,
    get_language_display_name
)

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestLanguageDetectionConfig(unittest.TestCase):
    """Test language detection configuration without API calls."""
    
    def test_default_config(self):
        """Test default configuration loading."""
        config = get_language_config("default")
        
        self.assertEqual(config.supported_languages, ["en", "ar"])
        self.assertTrue(config.enable_language_detection)
        self.assertTrue(config.add_language_markers)
        self.assertEqual(config.whisper_model, "whisper-1")
        self.assertEqual(config.default_language, "en")
    
    def test_english_only_config(self):
        """Test English-only configuration."""
        config = get_language_config("english_only")
        
        self.assertEqual(config.supported_languages, ["en"])
        self.assertFalse(config.enable_language_detection)
        self.assertFalse(config.add_language_markers)
        self.assertEqual(config.default_language, "en")
    
    def test_arabic_only_config(self):
        """Test Arabic-only configuration."""
        config = get_language_config("arabic_only")
        
        self.assertEqual(config.supported_languages, ["ar"])
        self.assertFalse(config.enable_language_detection)
        self.assertFalse(config.add_language_markers)
        self.assertEqual(config.default_language, "ar")
    
    def test_format_transcript_with_markers(self):
        """Test transcript formatting with language markers."""
        config = get_language_config("default")
        
        # Test English text
        result = format_transcript_with_markers("Hello world", "en", config)
        self.assertEqual(result, "[EN]Hello world[/EN]")
        
        # Test Arabic text
        result = format_transcript_with_markers("مرحبا", "ar", config)
        self.assertEqual(result, "[AR]مرحبا[/AR]")
        
        # Test empty text
        result = format_transcript_with_markers("", "en", config)
        self.assertEqual(result, "")
        
        # Test whitespace-only text
        result = format_transcript_with_markers("   ", "en", config)
        self.assertEqual(result, "   ")
    
    def test_format_transcript_without_markers(self):
        """Test transcript formatting with markers disabled."""
        config = get_language_config("english_only")  # Has add_language_markers=False
        
        # Should return original text without markers
        result = format_transcript_with_markers("Hello world", "en", config)
        self.assertEqual(result, "Hello world")
    
    def test_unsupported_language_handling(self):
        """Test handling of unsupported languages."""
        config = get_language_config("default")
        
        # Test with unsupported language - should return original text
        result = format_transcript_with_markers("Bonjour", "fr", config)
        self.assertEqual(result, "Bonjour")  # No markers for unsupported language
    
    def test_validate_language_code(self):
        """Test language code validation."""
        config = get_language_config("default")
        
        # Test supported languages
        self.assertTrue(validate_language_code("en", config))
        self.assertTrue(validate_language_code("ar", config))
        
        # Test unsupported language
        self.assertFalse(validate_language_code("fr", config))
        self.assertFalse(validate_language_code("es", config))
    
    def test_get_language_display_name(self):
        """Test language display name retrieval."""
        self.assertEqual(get_language_display_name("en"), "English")
        self.assertEqual(get_language_display_name("ar"), "Arabic")
        self.assertEqual(get_language_display_name("unknown"), "Unknown")
        self.assertEqual(get_language_display_name("fr"), "FR")  # Fallback to uppercase

class TestCodeSwitchingDetection(unittest.TestCase):
    """Test code-switching detection utilities."""
    
    def test_code_switching_detection(self):
        """Test code-switching context detection."""
        # Test mixed content
        scores = detect_code_switching_context("We need the budget قبل نهاية الأسبوع")
        self.assertGreater(scores["en"], 0)
        self.assertGreater(scores["ar"], 0)
        
        # Test English-only content
        scores = detect_code_switching_context("Hello how are you today")
        self.assertGreaterEqual(scores["en"], scores["ar"])
        
        # Test Arabic-only content
        scores = detect_code_switching_context("مرحبا كيف حالك اليوم")
        self.assertGreaterEqual(scores["ar"], scores["en"])
    
    def test_empty_text_code_switching(self):
        """Test code-switching detection with empty text."""
        scores = detect_code_switching_context("")
        self.assertEqual(scores["en"], 0.0)
        self.assertEqual(scores["ar"], 0.0)

class TestScenarioDefinitions(unittest.TestCase):
    """Test scenario definitions for validation."""
    
    def test_scenario_definitions(self):
        """Test that test scenarios are properly defined."""
        scenarios = create_test_scenarios()
        self.assertGreater(len(scenarios), 0)
        
        for scenario in scenarios:
            self.assertIn("name", scenario)
            self.assertIn("input", scenario)
            self.assertIn("expected_language", scenario)
            self.assertIn("expected_output", scenario)
            
            # Verify scenario has meaningful content
            self.assertTrue(len(scenario["name"]) > 0)
            self.assertTrue(len(scenario["input"]) > 0)
            self.assertTrue(len(scenario["expected_language"]) > 0)
    
    def test_english_scenario_formatting(self):
        """Test English scenario formatting."""
        config = get_language_config("default")
        scenarios = create_test_scenarios()
        
        english_scenario = next(
            s for s in scenarios 
            if s["name"] == "Pure English"
        )
        
        result = format_transcript_with_markers(
            english_scenario["input"],
            "en",
            config
        )
        
        self.assertTrue(result.startswith("[EN]"))
        self.assertTrue(result.endswith("[/EN]"))
        self.assertIn(english_scenario["input"], result)
    
    def test_arabic_scenario_formatting(self):
        """Test Arabic scenario formatting."""
        config = get_language_config("default")
        scenarios = create_test_scenarios()
        
        arabic_scenario = next(
            s for s in scenarios 
            if s["name"] == "Pure Arabic"
        )
        
        result = format_transcript_with_markers(
            arabic_scenario["input"],
            "ar",
            config
        )
        
        self.assertTrue(result.startswith("[AR]"))
        self.assertTrue(result.endswith("[/AR]"))
        self.assertIn(arabic_scenario["input"], result)

class TestConfigurationEdgeCases(unittest.TestCase):
    """Test edge cases in configuration handling."""
    
    def test_unknown_config_name(self):
        """Test handling of unknown configuration names."""
        # Should return default config for unknown names
        config = get_language_config("nonexistent_config")
        default_config = get_language_config("default")
        
        self.assertEqual(config.supported_languages, default_config.supported_languages)
        self.assertEqual(config.whisper_model, default_config.whisper_model)
    
    def test_none_text_handling(self):
        """Test handling of None text input."""
        config = get_language_config("default")
        
        # Should handle None gracefully
        try:
            result = format_transcript_with_markers(None, "en", config)
            # If it doesn't raise an exception, that's fine
            # The exact behavior depends on implementation
        except (TypeError, AttributeError):
            # This is also acceptable - None input should be handled
            pass

def run_manual_test_scenarios():
    """Run manual test scenarios for validation."""
    logger.info("Running manual test scenarios...")
    
    scenarios = create_test_scenarios()
    config = get_language_config("default")
    
    for scenario in scenarios:
        logger.info(f"\n--- Testing: {scenario['name']} ---")
        logger.info(f"Input: {scenario['input']}")
        logger.info(f"Expected Language: {scenario['expected_language']}")
        
        # For pure language scenarios, test formatting
        if scenario['expected_language'] in ['en', 'ar']:
            result = format_transcript_with_markers(
                scenario['input'],
                scenario['expected_language'],
                config
            )
            logger.info(f"Formatted Output: {result}")
            logger.info(f"Expected Output: {scenario['expected_output']}")
            logger.info(f"Match: {result == scenario['expected_output']}")
        else:
            logger.info("Mixed language scenario - would require actual STT processing")

if __name__ == "__main__":
    logger.info("Starting simple language detection tests (no API keys required)...")
    
    # Run unittest suite
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run additional manual tests
    run_manual_test_scenarios()
    
    logger.info("All simple tests completed!")
