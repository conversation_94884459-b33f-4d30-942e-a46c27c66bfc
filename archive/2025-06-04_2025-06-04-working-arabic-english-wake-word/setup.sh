#!/bin/bash

# LiveKit Voice Agent Setup Script
# This script helps you set up the LiveKit voice agent with your API keys

echo "🎙️  LiveKit Voice Agent Setup"
echo "================================"

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed. Please install Python 3.8 or higher."
    exit 1
fi

echo "✅ Python 3 found"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "📥 Installing requirements..."
pip install -r requirements.txt

# Download turn detector model files (required for first run)
echo "🤖 Downloading turn detector model files..."
python3 -c "
import asyncio
from livekit.plugins import turn_detector

async def download_models():
    try:
        # This will download the required model files
        model = turn_detector.EOUModel()
        print('✅ Turn detector models downloaded successfully')
    except Exception as e:
        print(f'⚠️  Warning: Could not download turn detector models: {e}')
        print('   You can try running the agent anyway, it will download on first use.')

asyncio.run(download_models())
"

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📝 Next steps:"
echo "1. Edit the .env file and add your API keys:"
echo "   - OPENAI_API_KEY=your_openai_api_key"
echo "   - CARTESIA_API_KEY=your_cartesia_api_key" 
echo "   - DEEPGRAM_API_KEY=your_deepgram_api_key"
echo ""
echo "2. Run the agent:"
echo "   python3 agent.py dev"
echo ""
echo "3. Open the LiveKit playground in your browser to test"
echo ""
echo "🔧 Troubleshooting:"
echo "   - Make sure all API keys are valid"
echo "   - Check that your LiveKit project is active"
echo "   - Ensure you have a stable internet connection"
