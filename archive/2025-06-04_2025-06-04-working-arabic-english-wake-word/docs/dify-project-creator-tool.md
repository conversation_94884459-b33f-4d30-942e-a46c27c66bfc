# Dify Custom Tool: Project Creator

## Tool Configuration for Dify

### Tool Details
- **Name**: `project_creator`
- **Display Name**: "Project Creator"
- **Description**: "Creates new projects and triggers N8N workflow for project setup"
- **Type**: "Custom API Tool"

### API Configuration

#### Endpoint Details
- **Method**: POST
- **URL**: `https://your-n8n-instance.com/webhook/create-project`
- **Content-Type**: application/json

#### Request Schema
```json
{
  "type": "object",
  "properties": {
    "project_name": {
      "type": "string",
      "description": "Name of the project to create",
      "required": true
    },
    "description": {
      "type": "string",
      "description": "Project description or objectives",
      "required": false
    },
    "deadline": {
      "type": "string",
      "description": "Project deadline in YYYY-MM-DD format",
      "required": false
    },
    "priority": {
      "type": "string",
      "enum": ["low", "medium", "high", "urgent"],
      "description": "Project priority level",
      "required": false,
      "default": "medium"
    },
    "domain": {
      "type": "string",
      "enum": ["executive", "academic", "business", "family", "wellness", "financial"],
      "description": "Domain category for the project",
      "required": false,
      "default": "business"
    },
    "assigned_to": {
      "type": "string",
      "description": "Team member or person assigned to the project",
      "required": false
    }
  },
  "required": ["project_name"]
}
```

#### Response Schema
```json
{
  "type": "object",
  "properties": {
    "success": {
      "type": "boolean",
      "description": "Whether the project was created successfully"
    },
    "project_id": {
      "type": "string",
      "description": "Unique identifier for the created project"
    },
    "message": {
      "type": "string",
      "description": "Success or error message"
    },
    "project_details": {
      "type": "object",
      "description": "Details of the created project"
    }
  }
}
```

### Authentication
- **Type**: API Key
- **Header**: `X-API-Key`
- **Value**: `your-n8n-webhook-secret` (if using authentication)

## N8N Webhook Workflow

### Webhook Node Configuration
1. **HTTP Method**: POST
2. **Path**: `/webhook/create-project`
3. **Response Mode**: "Respond to Webhook"

### Workflow Steps

#### 1. Webhook Trigger
- Receives project creation request from Dify
- Extracts project parameters

#### 2. Data Validation
- Validates required fields
- Sets default values for optional fields
- Formats dates and priorities

#### 3. Supabase Insert
- **Table**: `projects`
- **Fields**:
  ```json
  {
    "name": "{{ $json.project_name }}",
    "description": "{{ $json.description }}",
    "deadline": "{{ $json.deadline }}",
    "priority": "{{ $json.priority }}",
    "domain": "{{ $json.domain }}",
    "assigned_to": "{{ $json.assigned_to }}",
    "status": "active",
    "created_at": "{{ $now }}",
    "created_by": "voice_agent"
  }
  ```

#### 4. Generate Project ID
- Create unique project identifier
- Update project record with ID

#### 5. Send Notifications (Optional)
- Email notification to assigned person
- Slack/Discord notification to team
- WhatsApp notification (using your existing setup)

#### 6. Response to Dify
```json
{
  "success": true,
  "project_id": "{{ $json.id }}",
  "message": "Project '{{ $json.project_name }}' created successfully",
  "project_details": {
    "name": "{{ $json.project_name }}",
    "id": "{{ $json.id }}",
    "deadline": "{{ $json.deadline }}",
    "priority": "{{ $json.priority }}",
    "domain": "{{ $json.domain }}"
  }
}
```

## Testing the Tool

### Test in Dify
1. Go to your agent's tool configuration
2. Add the custom API tool with above configuration
3. Test with sample data:
   ```json
   {
     "project_name": "Test Project",
     "description": "This is a test project",
     "deadline": "2024-02-15",
     "priority": "high",
     "domain": "business"
   }
   ```

### Test Commands for Agent
- "Create a new project called Website Redesign with high priority"
- "Start a new business project for client onboarding due next month"
- "Create an academic project for research paper with medium priority"

## Integration with Your Domain System

The tool respects your domain-based architecture:
- **Executive**: High-level strategic projects
- **Academic**: Research and learning projects
- **Business**: Client and revenue projects
- **Family**: Personal and family projects
- **Wellness**: Health and fitness projects
- **Financial**: Budget and financial projects

Each project gets tagged with the appropriate domain for your existing email routing system.

## Next Steps

1. **Set up the N8N webhook workflow**
2. **Configure the Dify custom tool**
3. **Test the integration**
4. **Create additional tools** (Task Manager, Status Checker)
5. **Connect to LiveKit voice agent**

This tool will be the foundation for your voice-driven project management system!
