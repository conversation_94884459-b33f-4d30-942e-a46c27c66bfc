# Juno - Quick Reference Guide

## 🚀 Essential Files

### 📋 **Start Here**
- **`PROJECT_OVERVIEW.md`** - Complete project summary and status
- **`june-1st-summarization-voice-agent-to-n8n-work.md`** - Detailed session summary

### 🔧 **Core Implementation**
- **`agent.py`** - Main working voice agent (FINAL VERSION)
- **`requirements.txt`** - All dependencies
- **`.env`** - Environment variables (API keys)

### 🐛 **Debugging & Troubleshooting**
- **`troubleshooting-agent-errors.md`** - Complete error solutions guide
- **Reference this FIRST** when encountering issues

## ⚡ Quick Commands

### Start the Agent
```bash
cd Juno
source venv/bin/activate
python agent.py start
```

### Check Agent Status
- Agent logs show "registered worker" = SUCCESS
- Debug interface: `http://localhost:8081/debug`
- Test in LiveKit playground

### Kill Existing Processes (if port conflicts)
```bash
lsof -i :8081
kill -9 <PID>
```

## 🎯 What Works Right Now

✅ **Voice Recognition** - Real-time speech-to-text  
✅ **AI Conversation** - Natural language responses  
✅ **Voice Synthesis** - High-quality text-to-speech  
✅ **LiveKit Integration** - Stable cloud connectivity  
✅ **Multi-Process Architecture** - Scalable performance  

## 🔑 Required Environment Variables

```bash
LIVEKIT_URL=wss://first-project-2rpwr03w.livekit.cloud
LIVEKIT_API_KEY=your-livekit-key
LIVEKIT_API_SECRET=your-livekit-secret
OPENAI_API_KEY=your-openai-key
DEEPGRAM_API_KEY=your-deepgram-key
CARTESIA_API_KEY=your-cartesia-key
```

## 🎯 Next Development Phase

### Ready for Integration:
1. **Dify Connection** - Link voice agent to Dify platform
2. **N8N Workflows** - Automated project management
3. **Voice Commands** - Project creation, task management
4. **Workflow Automation** - End-to-end voice-driven processes

### Architecture Goal:
```
User Voice → LiveKit Agent → Dify → N8N → Project Management → Voice Response
```

## 📞 Test Commands

Try these in the LiveKit playground:
- "Hello Alex, how are you?"
- "What can you help me with?"
- "Tell me about yourself"

## 🆘 Emergency Debugging

1. **Check `troubleshooting-agent-errors.md`** first
2. **Verify environment variables** are set correctly
3. **Kill existing processes** if port conflicts
4. **Check agent logs** for specific error messages
5. **Use Context7 MCP** for API documentation

## 📁 File Organization

```
Juno/
├── 📋 PROJECT_OVERVIEW.md          # Complete project summary
├── 📋 QUICK_REFERENCE.md           # This file
├── 📋 june-1st-summarization...md  # Detailed session summary
├── 🐛 troubleshooting-agent-errors.md  # Debugging guide
├── 🔧 agent.py                     # MAIN WORKING AGENT
├── 🔧 requirements.txt             # Dependencies
├── 🔧 .env                         # API keys
├── 📁 venv/                        # Virtual environment
└── 📁 Agent Collaboration/         # Development history
```

## ✅ Success Checklist

Before continuing development:
- [ ] Agent starts without errors
- [ ] "registered worker" appears in logs
- [ ] Voice interaction works in playground
- [ ] All API keys are configured
- [ ] Debug interface accessible
- [ ] No port conflicts

## 🎊 Project Status: COMPLETE FOUNDATION

The voice agent foundation is **100% working** and ready for the next phase of Dify and N8N integration!
