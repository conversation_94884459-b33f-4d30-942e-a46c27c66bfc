# Voice Agent → Dify → N8n Prototype Setup

This is a simple prototype that connects your LiveKit voice agent to Dify, which can then trigger N8n workflows for project management.

## 🎯 **Architecture Overview**

```
User Voice → LiveKit Agent → Dify → N8n → Project Creation
```

## 🚀 **Quick Setup Steps**

### 1. **Configure Environment Variables**

Copy the example environment file:
```bash
cp .env.example .env
```

Edit `.env` and add your API keys:
```bash
# Required for basic voice agent
OPENAI_API_KEY=your-openai-api-key
CARTESIA_API_KEY=your-cartesia-api-key
DEEPGRAM_API_KEY=your-deepgram-api-key

# Required for Dify integration
DIFY_API_URL=http://localhost:5001/v1/chat-messages
DIFY_API_KEY=your-dify-api-key
```

### 2. **Test Dify Integration**

Test the Dify connection:
```bash
cd livekit-voice-agent
python3 dify_integration.py
```

### 3. **Run the Prototype Agent**

Start the voice agent with Dify integration:
```bash
python3 agent_with_dify.py start
```

## 🎤 **How It Works**

### **Voice Commands That Trigger Dify:**
- "Create a new project"
- "Set up a workflow"
- "Manage my tasks"
- "Organize my projects"

### **Voice Commands That Use OpenAI:**
- General conversation
- Questions not related to project management

### **What Happens:**

1. **User speaks** to the LiveKit agent
2. **Agent analyzes** if it's a project management request
3. **If PM request**: Routes to Dify → Dify can trigger N8n workflows
4. **If general**: Uses OpenAI for normal conversation
5. **Response** flows back to user via voice

## 🔧 **Customization**

### **Add More Project Management Keywords:**

Edit `agent_with_dify.py` line 67:
```python
pm_keywords = ["project", "create", "workflow", "task", "manage", "organize", "build", "setup"]
```

### **Configure Dify Workflows:**

In your Dify application:
1. Set up workflows that call N8n webhooks
2. Configure project creation templates
3. Add task management logic

### **N8n Integration:**

Your N8n workflows should:
1. Receive webhooks from Dify
2. Create project folders/files
3. Set up project templates
4. Send confirmation back to Dify

## 🧪 **Testing the Prototype**

### **Test Voice Commands:**

1. **Start the agent**: `python3 agent_with_dify.py start`
2. **Connect via LiveKit playground**
3. **Try these commands**:
   - "Hello Alex" (should use OpenAI)
   - "Create a new project called Voice AI" (should use Dify)
   - "What's the weather?" (should use OpenAI)
   - "Set up a workflow for my project" (should use Dify)

### **Check Logs:**

The agent will log which system it's using:
- `"Using OpenAI for general conversation"`
- `"Routing to Dify for project management"`

## 📋 **Next Steps**

1. **Set up Dify** with your project management workflows
2. **Configure N8n** to receive webhooks from Dify
3. **Test the full pipeline** with actual project creation
4. **Customize** the voice prompts and responses
5. **Add more** project management features

## 🔍 **Troubleshooting**

### **Agent Not Starting:**
- Check your API keys in `.env`
- Ensure all dependencies are installed

### **Dify Not Responding:**
- Verify `DIFY_API_URL` and `DIFY_API_KEY`
- Test with `python3 dify_integration.py`

### **No Voice Response:**
- Check LiveKit connection
- Verify Cartesia and DeepGram keys

This prototype gives you a **working foundation** to build your complete Voice → Dify → N8n → Project Management system! 🎉
