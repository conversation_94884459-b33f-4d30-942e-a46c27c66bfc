# Dify Project Management Agent Setup Guide

## Phase 1: Initial Dify Setup

### Step 1: Access Dify
- **Recommended**: Use Dify Cloud at https://cloud.dify.ai
- Sign up and create your workspace

### Step 2: Create Project Management Agent

1. **Click "Create App"**
2. **Select "Agent"** (not Chatbot or Workflow)
3. **Name**: "REXOS Project Manager"
4. **Description**: "AI agent for voice-driven project management and N8N workflow automation"

### Step 3: Configure Agent Prompt

Use this initial system prompt:

```
You are REXOS Project Manager, an AI assistant specialized in project management and task automation. You help users create, manage, and track projects through voice commands.

## Your Capabilities:
- Create new projects with detailed specifications
- Add tasks to existing projects
- Set deadlines and priorities
- Assign team members to tasks
- Track project progress
- Generate project reports
- Integrate with N8N workflows for automation

## Voice Command Examples You Handle:
- "Create a new project called [name]"
- "Add a task to [project] with deadline [date]"
- "What's the status of [project]?"
- "Assign [task] to [team member]"
- "Show me today's completed tasks"

## Response Style:
- Keep responses brief and conversational for voice interaction
- Always confirm actions before executing
- Provide clear status updates
- Ask for clarification when needed

## Integration Points:
- You can trigger N8N workflows for project creation
- You can update project databases
- You can send notifications to team members
- You can generate reports and summaries

When a user requests project management actions, extract the key information (project name, task details, deadlines, assignees) and prepare to execute the appropriate workflow.
```

### Step 4: Add Model Configuration

1. **Select your preferred LLM**:
   - OpenAI GPT-4 (recommended for complex reasoning)
   - Or GPT-3.5-turbo for faster responses
   - Configure temperature: 0.3 (for consistent responses)

### Step 5: Enable Function Calling

In the agent settings:
- **Enable "Function Calling"**
- This allows the agent to use tools and trigger workflows

## Phase 2: Create Custom Tools for N8N Integration

### Tool 1: Project Creator
**Purpose**: Create new projects in your system
**Integration**: Calls N8N webhook to create project

### Tool 2: Task Manager
**Purpose**: Add, update, and manage tasks
**Integration**: Updates your Supabase database via N8N

### Tool 3: Status Checker
**Purpose**: Query project and task status
**Integration**: Retrieves data from Supabase

### Tool 4: Team Coordinator
**Purpose**: Assign tasks and notify team members
**Integration**: Triggers notification workflows in N8N

## Phase 3: Webhook Configuration

### Incoming Webhooks (from LiveKit)
- **Endpoint**: `/api/v1/dify-webhook/project-management`
- **Purpose**: Receive voice commands from LiveKit agent
- **Format**: JSON with transcribed text and user context

### Outgoing Webhooks (to N8N)
- **Project Creation**: Webhook to N8N project creation workflow
- **Task Management**: Webhook to N8N task management workflow
- **Notifications**: Webhook to N8N notification workflow

## Next Steps

1. **Set up the basic agent in Dify**
2. **Test with simple text commands**
3. **Create the first custom tool (Project Creator)**
4. **Set up webhook integration with N8N**
5. **Connect to LiveKit voice agent**

## Integration Architecture

```
Voice Input → LiveKit Agent → Dify Agent → Custom Tools → N8N Workflows → Supabase/Actions
```

This setup leverages:
- Your existing N8N expertise
- Your proven Supabase data architecture
- Your working LiveKit voice agent
- Dify's agent orchestration capabilities

The result will be a voice-driven project management system that integrates seamlessly with your existing infrastructure.
