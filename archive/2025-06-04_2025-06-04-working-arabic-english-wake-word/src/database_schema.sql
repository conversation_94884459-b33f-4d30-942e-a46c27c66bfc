-- Juno Project - Database Schema
-- Version 1.0

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Meetings Table: Stores information about each meeting session
CREATE TABLE meetings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255),                         -- Optional title for the meeting
    start_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- When the meeting started
    end_time TIMESTAMP WITH TIME ZONE,            -- When the meeting ended
    participants JSONB,                         -- JSON object or array storing participant information (e.g., names, IDs)
    status VARCHAR(50) NOT NULL DEFAULT 'active', -- e.g., 'active', 'completed', 'archived'
    summary TEXT,                               -- For storing the generated meeting summary (Step 3: Meeting Summary Generation)
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Audio Segments Table: Stores transcribed audio chunks and related metadata
CREATE TABLE audio_segments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    meeting_id UUID NOT NULL REFERENCES meetings(id) ON DELETE CASCADE, -- Foreign key to meetings table
    start_time TIMESTAMP WITH TIME ZONE NOT NULL, -- Timestamp of when this audio segment began within the meeting
    duration_ms INTEGER,                        -- Duration of the audio segment in milliseconds
    audio_url TEXT,                             -- Optional: URL if raw audio is stored elsewhere
    transcription TEXT NOT NULL,                -- The transcribed text of the audio segment
    language_markers TEXT,                      -- Transcription with language markers (e.g., [EN]...[/EN] [AR]...[/AR]) from Step 2A
    speaker_id VARCHAR(100),                    -- Identifier for the speaker of this segment (if available)
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Meeting Events Table: Stores significant events detected during a meeting
CREATE TABLE meeting_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    meeting_id UUID NOT NULL REFERENCES meetings(id) ON DELETE CASCADE, -- Foreign key to meetings table
    event_type VARCHAR(100) NOT NULL,           -- Type of event (e.g., 'wake_word_detected', 'user_question', 'potential_action_item', 'detected_decision', 'extracted_quote', 'follow_up_commitment', 'next_meeting_topic')
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,  -- When the event occurred during the meeting
    content JSONB,                              -- Detailed content of the event (e.g., question text, action item details, decision text, quote text, follow-up details, suggested topic)
    source_segment_id UUID REFERENCES audio_segments(id) ON DELETE SET NULL, -- Optional: Link to the audio segment that triggered this event
    processed BOOLEAN DEFAULT FALSE,            -- Flag to indicate if this event has been further processed by other workflows (e.g., action item created in a task manager)
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance optimization

-- Meetings table
CREATE INDEX idx_meetings_start_time ON meetings(start_time DESC);
CREATE INDEX idx_meetings_status ON meetings(status);

-- Audio Segments table
CREATE INDEX idx_audio_segments_meeting_id ON audio_segments(meeting_id);
CREATE INDEX idx_audio_segments_start_time ON audio_segments(start_time);

-- Meeting Events table
CREATE INDEX idx_meeting_events_meeting_id ON meeting_events(meeting_id);
CREATE INDEX idx_meeting_events_event_type ON meeting_events(event_type);
CREATE INDEX idx_meeting_events_timestamp ON meeting_events(timestamp);
CREATE INDEX idx_meeting_events_processed ON meeting_events(processed) WHERE processed = FALSE; -- Useful for finding unprocessed events

-- Function to automatically update 'updated_at' timestamp
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for meetings table
CREATE TRIGGER set_timestamp_meetings
BEFORE UPDATE ON meetings
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- (Consider adding similar triggers for other tables if they will be updated frequently after creation)

COMMENT ON TABLE meetings IS 'Stores information about each meeting session, including status and summary.';
COMMENT ON COLUMN meetings.participants IS 'JSON object/array for participant details. Structure can be flexible, e.g., [{"id": "user1", "name": "Alice"}, {"id": "user2", "name": "Bob"}].';
COMMENT ON COLUMN meetings.summary IS 'Stores the AI-generated summary of the meeting.';

COMMENT ON TABLE audio_segments IS 'Stores individual transcribed audio chunks with language and speaker information.';
COMMENT ON COLUMN audio_segments.language_markers IS 'Transcription including [EN]...[/EN] [AR]...[/AR] style markers.';

COMMENT ON TABLE meeting_events IS 'Logs significant events detected or occurring during a meeting for further processing or analysis.';
COMMENT ON COLUMN meeting_events.event_type IS 'Categorizes the event, e.g., potential_action_item, detected_decision, extracted_quote.';
COMMENT ON COLUMN meeting_events.content IS 'JSONB field to store structured data specific to the event_type.';
COMMENT ON COLUMN meeting_events.source_segment_id IS 'Links event to the specific audio segment where it originated, if applicable.';
COMMENT ON COLUMN meeting_events.processed IS 'Indicates if an event has been handled by a subsequent workflow (e.g., action item created).';