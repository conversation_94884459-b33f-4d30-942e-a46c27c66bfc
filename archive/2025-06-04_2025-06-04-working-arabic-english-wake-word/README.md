# Active Development Folder

This is your main working directory for current development projects.

## Current Project
- **Status**: Ready for new development
- **Last Updated**: $(date)

## Workflow
1. All active development happens in this folder
2. When a development phase is complete, the entire folder gets archived
3. A fresh active folder is created for the next phase

## Archive Process
When development is complete:
1. Copy entire `active/` folder to `archive/YYYY-MM-DD_project-name/`
2. Clear or reset `active/` folder for next project
3. Update this README with new project info

## Structure
```
active/
├── README.md (this file)
├── src/          (source code)
├── docs/         (documentation)
├── tests/        (test files)
└── config/       (configuration files)
```
