"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2023 LiveKit, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
from . import data_stream_pb2
from . import e2ee_pb2
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
from . import handle_pb2
from . import participant_pb2
from . import stats_pb2
import sys
from . import track_pb2
import typing
from . import video_frame_pb2

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _IceTransportType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _IceTransportTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_IceTransportType.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    TRANSPORT_RELAY: _IceTransportType.ValueType  # 0
    TRANSPORT_NOHOST: _IceTransportType.ValueType  # 1
    TRANSPORT_ALL: _IceTransportType.ValueType  # 2

class IceTransportType(_IceTransportType, metaclass=_IceTransportTypeEnumTypeWrapper): ...

TRANSPORT_RELAY: IceTransportType.ValueType  # 0
TRANSPORT_NOHOST: IceTransportType.ValueType  # 1
TRANSPORT_ALL: IceTransportType.ValueType  # 2
global___IceTransportType = IceTransportType

class _ContinualGatheringPolicy:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ContinualGatheringPolicyEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ContinualGatheringPolicy.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    GATHER_ONCE: _ContinualGatheringPolicy.ValueType  # 0
    GATHER_CONTINUALLY: _ContinualGatheringPolicy.ValueType  # 1

class ContinualGatheringPolicy(_ContinualGatheringPolicy, metaclass=_ContinualGatheringPolicyEnumTypeWrapper): ...

GATHER_ONCE: ContinualGatheringPolicy.ValueType  # 0
GATHER_CONTINUALLY: ContinualGatheringPolicy.ValueType  # 1
global___ContinualGatheringPolicy = ContinualGatheringPolicy

class _ConnectionQuality:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ConnectionQualityEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ConnectionQuality.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    QUALITY_POOR: _ConnectionQuality.ValueType  # 0
    QUALITY_GOOD: _ConnectionQuality.ValueType  # 1
    QUALITY_EXCELLENT: _ConnectionQuality.ValueType  # 2
    QUALITY_LOST: _ConnectionQuality.ValueType  # 3

class ConnectionQuality(_ConnectionQuality, metaclass=_ConnectionQualityEnumTypeWrapper):
    """
    Room
    """

QUALITY_POOR: ConnectionQuality.ValueType  # 0
QUALITY_GOOD: ConnectionQuality.ValueType  # 1
QUALITY_EXCELLENT: ConnectionQuality.ValueType  # 2
QUALITY_LOST: ConnectionQuality.ValueType  # 3
global___ConnectionQuality = ConnectionQuality

class _ConnectionState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ConnectionStateEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ConnectionState.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    CONN_DISCONNECTED: _ConnectionState.ValueType  # 0
    CONN_CONNECTED: _ConnectionState.ValueType  # 1
    CONN_RECONNECTING: _ConnectionState.ValueType  # 2

class ConnectionState(_ConnectionState, metaclass=_ConnectionStateEnumTypeWrapper): ...

CONN_DISCONNECTED: ConnectionState.ValueType  # 0
CONN_CONNECTED: ConnectionState.ValueType  # 1
CONN_RECONNECTING: ConnectionState.ValueType  # 2
global___ConnectionState = ConnectionState

class _DataPacketKind:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DataPacketKindEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_DataPacketKind.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    KIND_LOSSY: _DataPacketKind.ValueType  # 0
    KIND_RELIABLE: _DataPacketKind.ValueType  # 1

class DataPacketKind(_DataPacketKind, metaclass=_DataPacketKindEnumTypeWrapper): ...

KIND_LOSSY: DataPacketKind.ValueType  # 0
KIND_RELIABLE: DataPacketKind.ValueType  # 1
global___DataPacketKind = DataPacketKind

@typing.final
class ConnectRequest(google.protobuf.message.Message):
    """Connect to a new LiveKit room"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    URL_FIELD_NUMBER: builtins.int
    TOKEN_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    url: builtins.str
    token: builtins.str
    @property
    def options(self) -> global___RoomOptions: ...
    def __init__(
        self,
        *,
        url: builtins.str | None = ...,
        token: builtins.str | None = ...,
        options: global___RoomOptions | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["options", b"options", "token", b"token", "url", b"url"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["options", b"options", "token", b"token", "url", b"url"]) -> None: ...

global___ConnectRequest = ConnectRequest

@typing.final
class ConnectResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___ConnectResponse = ConnectResponse

@typing.final
class ConnectCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class ParticipantWithTracks(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        PARTICIPANT_FIELD_NUMBER: builtins.int
        PUBLICATIONS_FIELD_NUMBER: builtins.int
        @property
        def participant(self) -> participant_pb2.OwnedParticipant: ...
        @property
        def publications(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[track_pb2.OwnedTrackPublication]:
            """TrackInfo are not needed here, if we're subscribed to a track, the FfiServer will send
            a TrackSubscribed event
            """

        def __init__(
            self,
            *,
            participant: participant_pb2.OwnedParticipant | None = ...,
            publications: collections.abc.Iterable[track_pb2.OwnedTrackPublication] | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["participant", b"participant"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["participant", b"participant", "publications", b"publications"]) -> None: ...

    @typing.final
    class Result(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        ROOM_FIELD_NUMBER: builtins.int
        LOCAL_PARTICIPANT_FIELD_NUMBER: builtins.int
        PARTICIPANTS_FIELD_NUMBER: builtins.int
        @property
        def room(self) -> global___OwnedRoom: ...
        @property
        def local_participant(self) -> participant_pb2.OwnedParticipant: ...
        @property
        def participants(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ConnectCallback.ParticipantWithTracks]: ...
        def __init__(
            self,
            *,
            room: global___OwnedRoom | None = ...,
            local_participant: participant_pb2.OwnedParticipant | None = ...,
            participants: collections.abc.Iterable[global___ConnectCallback.ParticipantWithTracks] | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["local_participant", b"local_participant", "room", b"room"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["local_participant", b"local_participant", "participants", b"participants", "room", b"room"]) -> None: ...

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    RESULT_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    @property
    def result(self) -> global___ConnectCallback.Result: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
        result: global___ConnectCallback.Result | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "message", b"message", "result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "message", b"message", "result", b"result"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["error", "result"] | None: ...

global___ConnectCallback = ConnectCallback

@typing.final
class DisconnectRequest(google.protobuf.message.Message):
    """Disconnect from the a room"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ROOM_HANDLE_FIELD_NUMBER: builtins.int
    room_handle: builtins.int
    def __init__(
        self,
        *,
        room_handle: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["room_handle", b"room_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["room_handle", b"room_handle"]) -> None: ...

global___DisconnectRequest = DisconnectRequest

@typing.final
class DisconnectResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___DisconnectResponse = DisconnectResponse

@typing.final
class DisconnectCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___DisconnectCallback = DisconnectCallback

@typing.final
class PublishTrackRequest(google.protobuf.message.Message):
    """Publish a track to the room"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    TRACK_HANDLE_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    track_handle: builtins.int
    @property
    def options(self) -> global___TrackPublishOptions: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        track_handle: builtins.int | None = ...,
        options: global___TrackPublishOptions | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "options", b"options", "track_handle", b"track_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "options", b"options", "track_handle", b"track_handle"]) -> None: ...

global___PublishTrackRequest = PublishTrackRequest

@typing.final
class PublishTrackResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___PublishTrackResponse = PublishTrackResponse

@typing.final
class PublishTrackCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    PUBLICATION_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    @property
    def publication(self) -> track_pb2.OwnedTrackPublication: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
        publication: track_pb2.OwnedTrackPublication | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "message", b"message", "publication", b"publication"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "message", b"message", "publication", b"publication"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["error", "publication"] | None: ...

global___PublishTrackCallback = PublishTrackCallback

@typing.final
class UnpublishTrackRequest(google.protobuf.message.Message):
    """Unpublish a track from the room"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    TRACK_SID_FIELD_NUMBER: builtins.int
    STOP_ON_UNPUBLISH_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    track_sid: builtins.str
    stop_on_unpublish: builtins.bool
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        track_sid: builtins.str | None = ...,
        stop_on_unpublish: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "stop_on_unpublish", b"stop_on_unpublish", "track_sid", b"track_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "stop_on_unpublish", b"stop_on_unpublish", "track_sid", b"track_sid"]) -> None: ...

global___UnpublishTrackRequest = UnpublishTrackRequest

@typing.final
class UnpublishTrackResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___UnpublishTrackResponse = UnpublishTrackResponse

@typing.final
class UnpublishTrackCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___UnpublishTrackCallback = UnpublishTrackCallback

@typing.final
class PublishDataRequest(google.protobuf.message.Message):
    """Publish data to other participants"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    DATA_PTR_FIELD_NUMBER: builtins.int
    DATA_LEN_FIELD_NUMBER: builtins.int
    RELIABLE_FIELD_NUMBER: builtins.int
    DESTINATION_SIDS_FIELD_NUMBER: builtins.int
    TOPIC_FIELD_NUMBER: builtins.int
    DESTINATION_IDENTITIES_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    data_ptr: builtins.int
    data_len: builtins.int
    reliable: builtins.bool
    topic: builtins.str
    @property
    def destination_sids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def destination_identities(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        data_ptr: builtins.int | None = ...,
        data_len: builtins.int | None = ...,
        reliable: builtins.bool | None = ...,
        destination_sids: collections.abc.Iterable[builtins.str] | None = ...,
        topic: builtins.str | None = ...,
        destination_identities: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data_len", b"data_len", "data_ptr", b"data_ptr", "local_participant_handle", b"local_participant_handle", "reliable", b"reliable", "topic", b"topic"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data_len", b"data_len", "data_ptr", b"data_ptr", "destination_identities", b"destination_identities", "destination_sids", b"destination_sids", "local_participant_handle", b"local_participant_handle", "reliable", b"reliable", "topic", b"topic"]) -> None: ...

global___PublishDataRequest = PublishDataRequest

@typing.final
class PublishDataResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___PublishDataResponse = PublishDataResponse

@typing.final
class PublishDataCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___PublishDataCallback = PublishDataCallback

@typing.final
class PublishTranscriptionRequest(google.protobuf.message.Message):
    """Publish transcription messages to room"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    TRACK_ID_FIELD_NUMBER: builtins.int
    SEGMENTS_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    participant_identity: builtins.str
    track_id: builtins.str
    @property
    def segments(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___TranscriptionSegment]: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        participant_identity: builtins.str | None = ...,
        track_id: builtins.str | None = ...,
        segments: collections.abc.Iterable[global___TranscriptionSegment] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "participant_identity", b"participant_identity", "track_id", b"track_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "participant_identity", b"participant_identity", "segments", b"segments", "track_id", b"track_id"]) -> None: ...

global___PublishTranscriptionRequest = PublishTranscriptionRequest

@typing.final
class PublishTranscriptionResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___PublishTranscriptionResponse = PublishTranscriptionResponse

@typing.final
class PublishTranscriptionCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___PublishTranscriptionCallback = PublishTranscriptionCallback

@typing.final
class PublishSipDtmfRequest(google.protobuf.message.Message):
    """Publish Sip DTMF messages to other participants"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    CODE_FIELD_NUMBER: builtins.int
    DIGIT_FIELD_NUMBER: builtins.int
    DESTINATION_IDENTITIES_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    code: builtins.int
    digit: builtins.str
    @property
    def destination_identities(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        code: builtins.int | None = ...,
        digit: builtins.str | None = ...,
        destination_identities: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["code", b"code", "digit", b"digit", "local_participant_handle", b"local_participant_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["code", b"code", "destination_identities", b"destination_identities", "digit", b"digit", "local_participant_handle", b"local_participant_handle"]) -> None: ...

global___PublishSipDtmfRequest = PublishSipDtmfRequest

@typing.final
class PublishSipDtmfResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___PublishSipDtmfResponse = PublishSipDtmfResponse

@typing.final
class PublishSipDtmfCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___PublishSipDtmfCallback = PublishSipDtmfCallback

@typing.final
class SetLocalMetadataRequest(google.protobuf.message.Message):
    """Change the local participant's metadata"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    metadata: builtins.str
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        metadata: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "metadata", b"metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "metadata", b"metadata"]) -> None: ...

global___SetLocalMetadataRequest = SetLocalMetadataRequest

@typing.final
class SetLocalMetadataResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___SetLocalMetadataResponse = SetLocalMetadataResponse

@typing.final
class SetLocalMetadataCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___SetLocalMetadataCallback = SetLocalMetadataCallback

@typing.final
class SendChatMessageRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    MESSAGE_FIELD_NUMBER: builtins.int
    DESTINATION_IDENTITIES_FIELD_NUMBER: builtins.int
    SENDER_IDENTITY_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    message: builtins.str
    sender_identity: builtins.str
    @property
    def destination_identities(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        message: builtins.str | None = ...,
        destination_identities: collections.abc.Iterable[builtins.str] | None = ...,
        sender_identity: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "message", b"message", "sender_identity", b"sender_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["destination_identities", b"destination_identities", "local_participant_handle", b"local_participant_handle", "message", b"message", "sender_identity", b"sender_identity"]) -> None: ...

global___SendChatMessageRequest = SendChatMessageRequest

@typing.final
class EditChatMessageRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    EDIT_TEXT_FIELD_NUMBER: builtins.int
    ORIGINAL_MESSAGE_FIELD_NUMBER: builtins.int
    DESTINATION_IDENTITIES_FIELD_NUMBER: builtins.int
    SENDER_IDENTITY_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    edit_text: builtins.str
    sender_identity: builtins.str
    @property
    def original_message(self) -> global___ChatMessage: ...
    @property
    def destination_identities(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        edit_text: builtins.str | None = ...,
        original_message: global___ChatMessage | None = ...,
        destination_identities: collections.abc.Iterable[builtins.str] | None = ...,
        sender_identity: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["edit_text", b"edit_text", "local_participant_handle", b"local_participant_handle", "original_message", b"original_message", "sender_identity", b"sender_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["destination_identities", b"destination_identities", "edit_text", b"edit_text", "local_participant_handle", b"local_participant_handle", "original_message", b"original_message", "sender_identity", b"sender_identity"]) -> None: ...

global___EditChatMessageRequest = EditChatMessageRequest

@typing.final
class SendChatMessageResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___SendChatMessageResponse = SendChatMessageResponse

@typing.final
class SendChatMessageCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    CHAT_MESSAGE_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    @property
    def chat_message(self) -> global___ChatMessage: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
        chat_message: global___ChatMessage | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "chat_message", b"chat_message", "error", b"error", "message", b"message"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "chat_message", b"chat_message", "error", b"error", "message", b"message"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["error", "chat_message"] | None: ...

global___SendChatMessageCallback = SendChatMessageCallback

@typing.final
class SetLocalAttributesRequest(google.protobuf.message.Message):
    """Change the local participant's attributes"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    @property
    def attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___AttributesEntry]: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        attributes: collections.abc.Iterable[global___AttributesEntry] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["attributes", b"attributes", "local_participant_handle", b"local_participant_handle"]) -> None: ...

global___SetLocalAttributesRequest = SetLocalAttributesRequest

@typing.final
class AttributesEntry(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    key: builtins.str
    value: builtins.str
    def __init__(
        self,
        *,
        key: builtins.str | None = ...,
        value: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

global___AttributesEntry = AttributesEntry

@typing.final
class SetLocalAttributesResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___SetLocalAttributesResponse = SetLocalAttributesResponse

@typing.final
class SetLocalAttributesCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___SetLocalAttributesCallback = SetLocalAttributesCallback

@typing.final
class SetLocalNameRequest(google.protobuf.message.Message):
    """Change the local participant's name"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    name: builtins.str
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        name: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "name", b"name"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "name", b"name"]) -> None: ...

global___SetLocalNameRequest = SetLocalNameRequest

@typing.final
class SetLocalNameResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___SetLocalNameResponse = SetLocalNameResponse

@typing.final
class SetLocalNameCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___SetLocalNameCallback = SetLocalNameCallback

@typing.final
class SetSubscribedRequest(google.protobuf.message.Message):
    """Change the "desire" to subs2ribe to a track"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SUBSCRIBE_FIELD_NUMBER: builtins.int
    PUBLICATION_HANDLE_FIELD_NUMBER: builtins.int
    subscribe: builtins.bool
    publication_handle: builtins.int
    def __init__(
        self,
        *,
        subscribe: builtins.bool | None = ...,
        publication_handle: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["publication_handle", b"publication_handle", "subscribe", b"subscribe"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["publication_handle", b"publication_handle", "subscribe", b"subscribe"]) -> None: ...

global___SetSubscribedRequest = SetSubscribedRequest

@typing.final
class SetSubscribedResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___SetSubscribedResponse = SetSubscribedResponse

@typing.final
class GetSessionStatsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ROOM_HANDLE_FIELD_NUMBER: builtins.int
    room_handle: builtins.int
    def __init__(
        self,
        *,
        room_handle: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["room_handle", b"room_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["room_handle", b"room_handle"]) -> None: ...

global___GetSessionStatsRequest = GetSessionStatsRequest

@typing.final
class GetSessionStatsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___GetSessionStatsResponse = GetSessionStatsResponse

@typing.final
class GetSessionStatsCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class Result(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        PUBLISHER_STATS_FIELD_NUMBER: builtins.int
        SUBSCRIBER_STATS_FIELD_NUMBER: builtins.int
        @property
        def publisher_stats(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[stats_pb2.RtcStats]: ...
        @property
        def subscriber_stats(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[stats_pb2.RtcStats]: ...
        def __init__(
            self,
            *,
            publisher_stats: collections.abc.Iterable[stats_pb2.RtcStats] | None = ...,
            subscriber_stats: collections.abc.Iterable[stats_pb2.RtcStats] | None = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["publisher_stats", b"publisher_stats", "subscriber_stats", b"subscriber_stats"]) -> None: ...

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    RESULT_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    @property
    def result(self) -> global___GetSessionStatsCallback.Result: ...
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
        result: global___GetSessionStatsCallback.Result | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "message", b"message", "result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error", "message", b"message", "result", b"result"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["error", "result"] | None: ...

global___GetSessionStatsCallback = GetSessionStatsCallback

@typing.final
class VideoEncoding(google.protobuf.message.Message):
    """
    Options
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MAX_BITRATE_FIELD_NUMBER: builtins.int
    MAX_FRAMERATE_FIELD_NUMBER: builtins.int
    max_bitrate: builtins.int
    max_framerate: builtins.float
    def __init__(
        self,
        *,
        max_bitrate: builtins.int | None = ...,
        max_framerate: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["max_bitrate", b"max_bitrate", "max_framerate", b"max_framerate"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["max_bitrate", b"max_bitrate", "max_framerate", b"max_framerate"]) -> None: ...

global___VideoEncoding = VideoEncoding

@typing.final
class AudioEncoding(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MAX_BITRATE_FIELD_NUMBER: builtins.int
    max_bitrate: builtins.int
    def __init__(
        self,
        *,
        max_bitrate: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["max_bitrate", b"max_bitrate"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["max_bitrate", b"max_bitrate"]) -> None: ...

global___AudioEncoding = AudioEncoding

@typing.final
class TrackPublishOptions(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VIDEO_ENCODING_FIELD_NUMBER: builtins.int
    AUDIO_ENCODING_FIELD_NUMBER: builtins.int
    VIDEO_CODEC_FIELD_NUMBER: builtins.int
    DTX_FIELD_NUMBER: builtins.int
    RED_FIELD_NUMBER: builtins.int
    SIMULCAST_FIELD_NUMBER: builtins.int
    SOURCE_FIELD_NUMBER: builtins.int
    STREAM_FIELD_NUMBER: builtins.int
    video_codec: video_frame_pb2.VideoCodec.ValueType
    dtx: builtins.bool
    red: builtins.bool
    simulcast: builtins.bool
    source: track_pb2.TrackSource.ValueType
    stream: builtins.str
    @property
    def video_encoding(self) -> global___VideoEncoding:
        """encodings are optional"""

    @property
    def audio_encoding(self) -> global___AudioEncoding: ...
    def __init__(
        self,
        *,
        video_encoding: global___VideoEncoding | None = ...,
        audio_encoding: global___AudioEncoding | None = ...,
        video_codec: video_frame_pb2.VideoCodec.ValueType | None = ...,
        dtx: builtins.bool | None = ...,
        red: builtins.bool | None = ...,
        simulcast: builtins.bool | None = ...,
        source: track_pb2.TrackSource.ValueType | None = ...,
        stream: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["audio_encoding", b"audio_encoding", "dtx", b"dtx", "red", b"red", "simulcast", b"simulcast", "source", b"source", "stream", b"stream", "video_codec", b"video_codec", "video_encoding", b"video_encoding"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["audio_encoding", b"audio_encoding", "dtx", b"dtx", "red", b"red", "simulcast", b"simulcast", "source", b"source", "stream", b"stream", "video_codec", b"video_codec", "video_encoding", b"video_encoding"]) -> None: ...

global___TrackPublishOptions = TrackPublishOptions

@typing.final
class IceServer(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    URLS_FIELD_NUMBER: builtins.int
    USERNAME_FIELD_NUMBER: builtins.int
    PASSWORD_FIELD_NUMBER: builtins.int
    username: builtins.str
    password: builtins.str
    @property
    def urls(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        urls: collections.abc.Iterable[builtins.str] | None = ...,
        username: builtins.str | None = ...,
        password: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["password", b"password", "username", b"username"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["password", b"password", "urls", b"urls", "username", b"username"]) -> None: ...

global___IceServer = IceServer

@typing.final
class RtcConfig(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ICE_TRANSPORT_TYPE_FIELD_NUMBER: builtins.int
    CONTINUAL_GATHERING_POLICY_FIELD_NUMBER: builtins.int
    ICE_SERVERS_FIELD_NUMBER: builtins.int
    ice_transport_type: global___IceTransportType.ValueType
    continual_gathering_policy: global___ContinualGatheringPolicy.ValueType
    @property
    def ice_servers(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___IceServer]:
        """empty fallback to default"""

    def __init__(
        self,
        *,
        ice_transport_type: global___IceTransportType.ValueType | None = ...,
        continual_gathering_policy: global___ContinualGatheringPolicy.ValueType | None = ...,
        ice_servers: collections.abc.Iterable[global___IceServer] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["continual_gathering_policy", b"continual_gathering_policy", "ice_transport_type", b"ice_transport_type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["continual_gathering_policy", b"continual_gathering_policy", "ice_servers", b"ice_servers", "ice_transport_type", b"ice_transport_type"]) -> None: ...

global___RtcConfig = RtcConfig

@typing.final
class RoomOptions(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    AUTO_SUBSCRIBE_FIELD_NUMBER: builtins.int
    ADAPTIVE_STREAM_FIELD_NUMBER: builtins.int
    DYNACAST_FIELD_NUMBER: builtins.int
    E2EE_FIELD_NUMBER: builtins.int
    RTC_CONFIG_FIELD_NUMBER: builtins.int
    JOIN_RETRIES_FIELD_NUMBER: builtins.int
    auto_subscribe: builtins.bool
    adaptive_stream: builtins.bool
    dynacast: builtins.bool
    join_retries: builtins.int
    @property
    def e2ee(self) -> e2ee_pb2.E2eeOptions: ...
    @property
    def rtc_config(self) -> global___RtcConfig:
        """allow to setup a custom RtcConfiguration"""

    def __init__(
        self,
        *,
        auto_subscribe: builtins.bool | None = ...,
        adaptive_stream: builtins.bool | None = ...,
        dynacast: builtins.bool | None = ...,
        e2ee: e2ee_pb2.E2eeOptions | None = ...,
        rtc_config: global___RtcConfig | None = ...,
        join_retries: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["adaptive_stream", b"adaptive_stream", "auto_subscribe", b"auto_subscribe", "dynacast", b"dynacast", "e2ee", b"e2ee", "join_retries", b"join_retries", "rtc_config", b"rtc_config"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["adaptive_stream", b"adaptive_stream", "auto_subscribe", b"auto_subscribe", "dynacast", b"dynacast", "e2ee", b"e2ee", "join_retries", b"join_retries", "rtc_config", b"rtc_config"]) -> None: ...

global___RoomOptions = RoomOptions

@typing.final
class TranscriptionSegment(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    TEXT_FIELD_NUMBER: builtins.int
    START_TIME_FIELD_NUMBER: builtins.int
    END_TIME_FIELD_NUMBER: builtins.int
    FINAL_FIELD_NUMBER: builtins.int
    LANGUAGE_FIELD_NUMBER: builtins.int
    id: builtins.str
    text: builtins.str
    start_time: builtins.int
    end_time: builtins.int
    final: builtins.bool
    language: builtins.str
    def __init__(
        self,
        *,
        id: builtins.str | None = ...,
        text: builtins.str | None = ...,
        start_time: builtins.int | None = ...,
        end_time: builtins.int | None = ...,
        final: builtins.bool | None = ...,
        language: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["end_time", b"end_time", "final", b"final", "id", b"id", "language", b"language", "start_time", b"start_time", "text", b"text"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["end_time", b"end_time", "final", b"final", "id", b"id", "language", b"language", "start_time", b"start_time", "text", b"text"]) -> None: ...

global___TranscriptionSegment = TranscriptionSegment

@typing.final
class BufferInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_PTR_FIELD_NUMBER: builtins.int
    DATA_LEN_FIELD_NUMBER: builtins.int
    data_ptr: builtins.int
    data_len: builtins.int
    def __init__(
        self,
        *,
        data_ptr: builtins.int | None = ...,
        data_len: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data_len", b"data_len", "data_ptr", b"data_ptr"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data_len", b"data_len", "data_ptr", b"data_ptr"]) -> None: ...

global___BufferInfo = BufferInfo

@typing.final
class OwnedBuffer(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def data(self) -> global___BufferInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        data: global___BufferInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data", "handle", b"handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data", "handle", b"handle"]) -> None: ...

global___OwnedBuffer = OwnedBuffer

@typing.final
class RoomEvent(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ROOM_HANDLE_FIELD_NUMBER: builtins.int
    PARTICIPANT_CONNECTED_FIELD_NUMBER: builtins.int
    PARTICIPANT_DISCONNECTED_FIELD_NUMBER: builtins.int
    LOCAL_TRACK_PUBLISHED_FIELD_NUMBER: builtins.int
    LOCAL_TRACK_UNPUBLISHED_FIELD_NUMBER: builtins.int
    LOCAL_TRACK_SUBSCRIBED_FIELD_NUMBER: builtins.int
    TRACK_PUBLISHED_FIELD_NUMBER: builtins.int
    TRACK_UNPUBLISHED_FIELD_NUMBER: builtins.int
    TRACK_SUBSCRIBED_FIELD_NUMBER: builtins.int
    TRACK_UNSUBSCRIBED_FIELD_NUMBER: builtins.int
    TRACK_SUBSCRIPTION_FAILED_FIELD_NUMBER: builtins.int
    TRACK_MUTED_FIELD_NUMBER: builtins.int
    TRACK_UNMUTED_FIELD_NUMBER: builtins.int
    ACTIVE_SPEAKERS_CHANGED_FIELD_NUMBER: builtins.int
    ROOM_METADATA_CHANGED_FIELD_NUMBER: builtins.int
    ROOM_SID_CHANGED_FIELD_NUMBER: builtins.int
    PARTICIPANT_METADATA_CHANGED_FIELD_NUMBER: builtins.int
    PARTICIPANT_NAME_CHANGED_FIELD_NUMBER: builtins.int
    PARTICIPANT_ATTRIBUTES_CHANGED_FIELD_NUMBER: builtins.int
    CONNECTION_QUALITY_CHANGED_FIELD_NUMBER: builtins.int
    CONNECTION_STATE_CHANGED_FIELD_NUMBER: builtins.int
    DISCONNECTED_FIELD_NUMBER: builtins.int
    RECONNECTING_FIELD_NUMBER: builtins.int
    RECONNECTED_FIELD_NUMBER: builtins.int
    E2EE_STATE_CHANGED_FIELD_NUMBER: builtins.int
    EOS_FIELD_NUMBER: builtins.int
    DATA_PACKET_RECEIVED_FIELD_NUMBER: builtins.int
    TRANSCRIPTION_RECEIVED_FIELD_NUMBER: builtins.int
    CHAT_MESSAGE_FIELD_NUMBER: builtins.int
    STREAM_HEADER_RECEIVED_FIELD_NUMBER: builtins.int
    STREAM_CHUNK_RECEIVED_FIELD_NUMBER: builtins.int
    STREAM_TRAILER_RECEIVED_FIELD_NUMBER: builtins.int
    DATA_CHANNEL_LOW_THRESHOLD_CHANGED_FIELD_NUMBER: builtins.int
    BYTE_STREAM_OPENED_FIELD_NUMBER: builtins.int
    TEXT_STREAM_OPENED_FIELD_NUMBER: builtins.int
    room_handle: builtins.int
    @property
    def participant_connected(self) -> global___ParticipantConnected: ...
    @property
    def participant_disconnected(self) -> global___ParticipantDisconnected: ...
    @property
    def local_track_published(self) -> global___LocalTrackPublished: ...
    @property
    def local_track_unpublished(self) -> global___LocalTrackUnpublished: ...
    @property
    def local_track_subscribed(self) -> global___LocalTrackSubscribed: ...
    @property
    def track_published(self) -> global___TrackPublished: ...
    @property
    def track_unpublished(self) -> global___TrackUnpublished: ...
    @property
    def track_subscribed(self) -> global___TrackSubscribed: ...
    @property
    def track_unsubscribed(self) -> global___TrackUnsubscribed: ...
    @property
    def track_subscription_failed(self) -> global___TrackSubscriptionFailed: ...
    @property
    def track_muted(self) -> global___TrackMuted: ...
    @property
    def track_unmuted(self) -> global___TrackUnmuted: ...
    @property
    def active_speakers_changed(self) -> global___ActiveSpeakersChanged: ...
    @property
    def room_metadata_changed(self) -> global___RoomMetadataChanged: ...
    @property
    def room_sid_changed(self) -> global___RoomSidChanged: ...
    @property
    def participant_metadata_changed(self) -> global___ParticipantMetadataChanged: ...
    @property
    def participant_name_changed(self) -> global___ParticipantNameChanged: ...
    @property
    def participant_attributes_changed(self) -> global___ParticipantAttributesChanged: ...
    @property
    def connection_quality_changed(self) -> global___ConnectionQualityChanged: ...
    @property
    def connection_state_changed(self) -> global___ConnectionStateChanged: ...
    @property
    def disconnected(self) -> global___Disconnected:
        """Connected connected = 21;"""

    @property
    def reconnecting(self) -> global___Reconnecting: ...
    @property
    def reconnected(self) -> global___Reconnected: ...
    @property
    def e2ee_state_changed(self) -> global___E2eeStateChanged: ...
    @property
    def eos(self) -> global___RoomEOS:
        """The stream of room events has ended"""

    @property
    def data_packet_received(self) -> global___DataPacketReceived: ...
    @property
    def transcription_received(self) -> global___TranscriptionReceived: ...
    @property
    def chat_message(self) -> global___ChatMessageReceived: ...
    @property
    def stream_header_received(self) -> global___DataStreamHeaderReceived:
        """Data stream (low level)"""

    @property
    def stream_chunk_received(self) -> global___DataStreamChunkReceived: ...
    @property
    def stream_trailer_received(self) -> global___DataStreamTrailerReceived: ...
    @property
    def data_channel_low_threshold_changed(self) -> global___DataChannelBufferedAmountLowThresholdChanged: ...
    @property
    def byte_stream_opened(self) -> global___ByteStreamOpened:
        """Data stream (high level)"""

    @property
    def text_stream_opened(self) -> global___TextStreamOpened: ...
    def __init__(
        self,
        *,
        room_handle: builtins.int | None = ...,
        participant_connected: global___ParticipantConnected | None = ...,
        participant_disconnected: global___ParticipantDisconnected | None = ...,
        local_track_published: global___LocalTrackPublished | None = ...,
        local_track_unpublished: global___LocalTrackUnpublished | None = ...,
        local_track_subscribed: global___LocalTrackSubscribed | None = ...,
        track_published: global___TrackPublished | None = ...,
        track_unpublished: global___TrackUnpublished | None = ...,
        track_subscribed: global___TrackSubscribed | None = ...,
        track_unsubscribed: global___TrackUnsubscribed | None = ...,
        track_subscription_failed: global___TrackSubscriptionFailed | None = ...,
        track_muted: global___TrackMuted | None = ...,
        track_unmuted: global___TrackUnmuted | None = ...,
        active_speakers_changed: global___ActiveSpeakersChanged | None = ...,
        room_metadata_changed: global___RoomMetadataChanged | None = ...,
        room_sid_changed: global___RoomSidChanged | None = ...,
        participant_metadata_changed: global___ParticipantMetadataChanged | None = ...,
        participant_name_changed: global___ParticipantNameChanged | None = ...,
        participant_attributes_changed: global___ParticipantAttributesChanged | None = ...,
        connection_quality_changed: global___ConnectionQualityChanged | None = ...,
        connection_state_changed: global___ConnectionStateChanged | None = ...,
        disconnected: global___Disconnected | None = ...,
        reconnecting: global___Reconnecting | None = ...,
        reconnected: global___Reconnected | None = ...,
        e2ee_state_changed: global___E2eeStateChanged | None = ...,
        eos: global___RoomEOS | None = ...,
        data_packet_received: global___DataPacketReceived | None = ...,
        transcription_received: global___TranscriptionReceived | None = ...,
        chat_message: global___ChatMessageReceived | None = ...,
        stream_header_received: global___DataStreamHeaderReceived | None = ...,
        stream_chunk_received: global___DataStreamChunkReceived | None = ...,
        stream_trailer_received: global___DataStreamTrailerReceived | None = ...,
        data_channel_low_threshold_changed: global___DataChannelBufferedAmountLowThresholdChanged | None = ...,
        byte_stream_opened: global___ByteStreamOpened | None = ...,
        text_stream_opened: global___TextStreamOpened | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["active_speakers_changed", b"active_speakers_changed", "byte_stream_opened", b"byte_stream_opened", "chat_message", b"chat_message", "connection_quality_changed", b"connection_quality_changed", "connection_state_changed", b"connection_state_changed", "data_channel_low_threshold_changed", b"data_channel_low_threshold_changed", "data_packet_received", b"data_packet_received", "disconnected", b"disconnected", "e2ee_state_changed", b"e2ee_state_changed", "eos", b"eos", "local_track_published", b"local_track_published", "local_track_subscribed", b"local_track_subscribed", "local_track_unpublished", b"local_track_unpublished", "message", b"message", "participant_attributes_changed", b"participant_attributes_changed", "participant_connected", b"participant_connected", "participant_disconnected", b"participant_disconnected", "participant_metadata_changed", b"participant_metadata_changed", "participant_name_changed", b"participant_name_changed", "reconnected", b"reconnected", "reconnecting", b"reconnecting", "room_handle", b"room_handle", "room_metadata_changed", b"room_metadata_changed", "room_sid_changed", b"room_sid_changed", "stream_chunk_received", b"stream_chunk_received", "stream_header_received", b"stream_header_received", "stream_trailer_received", b"stream_trailer_received", "text_stream_opened", b"text_stream_opened", "track_muted", b"track_muted", "track_published", b"track_published", "track_subscribed", b"track_subscribed", "track_subscription_failed", b"track_subscription_failed", "track_unmuted", b"track_unmuted", "track_unpublished", b"track_unpublished", "track_unsubscribed", b"track_unsubscribed", "transcription_received", b"transcription_received"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["active_speakers_changed", b"active_speakers_changed", "byte_stream_opened", b"byte_stream_opened", "chat_message", b"chat_message", "connection_quality_changed", b"connection_quality_changed", "connection_state_changed", b"connection_state_changed", "data_channel_low_threshold_changed", b"data_channel_low_threshold_changed", "data_packet_received", b"data_packet_received", "disconnected", b"disconnected", "e2ee_state_changed", b"e2ee_state_changed", "eos", b"eos", "local_track_published", b"local_track_published", "local_track_subscribed", b"local_track_subscribed", "local_track_unpublished", b"local_track_unpublished", "message", b"message", "participant_attributes_changed", b"participant_attributes_changed", "participant_connected", b"participant_connected", "participant_disconnected", b"participant_disconnected", "participant_metadata_changed", b"participant_metadata_changed", "participant_name_changed", b"participant_name_changed", "reconnected", b"reconnected", "reconnecting", b"reconnecting", "room_handle", b"room_handle", "room_metadata_changed", b"room_metadata_changed", "room_sid_changed", b"room_sid_changed", "stream_chunk_received", b"stream_chunk_received", "stream_header_received", b"stream_header_received", "stream_trailer_received", b"stream_trailer_received", "text_stream_opened", b"text_stream_opened", "track_muted", b"track_muted", "track_published", b"track_published", "track_subscribed", b"track_subscribed", "track_subscription_failed", b"track_subscription_failed", "track_unmuted", b"track_unmuted", "track_unpublished", b"track_unpublished", "track_unsubscribed", b"track_unsubscribed", "transcription_received", b"transcription_received"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["participant_connected", "participant_disconnected", "local_track_published", "local_track_unpublished", "local_track_subscribed", "track_published", "track_unpublished", "track_subscribed", "track_unsubscribed", "track_subscription_failed", "track_muted", "track_unmuted", "active_speakers_changed", "room_metadata_changed", "room_sid_changed", "participant_metadata_changed", "participant_name_changed", "participant_attributes_changed", "connection_quality_changed", "connection_state_changed", "disconnected", "reconnecting", "reconnected", "e2ee_state_changed", "eos", "data_packet_received", "transcription_received", "chat_message", "stream_header_received", "stream_chunk_received", "stream_trailer_received", "data_channel_low_threshold_changed", "byte_stream_opened", "text_stream_opened"] | None: ...

global___RoomEvent = RoomEvent

@typing.final
class RoomInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SID_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    LOSSY_DC_BUFFERED_AMOUNT_LOW_THRESHOLD_FIELD_NUMBER: builtins.int
    RELIABLE_DC_BUFFERED_AMOUNT_LOW_THRESHOLD_FIELD_NUMBER: builtins.int
    sid: builtins.str
    name: builtins.str
    metadata: builtins.str
    lossy_dc_buffered_amount_low_threshold: builtins.int
    reliable_dc_buffered_amount_low_threshold: builtins.int
    def __init__(
        self,
        *,
        sid: builtins.str | None = ...,
        name: builtins.str | None = ...,
        metadata: builtins.str | None = ...,
        lossy_dc_buffered_amount_low_threshold: builtins.int | None = ...,
        reliable_dc_buffered_amount_low_threshold: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lossy_dc_buffered_amount_low_threshold", b"lossy_dc_buffered_amount_low_threshold", "metadata", b"metadata", "name", b"name", "reliable_dc_buffered_amount_low_threshold", b"reliable_dc_buffered_amount_low_threshold", "sid", b"sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lossy_dc_buffered_amount_low_threshold", b"lossy_dc_buffered_amount_low_threshold", "metadata", b"metadata", "name", b"name", "reliable_dc_buffered_amount_low_threshold", b"reliable_dc_buffered_amount_low_threshold", "sid", b"sid"]) -> None: ...

global___RoomInfo = RoomInfo

@typing.final
class OwnedRoom(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HANDLE_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def handle(self) -> handle_pb2.FfiOwnedHandle: ...
    @property
    def info(self) -> global___RoomInfo: ...
    def __init__(
        self,
        *,
        handle: handle_pb2.FfiOwnedHandle | None = ...,
        info: global___RoomInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["handle", b"handle", "info", b"info"]) -> None: ...

global___OwnedRoom = OwnedRoom

@typing.final
class ParticipantConnected(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INFO_FIELD_NUMBER: builtins.int
    @property
    def info(self) -> participant_pb2.OwnedParticipant: ...
    def __init__(
        self,
        *,
        info: participant_pb2.OwnedParticipant | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["info", b"info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["info", b"info"]) -> None: ...

global___ParticipantConnected = ParticipantConnected

@typing.final
class ParticipantDisconnected(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    DISCONNECT_REASON_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    disconnect_reason: participant_pb2.DisconnectReason.ValueType
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        disconnect_reason: participant_pb2.DisconnectReason.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["disconnect_reason", b"disconnect_reason", "participant_identity", b"participant_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["disconnect_reason", b"disconnect_reason", "participant_identity", b"participant_identity"]) -> None: ...

global___ParticipantDisconnected = ParticipantDisconnected

@typing.final
class LocalTrackPublished(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRACK_SID_FIELD_NUMBER: builtins.int
    track_sid: builtins.str
    """The TrackPublicationInfo comes from the PublishTrack response
    and the FfiClient musts wait for it before firing this event
    """
    def __init__(
        self,
        *,
        track_sid: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["track_sid", b"track_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["track_sid", b"track_sid"]) -> None: ...

global___LocalTrackPublished = LocalTrackPublished

@typing.final
class LocalTrackUnpublished(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PUBLICATION_SID_FIELD_NUMBER: builtins.int
    publication_sid: builtins.str
    def __init__(
        self,
        *,
        publication_sid: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["publication_sid", b"publication_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["publication_sid", b"publication_sid"]) -> None: ...

global___LocalTrackUnpublished = LocalTrackUnpublished

@typing.final
class LocalTrackSubscribed(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRACK_SID_FIELD_NUMBER: builtins.int
    track_sid: builtins.str
    def __init__(
        self,
        *,
        track_sid: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["track_sid", b"track_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["track_sid", b"track_sid"]) -> None: ...

global___LocalTrackSubscribed = LocalTrackSubscribed

@typing.final
class TrackPublished(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    PUBLICATION_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    @property
    def publication(self) -> track_pb2.OwnedTrackPublication: ...
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        publication: track_pb2.OwnedTrackPublication | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "publication", b"publication"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "publication", b"publication"]) -> None: ...

global___TrackPublished = TrackPublished

@typing.final
class TrackUnpublished(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    PUBLICATION_SID_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    publication_sid: builtins.str
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        publication_sid: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "publication_sid", b"publication_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "publication_sid", b"publication_sid"]) -> None: ...

global___TrackUnpublished = TrackUnpublished

@typing.final
class TrackSubscribed(google.protobuf.message.Message):
    """Publication isn't needed for subscription events on the FFI
    The FFI will retrieve the publication using the Track sid
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    TRACK_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    @property
    def track(self) -> track_pb2.OwnedTrack: ...
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        track: track_pb2.OwnedTrack | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "track", b"track"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "track", b"track"]) -> None: ...

global___TrackSubscribed = TrackSubscribed

@typing.final
class TrackUnsubscribed(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    TRACK_SID_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    """The FFI language can dispose/remove the VideoSink here"""
    track_sid: builtins.str
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        track_sid: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> None: ...

global___TrackUnsubscribed = TrackUnsubscribed

@typing.final
class TrackSubscriptionFailed(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    TRACK_SID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    track_sid: builtins.str
    error: builtins.str
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        track_sid: builtins.str | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["error", b"error", "participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error", "participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> None: ...

global___TrackSubscriptionFailed = TrackSubscriptionFailed

@typing.final
class TrackMuted(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    TRACK_SID_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    track_sid: builtins.str
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        track_sid: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> None: ...

global___TrackMuted = TrackMuted

@typing.final
class TrackUnmuted(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    TRACK_SID_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    track_sid: builtins.str
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        track_sid: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> None: ...

global___TrackUnmuted = TrackUnmuted

@typing.final
class E2eeStateChanged(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    """Using sid instead of identity for ffi communication"""
    state: e2ee_pb2.EncryptionState.ValueType
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        state: e2ee_pb2.EncryptionState.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "state", b"state"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "state", b"state"]) -> None: ...

global___E2eeStateChanged = E2eeStateChanged

@typing.final
class ActiveSpeakersChanged(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITIES_FIELD_NUMBER: builtins.int
    @property
    def participant_identities(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        participant_identities: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["participant_identities", b"participant_identities"]) -> None: ...

global___ActiveSpeakersChanged = ActiveSpeakersChanged

@typing.final
class RoomMetadataChanged(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    METADATA_FIELD_NUMBER: builtins.int
    metadata: builtins.str
    def __init__(
        self,
        *,
        metadata: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["metadata", b"metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["metadata", b"metadata"]) -> None: ...

global___RoomMetadataChanged = RoomMetadataChanged

@typing.final
class RoomSidChanged(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SID_FIELD_NUMBER: builtins.int
    sid: builtins.str
    def __init__(
        self,
        *,
        sid: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["sid", b"sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["sid", b"sid"]) -> None: ...

global___RoomSidChanged = RoomSidChanged

@typing.final
class ParticipantMetadataChanged(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    metadata: builtins.str
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        metadata: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["metadata", b"metadata", "participant_identity", b"participant_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["metadata", b"metadata", "participant_identity", b"participant_identity"]) -> None: ...

global___ParticipantMetadataChanged = ParticipantMetadataChanged

@typing.final
class ParticipantAttributesChanged(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    CHANGED_ATTRIBUTES_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    @property
    def attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___AttributesEntry]: ...
    @property
    def changed_attributes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___AttributesEntry]: ...
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        attributes: collections.abc.Iterable[global___AttributesEntry] | None = ...,
        changed_attributes: collections.abc.Iterable[global___AttributesEntry] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["attributes", b"attributes", "changed_attributes", b"changed_attributes", "participant_identity", b"participant_identity"]) -> None: ...

global___ParticipantAttributesChanged = ParticipantAttributesChanged

@typing.final
class ParticipantNameChanged(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    name: builtins.str
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        name: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["name", b"name", "participant_identity", b"participant_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["name", b"name", "participant_identity", b"participant_identity"]) -> None: ...

global___ParticipantNameChanged = ParticipantNameChanged

@typing.final
class ConnectionQualityChanged(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    QUALITY_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    quality: global___ConnectionQuality.ValueType
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        quality: global___ConnectionQuality.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "quality", b"quality"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "quality", b"quality"]) -> None: ...

global___ConnectionQualityChanged = ConnectionQualityChanged

@typing.final
class UserPacket(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    TOPIC_FIELD_NUMBER: builtins.int
    topic: builtins.str
    @property
    def data(self) -> global___OwnedBuffer: ...
    def __init__(
        self,
        *,
        data: global___OwnedBuffer | None = ...,
        topic: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data", "topic", b"topic"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data", "topic", b"topic"]) -> None: ...

global___UserPacket = UserPacket

@typing.final
class ChatMessage(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    TIMESTAMP_FIELD_NUMBER: builtins.int
    MESSAGE_FIELD_NUMBER: builtins.int
    EDIT_TIMESTAMP_FIELD_NUMBER: builtins.int
    DELETED_FIELD_NUMBER: builtins.int
    GENERATED_FIELD_NUMBER: builtins.int
    id: builtins.str
    timestamp: builtins.int
    message: builtins.str
    edit_timestamp: builtins.int
    deleted: builtins.bool
    generated: builtins.bool
    def __init__(
        self,
        *,
        id: builtins.str | None = ...,
        timestamp: builtins.int | None = ...,
        message: builtins.str | None = ...,
        edit_timestamp: builtins.int | None = ...,
        deleted: builtins.bool | None = ...,
        generated: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["deleted", b"deleted", "edit_timestamp", b"edit_timestamp", "generated", b"generated", "id", b"id", "message", b"message", "timestamp", b"timestamp"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["deleted", b"deleted", "edit_timestamp", b"edit_timestamp", "generated", b"generated", "id", b"id", "message", b"message", "timestamp", b"timestamp"]) -> None: ...

global___ChatMessage = ChatMessage

@typing.final
class ChatMessageReceived(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_FIELD_NUMBER: builtins.int
    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    @property
    def message(self) -> global___ChatMessage: ...
    def __init__(
        self,
        *,
        message: global___ChatMessage | None = ...,
        participant_identity: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["message", b"message", "participant_identity", b"participant_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["message", b"message", "participant_identity", b"participant_identity"]) -> None: ...

global___ChatMessageReceived = ChatMessageReceived

@typing.final
class SipDTMF(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CODE_FIELD_NUMBER: builtins.int
    DIGIT_FIELD_NUMBER: builtins.int
    code: builtins.int
    digit: builtins.str
    def __init__(
        self,
        *,
        code: builtins.int | None = ...,
        digit: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["code", b"code", "digit", b"digit"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["code", b"code", "digit", b"digit"]) -> None: ...

global___SipDTMF = SipDTMF

@typing.final
class DataPacketReceived(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KIND_FIELD_NUMBER: builtins.int
    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    USER_FIELD_NUMBER: builtins.int
    SIP_DTMF_FIELD_NUMBER: builtins.int
    kind: global___DataPacketKind.ValueType
    participant_identity: builtins.str
    """Can be empty if the data is sent a server SDK"""
    @property
    def user(self) -> global___UserPacket: ...
    @property
    def sip_dtmf(self) -> global___SipDTMF: ...
    def __init__(
        self,
        *,
        kind: global___DataPacketKind.ValueType | None = ...,
        participant_identity: builtins.str | None = ...,
        user: global___UserPacket | None = ...,
        sip_dtmf: global___SipDTMF | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["kind", b"kind", "participant_identity", b"participant_identity", "sip_dtmf", b"sip_dtmf", "user", b"user", "value", b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["kind", b"kind", "participant_identity", b"participant_identity", "sip_dtmf", b"sip_dtmf", "user", b"user", "value", b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["value", b"value"]) -> typing.Literal["user", "sip_dtmf"] | None: ...

global___DataPacketReceived = DataPacketReceived

@typing.final
class TranscriptionReceived(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    TRACK_SID_FIELD_NUMBER: builtins.int
    SEGMENTS_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    track_sid: builtins.str
    @property
    def segments(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___TranscriptionSegment]: ...
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        track_sid: builtins.str | None = ...,
        segments: collections.abc.Iterable[global___TranscriptionSegment] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "track_sid", b"track_sid"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "segments", b"segments", "track_sid", b"track_sid"]) -> None: ...

global___TranscriptionReceived = TranscriptionReceived

@typing.final
class ConnectionStateChanged(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STATE_FIELD_NUMBER: builtins.int
    state: global___ConnectionState.ValueType
    def __init__(
        self,
        *,
        state: global___ConnectionState.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["state", b"state"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["state", b"state"]) -> None: ...

global___ConnectionStateChanged = ConnectionStateChanged

@typing.final
class Connected(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___Connected = Connected

@typing.final
class Disconnected(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    REASON_FIELD_NUMBER: builtins.int
    reason: participant_pb2.DisconnectReason.ValueType
    def __init__(
        self,
        *,
        reason: participant_pb2.DisconnectReason.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["reason", b"reason"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["reason", b"reason"]) -> None: ...

global___Disconnected = Disconnected

@typing.final
class Reconnecting(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___Reconnecting = Reconnecting

@typing.final
class Reconnected(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___Reconnected = Reconnected

@typing.final
class RoomEOS(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___RoomEOS = RoomEOS

@typing.final
class DataStream(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _OperationType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _OperationTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[DataStream._OperationType.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        CREATE: DataStream._OperationType.ValueType  # 0
        UPDATE: DataStream._OperationType.ValueType  # 1
        DELETE: DataStream._OperationType.ValueType  # 2
        REACTION: DataStream._OperationType.ValueType  # 3

    class OperationType(_OperationType, metaclass=_OperationTypeEnumTypeWrapper):
        """enum for operation types (specific to TextHeader)"""

    CREATE: DataStream.OperationType.ValueType  # 0
    UPDATE: DataStream.OperationType.ValueType  # 1
    DELETE: DataStream.OperationType.ValueType  # 2
    REACTION: DataStream.OperationType.ValueType  # 3

    @typing.final
    class TextHeader(google.protobuf.message.Message):
        """header properties specific to text streams"""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        OPERATION_TYPE_FIELD_NUMBER: builtins.int
        VERSION_FIELD_NUMBER: builtins.int
        REPLY_TO_STREAM_ID_FIELD_NUMBER: builtins.int
        ATTACHED_STREAM_IDS_FIELD_NUMBER: builtins.int
        GENERATED_FIELD_NUMBER: builtins.int
        operation_type: global___DataStream.OperationType.ValueType
        version: builtins.int
        """Optional: Version for updates/edits"""
        reply_to_stream_id: builtins.str
        """Optional: Reply to specific message"""
        generated: builtins.bool
        """true if the text has been generated by an agent from a participant's audio transcription"""
        @property
        def attached_stream_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
            """file attachments for text streams"""

        def __init__(
            self,
            *,
            operation_type: global___DataStream.OperationType.ValueType | None = ...,
            version: builtins.int | None = ...,
            reply_to_stream_id: builtins.str | None = ...,
            attached_stream_ids: collections.abc.Iterable[builtins.str] | None = ...,
            generated: builtins.bool | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["generated", b"generated", "operation_type", b"operation_type", "reply_to_stream_id", b"reply_to_stream_id", "version", b"version"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["attached_stream_ids", b"attached_stream_ids", "generated", b"generated", "operation_type", b"operation_type", "reply_to_stream_id", b"reply_to_stream_id", "version", b"version"]) -> None: ...

    @typing.final
    class ByteHeader(google.protobuf.message.Message):
        """header properties specific to byte or file streams"""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        NAME_FIELD_NUMBER: builtins.int
        name: builtins.str
        def __init__(
            self,
            *,
            name: builtins.str | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["name", b"name"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["name", b"name"]) -> None: ...

    @typing.final
    class Header(google.protobuf.message.Message):
        """main DataStream.Header that contains a oneof for specific headers"""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        @typing.final
        class AttributesEntry(google.protobuf.message.Message):
            DESCRIPTOR: google.protobuf.descriptor.Descriptor

            KEY_FIELD_NUMBER: builtins.int
            VALUE_FIELD_NUMBER: builtins.int
            key: builtins.str
            value: builtins.str
            def __init__(
                self,
                *,
                key: builtins.str | None = ...,
                value: builtins.str | None = ...,
            ) -> None: ...
            def HasField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> builtins.bool: ...
            def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

        STREAM_ID_FIELD_NUMBER: builtins.int
        TIMESTAMP_FIELD_NUMBER: builtins.int
        MIME_TYPE_FIELD_NUMBER: builtins.int
        TOPIC_FIELD_NUMBER: builtins.int
        TOTAL_LENGTH_FIELD_NUMBER: builtins.int
        ATTRIBUTES_FIELD_NUMBER: builtins.int
        TEXT_HEADER_FIELD_NUMBER: builtins.int
        BYTE_HEADER_FIELD_NUMBER: builtins.int
        stream_id: builtins.str
        """unique identifier for this data stream"""
        timestamp: builtins.int
        """using int64 for Unix timestamp"""
        mime_type: builtins.str
        topic: builtins.str
        total_length: builtins.int
        """only populated for finite streams, if it's a stream of unknown size this stays empty"""
        @property
        def attributes(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
            """user defined attributes map that can carry additional info"""

        @property
        def text_header(self) -> global___DataStream.TextHeader: ...
        @property
        def byte_header(self) -> global___DataStream.ByteHeader: ...
        def __init__(
            self,
            *,
            stream_id: builtins.str | None = ...,
            timestamp: builtins.int | None = ...,
            mime_type: builtins.str | None = ...,
            topic: builtins.str | None = ...,
            total_length: builtins.int | None = ...,
            attributes: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
            text_header: global___DataStream.TextHeader | None = ...,
            byte_header: global___DataStream.ByteHeader | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["byte_header", b"byte_header", "content_header", b"content_header", "mime_type", b"mime_type", "stream_id", b"stream_id", "text_header", b"text_header", "timestamp", b"timestamp", "topic", b"topic", "total_length", b"total_length"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["attributes", b"attributes", "byte_header", b"byte_header", "content_header", b"content_header", "mime_type", b"mime_type", "stream_id", b"stream_id", "text_header", b"text_header", "timestamp", b"timestamp", "topic", b"topic", "total_length", b"total_length"]) -> None: ...
        def WhichOneof(self, oneof_group: typing.Literal["content_header", b"content_header"]) -> typing.Literal["text_header", "byte_header"] | None: ...

    @typing.final
    class Chunk(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        STREAM_ID_FIELD_NUMBER: builtins.int
        CHUNK_INDEX_FIELD_NUMBER: builtins.int
        CONTENT_FIELD_NUMBER: builtins.int
        VERSION_FIELD_NUMBER: builtins.int
        IV_FIELD_NUMBER: builtins.int
        stream_id: builtins.str
        """unique identifier for this data stream to map it to the correct header"""
        chunk_index: builtins.int
        content: builtins.bytes
        """content as binary (bytes)"""
        version: builtins.int
        """a version indicating that this chunk_index has been retroactively modified and the original one needs to be replaced"""
        iv: builtins.bytes
        """optional, initialization vector for AES-GCM encryption"""
        def __init__(
            self,
            *,
            stream_id: builtins.str | None = ...,
            chunk_index: builtins.int | None = ...,
            content: builtins.bytes | None = ...,
            version: builtins.int | None = ...,
            iv: builtins.bytes | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["chunk_index", b"chunk_index", "content", b"content", "iv", b"iv", "stream_id", b"stream_id", "version", b"version"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["chunk_index", b"chunk_index", "content", b"content", "iv", b"iv", "stream_id", b"stream_id", "version", b"version"]) -> None: ...

    @typing.final
    class Trailer(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        @typing.final
        class AttributesEntry(google.protobuf.message.Message):
            DESCRIPTOR: google.protobuf.descriptor.Descriptor

            KEY_FIELD_NUMBER: builtins.int
            VALUE_FIELD_NUMBER: builtins.int
            key: builtins.str
            value: builtins.str
            def __init__(
                self,
                *,
                key: builtins.str | None = ...,
                value: builtins.str | None = ...,
            ) -> None: ...
            def HasField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> builtins.bool: ...
            def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

        STREAM_ID_FIELD_NUMBER: builtins.int
        REASON_FIELD_NUMBER: builtins.int
        ATTRIBUTES_FIELD_NUMBER: builtins.int
        stream_id: builtins.str
        """unique identifier for this data stream"""
        reason: builtins.str
        """reason why the stream was closed (could contain "error" / "interrupted" / empty for expected end)"""
        @property
        def attributes(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
            """finalizing updates for the stream, can also include additional insights for errors or endTime for transcription"""

        def __init__(
            self,
            *,
            stream_id: builtins.str | None = ...,
            reason: builtins.str | None = ...,
            attributes: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["reason", b"reason", "stream_id", b"stream_id"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["attributes", b"attributes", "reason", b"reason", "stream_id", b"stream_id"]) -> None: ...

    def __init__(
        self,
    ) -> None: ...

global___DataStream = DataStream

@typing.final
class DataStreamHeaderReceived(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    @property
    def header(self) -> global___DataStream.Header: ...
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        header: global___DataStream.Header | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["header", b"header", "participant_identity", b"participant_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["header", b"header", "participant_identity", b"participant_identity"]) -> None: ...

global___DataStreamHeaderReceived = DataStreamHeaderReceived

@typing.final
class DataStreamChunkReceived(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    CHUNK_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    @property
    def chunk(self) -> global___DataStream.Chunk: ...
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        chunk: global___DataStream.Chunk | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["chunk", b"chunk", "participant_identity", b"participant_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["chunk", b"chunk", "participant_identity", b"participant_identity"]) -> None: ...

global___DataStreamChunkReceived = DataStreamChunkReceived

@typing.final
class DataStreamTrailerReceived(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    TRAILER_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    @property
    def trailer(self) -> global___DataStream.Trailer: ...
    def __init__(
        self,
        *,
        participant_identity: builtins.str | None = ...,
        trailer: global___DataStream.Trailer | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "trailer", b"trailer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "trailer", b"trailer"]) -> None: ...

global___DataStreamTrailerReceived = DataStreamTrailerReceived

@typing.final
class SendStreamHeaderRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    DESTINATION_IDENTITIES_FIELD_NUMBER: builtins.int
    SENDER_IDENTITY_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    sender_identity: builtins.str
    @property
    def header(self) -> global___DataStream.Header: ...
    @property
    def destination_identities(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        header: global___DataStream.Header | None = ...,
        destination_identities: collections.abc.Iterable[builtins.str] | None = ...,
        sender_identity: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["header", b"header", "local_participant_handle", b"local_participant_handle", "sender_identity", b"sender_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["destination_identities", b"destination_identities", "header", b"header", "local_participant_handle", b"local_participant_handle", "sender_identity", b"sender_identity"]) -> None: ...

global___SendStreamHeaderRequest = SendStreamHeaderRequest

@typing.final
class SendStreamChunkRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    CHUNK_FIELD_NUMBER: builtins.int
    DESTINATION_IDENTITIES_FIELD_NUMBER: builtins.int
    SENDER_IDENTITY_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    sender_identity: builtins.str
    @property
    def chunk(self) -> global___DataStream.Chunk: ...
    @property
    def destination_identities(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        chunk: global___DataStream.Chunk | None = ...,
        destination_identities: collections.abc.Iterable[builtins.str] | None = ...,
        sender_identity: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["chunk", b"chunk", "local_participant_handle", b"local_participant_handle", "sender_identity", b"sender_identity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["chunk", b"chunk", "destination_identities", b"destination_identities", "local_participant_handle", b"local_participant_handle", "sender_identity", b"sender_identity"]) -> None: ...

global___SendStreamChunkRequest = SendStreamChunkRequest

@typing.final
class SendStreamTrailerRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    TRAILER_FIELD_NUMBER: builtins.int
    DESTINATION_IDENTITIES_FIELD_NUMBER: builtins.int
    SENDER_IDENTITY_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    sender_identity: builtins.str
    @property
    def trailer(self) -> global___DataStream.Trailer: ...
    @property
    def destination_identities(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        trailer: global___DataStream.Trailer | None = ...,
        destination_identities: collections.abc.Iterable[builtins.str] | None = ...,
        sender_identity: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["local_participant_handle", b"local_participant_handle", "sender_identity", b"sender_identity", "trailer", b"trailer"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["destination_identities", b"destination_identities", "local_participant_handle", b"local_participant_handle", "sender_identity", b"sender_identity", "trailer", b"trailer"]) -> None: ...

global___SendStreamTrailerRequest = SendStreamTrailerRequest

@typing.final
class SendStreamHeaderResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___SendStreamHeaderResponse = SendStreamHeaderResponse

@typing.final
class SendStreamChunkResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___SendStreamChunkResponse = SendStreamChunkResponse

@typing.final
class SendStreamTrailerResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id"]) -> None: ...

global___SendStreamTrailerResponse = SendStreamTrailerResponse

@typing.final
class SendStreamHeaderCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___SendStreamHeaderCallback = SendStreamHeaderCallback

@typing.final
class SendStreamChunkCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___SendStreamChunkCallback = SendStreamChunkCallback

@typing.final
class SendStreamTrailerCallback(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_ID_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    async_id: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        async_id: builtins.int | None = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["async_id", b"async_id", "error", b"error"]) -> None: ...

global___SendStreamTrailerCallback = SendStreamTrailerCallback

@typing.final
class SetDataChannelBufferedAmountLowThresholdRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_PARTICIPANT_HANDLE_FIELD_NUMBER: builtins.int
    THRESHOLD_FIELD_NUMBER: builtins.int
    KIND_FIELD_NUMBER: builtins.int
    local_participant_handle: builtins.int
    threshold: builtins.int
    kind: global___DataPacketKind.ValueType
    def __init__(
        self,
        *,
        local_participant_handle: builtins.int | None = ...,
        threshold: builtins.int | None = ...,
        kind: global___DataPacketKind.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["kind", b"kind", "local_participant_handle", b"local_participant_handle", "threshold", b"threshold"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["kind", b"kind", "local_participant_handle", b"local_participant_handle", "threshold", b"threshold"]) -> None: ...

global___SetDataChannelBufferedAmountLowThresholdRequest = SetDataChannelBufferedAmountLowThresholdRequest

@typing.final
class SetDataChannelBufferedAmountLowThresholdResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___SetDataChannelBufferedAmountLowThresholdResponse = SetDataChannelBufferedAmountLowThresholdResponse

@typing.final
class DataChannelBufferedAmountLowThresholdChanged(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KIND_FIELD_NUMBER: builtins.int
    THRESHOLD_FIELD_NUMBER: builtins.int
    kind: global___DataPacketKind.ValueType
    threshold: builtins.int
    def __init__(
        self,
        *,
        kind: global___DataPacketKind.ValueType | None = ...,
        threshold: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["kind", b"kind", "threshold", b"threshold"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["kind", b"kind", "threshold", b"threshold"]) -> None: ...

global___DataChannelBufferedAmountLowThresholdChanged = DataChannelBufferedAmountLowThresholdChanged

@typing.final
class ByteStreamOpened(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    READER_FIELD_NUMBER: builtins.int
    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    @property
    def reader(self) -> data_stream_pb2.OwnedByteStreamReader: ...
    def __init__(
        self,
        *,
        reader: data_stream_pb2.OwnedByteStreamReader | None = ...,
        participant_identity: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "reader", b"reader"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "reader", b"reader"]) -> None: ...

global___ByteStreamOpened = ByteStreamOpened

@typing.final
class TextStreamOpened(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    READER_FIELD_NUMBER: builtins.int
    PARTICIPANT_IDENTITY_FIELD_NUMBER: builtins.int
    participant_identity: builtins.str
    @property
    def reader(self) -> data_stream_pb2.OwnedTextStreamReader: ...
    def __init__(
        self,
        *,
        reader: data_stream_pb2.OwnedTextStreamReader | None = ...,
        participant_identity: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "reader", b"reader"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["participant_identity", b"participant_identity", "reader", b"reader"]) -> None: ...

global___TextStreamOpened = TextStreamOpened
