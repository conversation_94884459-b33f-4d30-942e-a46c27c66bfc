"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2023 LiveKit, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class EnableRemoteTrackPublicationRequest(google.protobuf.message.Message):
    """Enable/Disable a remote track publication"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRACK_PUBLICATION_HANDLE_FIELD_NUMBER: builtins.int
    ENABLED_FIELD_NUMBER: builtins.int
    track_publication_handle: builtins.int
    enabled: builtins.bool
    def __init__(
        self,
        *,
        track_publication_handle: builtins.int | None = ...,
        enabled: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["enabled", b"enabled", "track_publication_handle", b"track_publication_handle"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["enabled", b"enabled", "track_publication_handle", b"track_publication_handle"]) -> None: ...

global___EnableRemoteTrackPublicationRequest = EnableRemoteTrackPublicationRequest

@typing.final
class EnableRemoteTrackPublicationResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___EnableRemoteTrackPublicationResponse = EnableRemoteTrackPublicationResponse

@typing.final
class UpdateRemoteTrackPublicationDimensionRequest(google.protobuf.message.Message):
    """update a remote track publication dimension"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRACK_PUBLICATION_HANDLE_FIELD_NUMBER: builtins.int
    WIDTH_FIELD_NUMBER: builtins.int
    HEIGHT_FIELD_NUMBER: builtins.int
    track_publication_handle: builtins.int
    width: builtins.int
    height: builtins.int
    def __init__(
        self,
        *,
        track_publication_handle: builtins.int | None = ...,
        width: builtins.int | None = ...,
        height: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["height", b"height", "track_publication_handle", b"track_publication_handle", "width", b"width"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["height", b"height", "track_publication_handle", b"track_publication_handle", "width", b"width"]) -> None: ...

global___UpdateRemoteTrackPublicationDimensionRequest = UpdateRemoteTrackPublicationDimensionRequest

@typing.final
class UpdateRemoteTrackPublicationDimensionResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___UpdateRemoteTrackPublicationDimensionResponse = UpdateRemoteTrackPublicationDimensionResponse
