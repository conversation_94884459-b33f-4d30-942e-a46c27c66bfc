# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: track_publication.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17track_publication.proto\x12\rlivekit.proto\"X\n#EnableRemoteTrackPublicationRequest\x12 \n\x18track_publication_handle\x18\x01 \x02(\x04\x12\x0f\n\x07\x65nabled\x18\x02 \x02(\x08\"&\n$EnableRemoteTrackPublicationResponse\"o\n,UpdateRemoteTrackPublicationDimensionRequest\x12 \n\x18track_publication_handle\x18\x01 \x02(\x04\x12\r\n\x05width\x18\x02 \x02(\r\x12\x0e\n\x06height\x18\x03 \x02(\r\"/\n-UpdateRemoteTrackPublicationDimensionResponseB\x10\xaa\x02\rLiveKit.Proto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'track_publication_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\252\002\rLiveKit.Proto'
  _globals['_ENABLEREMOTETRACKPUBLICATIONREQUEST']._serialized_start=42
  _globals['_ENABLEREMOTETRACKPUBLICATIONREQUEST']._serialized_end=130
  _globals['_ENABLEREMOTETRACKPUBLICATIONRESPONSE']._serialized_start=132
  _globals['_ENABLEREMOTETRACKPUBLICATIONRESPONSE']._serialized_end=170
  _globals['_UPDATEREMOTETRACKPUBLICATIONDIMENSIONREQUEST']._serialized_start=172
  _globals['_UPDATEREMOTETRACKPUBLICATIONDIMENSIONREQUEST']._serialized_end=283
  _globals['_UPDATEREMOTETRACKPUBLICATIONDIMENSIONRESPONSE']._serialized_start=285
  _globals['_UPDATEREMOTETRACKPUBLICATIONDIMENSIONRESPONSE']._serialized_end=332
# @@protoc_insertion_point(module_scope)
